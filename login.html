<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Connexion - Système de gestion Permis de Travail</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"/>
  <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet"/>
  <style>
    :root {
      --primary: #3b82f6; 
      --primary-dark: #2563eb;
      --primary-light: #93c5fd;
      --secondary: #4f46e5;
      --accent: #3b82f6;
      --success: #10b981;
      --warning: #f59e0b;
      --danger: #ef4444;
      --info: #06b6d4;
      --gray-50: #f9fafb;
      --gray-100: #f3f4f6;
      --gray-200: #e5e7eb;
      --gray-300: #d1d5db;
      --gray-400: #9ca3af;
      --gray-500: #6b7280;
      --gray-600: #4b5563;
      --gray-700: #374151;
      --gray-800: #1f2937;
      --gray-900: #111827;
      --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      --radius-sm: 6px;
      --radius-md: 8px;
      --radius-lg: 12px;
      --radius-xl: 16px;
      --transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Reset */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background-color: #f1f5f9;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    .login-container {
      max-width: 450px;
      width: 100%;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      position: relative;
      border: 1px solid var(--gray-200);
      display: flex;
      flex-direction: column;
    }

    .login-header {
      padding: 24px 20px;
      text-align: center;
      background-color: var(--primary);
      background: linear-gradient(135deg, var(--primary), var(--primary-dark));
      position: relative;
      color: white;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .login-header img {
      width: 75px;
      height: auto;
      margin-bottom: 16px;
      filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.2));
    }

    .login-header h1 {
      font-size: 1.4rem;
      font-weight: 700;
      margin-bottom: 6px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .login-header p {
      font-size: 0.9rem;
      margin-top: 6px;
      max-width: 280px;
      margin-left: auto;
      margin-right: auto;
      opacity: 0.9;
      line-height: 1.4;
    }

    .login-form {
      padding: 20px 20px;
      display: flex;
      flex-direction: column;
      gap: 18px;
      background-color: white;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .form-group label {
      font-size: 0.8rem;
      font-weight: 600;
      color: var(--gray-700);
      display: block;
      margin-bottom: 5px;
      letter-spacing: 0.2px;
    }

    .input-with-icon {
      position: relative;
      display: flex;
      align-items: center;
    }

    .input-with-icon input {
      width: 100%;
      padding: 10px 12px 10px 36px;
      border: 1px solid var(--gray-300);
      border-radius: 6px;
      font-size: 0.85rem;
      outline: none;
      transition: all 0.2s ease;
      background-color: #f9fafb;
    }

    .input-with-icon input:focus {
      border-color: var(--primary);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15);
      background-color: white;
    }

    .input-with-icon i {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 18px;
      color: var(--gray-500);
      transition: var(--transition);
    }

    .input-with-icon input:focus + i {
      color: var(--primary);
    }

    .form-options {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.875rem;
      margin-top: -8px;
    }

    .remember-me {
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--gray-600);
    }

    .remember-me input[type="checkbox"] {
      width: 18px;
      height: 18px;
      accent-color: var(--primary);
      cursor: pointer;
    }

    .forgot-password {
      color: var(--primary);
      text-decoration: none;
      font-weight: 500;
      transition: var(--transition);
      font-size: 0.8rem;
    }

    .forgot-password:hover {
      color: var(--primary-dark);
      text-decoration: underline;
    }

    .login-button {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      background-color: var(--primary);
      background: linear-gradient(to right, var(--primary), var(--primary-dark));
      color: white;
      border: none;
      padding: 10px 16px;
      font-size: 0.85rem;
      font-weight: 600;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-top: 6px;
      width: 100%;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .login-button:hover {
      background: linear-gradient(to right, var(--primary-dark), var(--secondary));
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    }

    .login-button:active {
      transform: translateY(1px);
    }

    .login-button i {
      font-size: 16px;
    }

    .login-footer {
      padding: 12px;
      font-size: 0.7rem;
      text-align: center;
      color: var(--gray-500);
      border-top: 1px solid var(--gray-200);
      background-color: #f8fafc;
    }



    /* Error message styling */
    .error-message {
      color: var(--danger);
      background-color: rgba(239, 68, 68, 0.05);
      border: 1px solid rgba(239, 68, 68, 0.2);
      border-radius: 6px;
      padding: 12px 15px;
      font-size: 0.875rem;
      text-align: left;
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;
      display: none;
    }

    .error-message i {
      font-size: 20px;
      flex-shrink: 0;
    }
    
    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
      20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
    
    .shake {
      animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
    }

    /* Loading animation */
    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    
    /* Modal styles */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(3px);
    }
    
    .modal-content {
      background-color: white;
      margin: 5% auto;
      padding: 20px;
      width: 90%;
      max-width: 360px;
      border-radius: 8px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      position: relative;
      border: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .close-modal {
      position: absolute;
      top: 15px;
      right: 20px;
      font-size: 24px;
      font-weight: bold;
      color: var(--gray-500);
      cursor: pointer;
      transition: color 0.2s;
    }
    
    .close-modal:hover {
      color: var(--gray-700);
    }
    
    .modal-content h2 {
      margin: 0 0 16px 0;
      color: var(--gray-800);
      font-size: 1.1rem;
      text-align: center;
    }
    
    .reset-step {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    
    .reset-step p {
      margin: 0 0 8px 0;
      color: var(--gray-600);
      font-size: 0.8rem;
      text-align: center;
    }
    
    /* Success message style */
    .success-message {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      background-color: rgba(16, 185, 129, 0.1);
      border: 1px solid rgba(16, 185, 129, 0.2);
      padding: 15px;
      border-radius: 6px;
      margin-bottom: 10px;
    }
    
    .success-message i {
      font-size: 24px;
      color: var(--success);
    }
    
    .success-message p {
      color: var(--gray-800);
      margin: 0;
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
      .login-container {
        max-width: 100%;
        border-radius: var(--radius-md);
      }
      
      .login-header {
        padding: 24px 16px;
      }
      
      .login-form {
        padding: 24px 16px;
      }
    }
  </style>
</head>
<body>
  <div class="login-container">
    <!-- Header -->
    <div class="login-header">
      <img src="sonatrach-logo.png" alt="Logo Sonatrach"/>
      <h1>Système de gestion Permis de Travail</h1>
      <p>Veuillez vous connecter pour accéder à votre compte</p>
    </div>

    <!-- Form -->
    <form class="login-form" id="loginForm" onsubmit="handleLogin(event)">
      <!-- Error message -->
      <div class="error-message" id="login-error">
        <i class='bx bx-shield-x'></i>
        <span>Message d'erreur ici</span>
      </div>

      <!-- Username -->
      <div class="form-group">
        <label for="username">Nom d'utilisateur</label>
        <div class="input-with-icon">
          <input type="text" id="username" placeholder="Entrez votre nom d'utilisateur" autocomplete="username" required />
          <i class='bx bx-user'></i>
        </div>
      </div>

      <!-- Password -->
      <div class="form-group">
        <label for="password">Mot de passe</label>
        <div class="input-with-icon">
          <input type="password" id="password" placeholder="Entrez votre mot de passe" autocomplete="current-password" required />
          <i class='bx bx-lock-alt'></i>
        </div>
      </div>

      <!-- Forgot password -->
      <div class="form-options">
        <div></div> <!-- Empty div for spacing -->
        <a href="#" class="forgot-password" onclick="forgotPassword(event)">Mot de passe oublié ?</a>
      </div>

      <!-- Submit Button -->
      <button type="submit" class="login-button" id="loginButton">
        Se connecter
        <i class='bx bx-right-arrow-alt'></i>
      </button>
    </form>

    <!-- Footer -->
    <div class="login-footer">
      © 2025 Sonatrach. Tous droits réservés.
    </div>
  </div>

  <!-- Password Reset Modal -->
  <div id="resetPasswordModal" class="modal">
    <div class="modal-content">
      <span class="close-modal">&times;</span>
      <h2>Réinitialisation du mot de passe</h2>
      
      <!-- Step 1: Email Input -->
      <div id="step1" class="reset-step">
        <p>Entrez votre adresse e-mail pour recevoir un code de réinitialisation</p>
        <div class="form-group">
          <label for="email">Adresse e-mail</label>
          <div class="input-with-icon">
            <input type="email" id="email" placeholder="Entrez votre adresse e-mail" required />
            <i class='bx bx-envelope'></i>
          </div>
        </div>
        <button type="button" id="sendCodeBtn" class="login-button" onclick="sendResetCode()">
          Envoyer le code
          <i class='bx bx-send'></i>
        </button>
      </div>
      
      <!-- Step 2: Code Verification -->
      <div id="step2" class="reset-step" style="display:none">
        <p>Un code a été envoyé à votre adresse e-mail</p>
        <div class="form-group">
          <label for="code">Code de vérification</label>
          <div class="input-with-icon">
            <input type="text" id="code" placeholder="Entrez le code reçu" required />
            <i class='bx bx-check-shield'></i>
          </div>
        </div>
        <button type="button" id="verifyCodeBtn" class="login-button" onclick="verifyCode()">
          Vérifier le code
          <i class='bx bx-check'></i>
        </button>
      </div>
      
      <!-- Step 3: New Password -->
      <div id="step3" class="reset-step" style="display:none">
        <p>Créez un nouveau mot de passe</p>
        <div class="form-group">
          <label for="newPassword">Nouveau mot de passe</label>
          <div class="input-with-icon">
            <input type="password" id="newPassword" placeholder="Entrez votre nouveau mot de passe" required />
            <i class='bx bx-lock-alt'></i>
          </div>
        </div>
        <div class="form-group">
          <label for="confirmPassword">Confirmer le mot de passe</label>
          <div class="input-with-icon">
            <input type="password" id="confirmPassword" placeholder="Confirmez votre nouveau mot de passe" required />
            <i class='bx bx-lock-alt'></i>
          </div>
        </div>
        <button type="button" id="resetPasswordBtn" class="login-button" onclick="resetPassword()">
          Réinitialiser le mot de passe
          <i class='bx bx-reset'></i>
        </button>
      </div>
      
      <!-- Success Message -->
      <div id="successMessage" class="reset-step" style="display:none">
        <div class="success-message">
          <i class='bx bx-check-circle'></i>
          <p>Votre mot de passe a été réinitialisé avec succès.</p>
        </div>
        <button type="button" class="login-button" onclick="closeModal()">
          Retour à la connexion
          <i class='bx bx-log-in'></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Internal JS -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Focus on username field when page loads
      document.getElementById('username').focus();
      
      // Close modal when clicking on the X or outside the modal
      document.querySelector('.close-modal').addEventListener('click', closeModal);
      window.addEventListener('click', function(event) {
        const modal = document.getElementById('resetPasswordModal');
        if (event.target === modal) {
          closeModal();
        }
      });
    });
    
    function handleLogin(e) {
      e.preventDefault();
      
      const loginButton = document.getElementById('loginButton');
      const username = document.getElementById('username').value.trim();
      const password = document.getElementById('password').value.trim();

      if (!username || !password) {
        showError("Veuillez remplir tous les champs.");
        return;
      }
      
      // Hide any previous error
      hideError();
      
      // Disable button and show loading state
      loginButton.disabled = true;
      loginButton.innerHTML = '<div class="loading-spinner"></div> Connexion en cours...';
      
      // Simulate API call with timeout
      setTimeout(() => {
        // Check credentials (demo only - replace with real authentication)
        checkCredentials(username, password);
      }, 1000);
    }
    
    function checkCredentials(username, password) {
      // Demo credential check - replace with actual authentication logic
      if (username === 'admin' && password === 'admin') {
        redirectTo('administrateur.html');
      } else if (username === 'initiateur' && password === 'initiateur') {
        redirectTo('initiateur.html');
      } else if (username === 'hse' && password === 'hse') {
        redirectTo('responsablehse.html');
      } else if (username === 'az' && password === 'az') {
        redirectTo('autoritezone.html');
      } else {
        showError("Nom d'utilisateur ou mot de passe incorrect.");
        resetLoginButton();
      }
    }
    
    function redirectTo(page) {
      window.location.href = page;
    }
    
    function showError(message) {
      const errorEl = document.getElementById('login-error');
      errorEl.querySelector('span').textContent = message;
      errorEl.style.display = 'flex';
      
      // Highlight input fields with error
      document.querySelectorAll('input[type="text"], input[type="password"]').forEach(input => {
        if (!input.value.trim()) {
          input.style.borderColor = 'var(--danger)';
          
          input.addEventListener('input', function removeErrorStyle() {
            input.style.borderColor = '';
            input.removeEventListener('input', removeErrorStyle);
          });
        }
      });
      
      resetLoginButton();
    }
    
    function hideError() {
      const errorEl = document.getElementById('login-error');
      errorEl.style.display = 'none';
    }
    
    function resetLoginButton() {
      const loginButton = document.getElementById('loginButton');
      loginButton.disabled = false;
      loginButton.innerHTML = 'Se connecter <i class="bx bx-right-arrow-alt"></i>';
    }
    
    // Password Reset Functions
    function forgotPassword(e) {
      e.preventDefault();
      document.getElementById('resetPasswordModal').style.display = 'block';
      document.getElementById('step1').style.display = 'block';
      document.getElementById('step2').style.display = 'none';
      document.getElementById('step3').style.display = 'none';
      document.getElementById('successMessage').style.display = 'none';
      document.getElementById('email').focus();
    }
    
    function closeModal() {
      document.getElementById('resetPasswordModal').style.display = 'none';
    }
    
    function sendResetCode() {
      const email = document.getElementById('email').value.trim();
      if (!email) {
        alert("Veuillez entrer votre adresse e-mail.");
        return;
      }
      
      if (!isValidEmail(email)) {
        alert("Veuillez entrer une adresse e-mail valide.");
        return;
      }
      
      const sendCodeBtn = document.getElementById('sendCodeBtn');
      sendCodeBtn.disabled = true;
      sendCodeBtn.innerHTML = '<div class="loading-spinner"></div> Envoi en cours...';
      
      // Simulate API call with timeout
      setTimeout(() => {
        document.getElementById('step1').style.display = 'none';
        document.getElementById('step2').style.display = 'block';
        document.getElementById('code').focus();
        
        // For demo purposes only - in a real app, this would be sent to the server
        localStorage.setItem('resetEmail', email);
        localStorage.setItem('resetCode', '123456'); // In a real app, this would be generated on the server
      }, 1500);
    }
    
    function verifyCode() {
      const code = document.getElementById('code').value.trim();
      if (!code) {
        alert("Veuillez entrer le code reçu par e-mail.");
        return;
      }
      
      const storedCode = localStorage.getItem('resetCode');
      
      const verifyCodeBtn = document.getElementById('verifyCodeBtn');
      verifyCodeBtn.disabled = true;
      verifyCodeBtn.innerHTML = '<div class="loading-spinner"></div> Vérification...';
      
      // Simulate API call with timeout
      setTimeout(() => {
        if (code === storedCode) { // In a real app, this would be verified on the server
          document.getElementById('step2').style.display = 'none';
          document.getElementById('step3').style.display = 'block';
          document.getElementById('newPassword').focus();
        } else {
          alert("Code incorrect. Veuillez vérifier et réessayer.");
          verifyCodeBtn.disabled = false;
          verifyCodeBtn.innerHTML = 'Vérifier le code <i class="bx bx-check"></i>';
        }
      }, 1500);
    }
    
    function resetPassword() {
      const newPassword = document.getElementById('newPassword').value.trim();
      const confirmPassword = document.getElementById('confirmPassword').value.trim();
      
      if (!newPassword || !confirmPassword) {
        alert("Veuillez remplir tous les champs.");
        return;
      }
      
      if (newPassword !== confirmPassword) {
        alert("Les mots de passe ne correspondent pas.");
        return;
      }
      
      if (newPassword.length < 6) {
        alert("Le mot de passe doit contenir au moins 6 caractères.");
        return;
      }
      
      const resetPasswordBtn = document.getElementById('resetPasswordBtn');
      resetPasswordBtn.disabled = true;
      resetPasswordBtn.innerHTML = '<div class="loading-spinner"></div> Réinitialisation...';
      
      // Simulate API call with timeout
      setTimeout(() => {
        // For demo purposes only - in a real app, this would be saved on the server
        document.getElementById('step3').style.display = 'none';
        document.getElementById('successMessage').style.display = 'block';
        
        // Clean up stored values
        localStorage.removeItem('resetEmail');
        localStorage.removeItem('resetCode');
      }, 1500);
    }
    
    function isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    }
  </script>

</body>
</html>