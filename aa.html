<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Archives des Permis – Responsable HSE</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
  <style>
    body {
      background-color: #f8f9fa;
      padding: 30px;
    }
    .filter-section {
      background-color: #ffffff;
      padding: 20px;
      border-radius: 6px;
      box-shadow: 0 0 8px rgba(0,0,0,0.05);
      margin-bottom: 20px;
    }
    .table-responsive {
      box-shadow: 0 0 8px rgba(0,0,0,0.05);
    }
    .table th {
      background-color: #007BFF;
      color: white;
    }
  </style>
</head>
<body>

  <h3 class="mb-4">📁 Archives des Permis de Travail</h3>

  <!-- Filtres -->
  <div class="filter-section">
    <form class="row g-3">
      <div class="col-md-3">
        <label class="form-label">Type de Permis</label>
        <select class="form-select" id="filterType">
          <option value="">Tous</option>
          <option>Travail à froid</option>
          <option>Travail à chaud</option>
          <option>Espace confiné</option>
        </select>
      </div>
      <div class="col-md-3">
        <label class="form-label">Statut Final</label>
        <select class="form-select" id="filterStatus">
          <option value="">Tous</option>
          <option>Clôturé</option>
          <option>Annulé</option>
        </select>
      </div>
      <div class="col-md-3">
        <label class="form-label">Période (début)</label>
        <input type="date" class="form-control" id="filterDateStart">
      </div>
      <div class="col-md-3">
        <label class="form-label">Période (fin)</label>
        <input type="date" class="form-control" id="filterDateEnd">
      </div>
      <div class="col-md-3">
        <label class="form-label">Recherche (N° Permis ou Zone)</label>
        <input type="text" class="form-control" id="filterSearch" placeholder="Ex: PT-2025-0012">
      </div>
    </form>
  </div>

  <!-- Tableau -->
  <div class="table-responsive">
    <table class="table table-bordered table-hover align-middle">
      <thead>
        <tr>
          <th>N° Permis</th>
          <th>Type</th>
          <th>Zone</th>
          <th>Statut Final</th>
          <th>Date début</th>
          <th>Date clôture</th>
          <th>Archivé par</th>
          <th>Action</th>
        </tr>
      </thead>
      <tbody id="permitTable">
        <!-- Exemples -->
        <tr>
          <td>PT-2025-0012</td>
          <td>Travail à froid</td>
          <td>Zone 3</td>
          <td>Clôturé</td>
          <td>20/06/2025</td>
          <td>24/06/2025</td>
          <td>R.HSE_Ali</td>
          <td><button class="btn btn-sm btn-primary">Voir</button></td>
        </tr>
        <tr>
          <td>PT-2025-0009</td>
          <td>Travail à chaud</td>
          <td>Zone 2</td>
          <td>Annulé</td>
          <td>15/06/2025</td>
          <td>16/06/2025</td>
          <td>R.HSE_Mohamed</td>
          <td><button class="btn btn-sm btn-primary">Voir</button></td>
        </tr>
      </tbody>
    </table>
  </div>

  <script>
    // Recherche rapide dans le tableau
    document.getElementById('filterSearch').addEventListener('input', function () {
      const search = this.value.toLowerCase();
      const rows = document.querySelectorAll("#permitTable tr");
      rows.forEach(row => {
        row.style.display = row.innerText.toLowerCase().includes(search) ? "" : "none";
      });
    });

    // Tu peux ajouter ici des filtres plus complexes en JS si tu veux filtrer par date, type, etc.
  </script>

</body>
</html>
