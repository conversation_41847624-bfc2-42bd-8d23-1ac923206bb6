<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Système de Gestion des Permis de Travail - Représentant de l'Autorité de Zone</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"/>
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet"/>
    <style>
        :root {
            --primary: #3b82f6; 
            --primary-dark: #2563eb;
            --primary-light: #93c5fd;
            --secondary: #4f46e5;
            --accent: #3b82f6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --sidebar-width: 280px;
            --topbar-height: 70px;
            --sidebar-collapsed-width: 80px;
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;
            --transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--gray-50);
            color: var(--gray-900);
            display: flex;
            min-height: 100vh;
            line-height: 1.5;
            font-size: 15px;
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--gray-900);
            color: var(--gray-100);
            position: fixed;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
            transition: var(--transition-slow);
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            border-right: 1px solid rgba(255, 255, 255, 0.05);
            overflow: hidden;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar.collapsed .logo-text,
        .sidebar.collapsed .menu-title,
        .sidebar.collapsed .menu-text,
        .sidebar.collapsed .menu-badge,
        .sidebar.collapsed .user-info,
        .sidebar.collapsed .user-actions {
            opacity: 0;
            width: 0;
            height: 0;
            margin: 0;
            padding: 0;
            overflow: hidden;
            position: absolute;
        }

        .sidebar.collapsed .menu-link {
            justify-content: center;
            padding: 12px 0;
        }

        .sidebar.collapsed .menu-icon {
            margin-right: 0;
            font-size: 24px;
        }

        .sidebar-header {
            padding: 0 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: var(--topbar-height);
            min-height: var(--topbar-height);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 18px;
            font-weight: 700;
            color: white;
            white-space: nowrap;
        }

        .logo-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            flex-shrink: 0;
        }

        .logo-text {
            transition: var(--transition-slow);
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--gray-400);
            font-size: 20px;
            cursor: pointer;
            transition: var(--transition);
            padding: 8px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar-toggle:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-menu {
            flex: 1;
            padding: 16px 0;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--gray-700) var(--gray-800);
        }

        .sidebar-menu::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar-menu::-webkit-scrollbar-track {
            background: var(--gray-800);
        }

        .sidebar-menu::-webkit-scrollbar-thumb {
            background-color: var(--gray-700);
            border-radius: var(--radius-full);
        }

        .menu-group {
            margin-bottom: 24px;
        }

        .menu-title {
            padding: 0 24px 12px;
            font-size: 12px;
            font-weight: 600;
            color: var(--gray-400);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: var(--transition-slow);
        }

        .menu-items {
            list-style: none;
        }

        .menu-item {
            margin-bottom: 4px;
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: var(--gray-300);
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
            border-radius: 0;
            position: relative;
        }

        .menu-link:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: white;
        }

        .menu-link.active {
            background-color: var(--primary);
            color: white;
        }

        .menu-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background-color: white;
        }

        .menu-icon {
            margin-right: 12px;
            font-size: 20px;
            transition: var(--transition-slow);
        }

        .menu-text {
            font-weight: 500;
            transition: var(--transition-slow);
        }

        .menu-badge {
            margin-left: auto;
            background-color: var(--danger);
            color: white;
            font-size: 11px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: var(--radius-full);
            min-width: 18px;
            text-align: center;
            transition: var(--transition-slow);
        }

        .user-section {
            padding: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: auto;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: var(--radius-lg);
            transition: var(--transition);
            cursor: pointer;
        }

        .user-profile:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 16px;
            flex-shrink: 0;
        }

        .user-info {
            flex: 1;
            min-width: 0;
            transition: var(--transition-slow);
        }

        .user-name {
            font-weight: 600;
            color: white;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-role {
            font-size: 12px;
            color: var(--gray-400);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-actions {
            display: flex;
            gap: 8px;
            transition: var(--transition-slow);
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-full);
            border: none;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--gray-300);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            font-size: 16px;
        }

        .action-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid var(--gray-900);
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: var(--transition-slow);
            display: flex;
            flex-direction: column;
        }

        .sidebar.collapsed ~ .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        /* Top Navigation */
        .topbar {
            height: var(--topbar-height);
            background-color: white;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-sm);
        }

        .topbar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 24px;
            color: var(--gray-600);
            cursor: pointer;
            padding: 8px;
            border-radius: var(--radius-md);
            transition: var(--transition);
        }

        .menu-toggle:hover {
            background-color: var(--gray-100);
            color: var(--gray-900);
        }

        .breadcrumbs {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .breadcrumb-item {
            color: var(--gray-500);
            font-weight: 500;
        }

        .breadcrumb-item.active {
            color: var(--gray-700);
            font-weight: 500;
        }

        .topbar-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .topbar-action {
            position: relative;
        }

        .action-btn-lg {
            width: 44px;
            height: 44px;
            border-radius: var(--radius-full);
            border: none;
            background-color: var(--gray-100);
            color: var(--gray-600);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            font-size: 20px;
            position: relative;
        }

        .action-btn-lg:hover {
            background-color: var(--primary);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background-color: var(--danger);
            color: white;
            font-size: 10px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: var(--radius-full);
            min-width: 16px;
            text-align: center;
        }

        /* Content Wrapper */
        .content-wrapper {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .content-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 32px;
            gap: 24px;
        }

        .content-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 8px;
        }

        .content-subtitle {
            font-size: 16px;
            color: var(--gray-600);
            margin-top: 8px;
        }

        .content-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 20px;
            border-radius: var(--radius-md);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            border: 1px solid transparent;
            text-decoration: none;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .btn-outline {
            background-color: transparent;
            border-color: var(--gray-300);
            color: var(--gray-700);
        }

        .btn-outline:hover {
            background-color: var(--gray-50);
            border-color: var(--gray-400);
        }

        .btn-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        /* Form Styles */
        .form-section {
            background-color: white;
            padding: 32px;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            margin-bottom: 24px;
        }

        .form-section.hidden {
            display: none;
        }

        /* Search Bar Styles */
        .search-bar {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .search-bar input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 14px;
            transition: var(--transition);
        }

        .search-bar input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-bar button {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 12px 18px;
            border-radius: var(--radius-md);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
        }

        .search-bar button:hover {
            background-color: var(--primary-dark);
        }

        /* Table Styles */
        table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        th {
            background-color: var(--gray-50);
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: var(--gray-700);
            border-bottom: 1px solid var(--gray-200);
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        td {
            padding: 16px;
            border-bottom: 1px solid var(--gray-200);
            vertical-align: middle;
        }

        tr:hover {
            background-color: var(--gray-50);
        }

        tr:last-child td {
            border-bottom: none;
        }

        .compact-table tr:hover {
            background-color: var(--gray-50);
        }

        .compact-table .table-actions {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
        }

        .compact-table .btn-table {
            padding: 4px 8px;
            font-size: 10px;
            border-radius: var(--radius-sm);
            min-width: auto;
            white-space: nowrap;
        }

        .compact-table .btn-table i {
            font-size: 12px;
            margin-right: 4px;
        }

        /* Enhanced Button Styles */
        .btn-table {
            padding: 6px 10px;
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .btn-table.btn-view {
            background-color: var(--primary);
            color: white;
        }

        .btn-table.btn-view:hover {
            background-color: var(--primary-dark);
        }

        .btn-table.btn-edit {
            background-color: var(--warning);
            color: var(--gray-900);
        }

        .btn-table.btn-edit:hover {
            background-color: var(--warning);
            opacity: 0.8;
        }

        .btn-table.btn-delete {
            background-color: var(--danger);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .btn-table.btn-delete:hover {
            background-color: var(--danger);
            opacity: 0.9;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
        }

        .btn-table.btn-delete:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
        }

        .btn-table.btn-warning {
            background-color: var(--warning);
            color: var(--gray-900);
        }

        .btn-table.btn-warning:hover {
            background-color: var(--warning);
            opacity: 0.8;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
        }

        .btn-table.btn-warning:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
        }

        .btn-table.btn-success {
            background-color: var(--success);
            color: white;
        }

        .btn-table.btn-success:hover {
            background-color: #059669;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
        }

        .btn-table.btn-success:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
        }

        /* Status Badge Styles */
        .badge {
            padding: 4px 8px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge.badge-warning {
            background-color: #fef3c7;
            color: var(--warning);
        }

        .badge.badge-success {
            background-color: #f0fdf4;
            color: var(--success);
        }

        .badge.badge-danger {
            background-color: #fef2f2;
            color: var(--danger);
        }

        .badge.badge-info {
            background-color: #f0f9ff;
            color: var(--info);
        }

        .badge.badge-secondary {
            background-color: #f3f4f6;
            color: var(--gray-600);
        }

        .table-actions {
            display: flex;
            gap: 6px;
            justify-content: center;
        }

        .hidden {
            display: none;
        }

        /* Profile Form Styles from responsablehse.html */
        .profile-form {
            background-color: white;
            padding: 32px;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
        }

        .form-section-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* Readonly fields */
        input[readonly] {
            background-color: var(--gray-50);
            color: var(--gray-600);
            cursor: not-allowed;
        }

        /* Success/Error Messages */
        .message {
            padding: 16px 20px;
            border-radius: var(--radius-md);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
        }

        .message-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .message-error {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .message-warning {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .message-info {
            background-color: rgba(6, 182, 212, 0.1);
            color: var(--info);
            border: 1px solid rgba(6, 182, 212, 0.2);
        }

        /* Form Group Styles */
        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--gray-700);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 14px;
            transition: var(--transition);
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        /* Animation Styles */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-section,
        .profile-form {
            animation: fadeIn 0.5s ease-out;
        }

        /* Loading states */
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn:disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .menu-toggle {
                display: flex;
            }

            .content-wrapper {
                padding: 16px;
            }

            .content-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .content-actions {
                width: 100%;
                justify-content: flex-end;
            }
        }

        @media (max-width: 768px) {
            .topbar {
                padding: 0 16px;
            }

            .topbar-actions {
                gap: 8px;
            }

            .action-btn-lg {
                width: 36px;
                height: 36px;
                font-size: 18px;
            }

            .content-title {
                font-size: 24px;
            }

            .content-subtitle {
                font-size: 14px;
            }

            table {
                font-size: 14px;
            }

            th, td {
                padding: 12px 8px;
            }

            .btn-table {
                padding: 4px 6px;
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar Navigation -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <div class="logo-icon">
                    <img src="sonatrach-logo.png" alt="Logo Sonatrach" style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;"/>
                </div>
                <span class="logo-text">PT Pro</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class='bx bx-chevron-left'></i>
            </button>
        </div>

        <nav class="sidebar-menu">
            <div class="menu-group">
                <h3 class="menu-title">Navigation</h3>
                <ul class="menu-items">
                    <li class="menu-item">
                        <div class="menu-link active" onclick="showSection('permis-attente')">
                            <i class='bx bx-time-five menu-icon'></i>
                            <span class="menu-text">Permis en attente</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('prolongation')">
                            <i class='bx bx-time menu-icon'></i>
                            <span class="menu-text">Prolongation</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('fin-travail')">
                            <i class='bx bx-check-circle menu-icon'></i>
                            <span class="menu-text">Fin de travail</span>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="menu-group">
                <h3 class="menu-title">Compte</h3>
                <ul class="menu-items">
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('profile')">
                            <i class='bx bx-user menu-icon'></i>
                            <span class="menu-text">Mon Profil</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('parametres')">
                            <i class='bx bx-cog menu-icon'></i>
                            <span class="menu-text">Paramètres</span>
                        </div>
                    </li>
                </ul>
            </div>
        </nav>

        <div class="user-section">
            <div class="user-profile">
                <div class="user-avatar">
                    AZ
                </div>
                <div class="user-info">
                    <div class="user-name">Autorité Zone</div>
                    <div class="user-role">Représentant</div>
                </div>
                <div class="user-actions">
                    <button class="action-btn" onclick="logout()">
                        <i class='bx bx-log-out'></i>
                    </button>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navigation -->
        <div class="topbar">
            <div class="topbar-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class='bx bx-menu'></i>
                </button>
                <div class="breadcrumbs">
                    <span class="breadcrumb-item">ACCUEIL</span>
                    <span class="breadcrumb-item active" id="page-title">PERMIS EN ATTENTE</span>
                </div>
            </div>
            <div class="topbar-actions">
                <div class="topbar-action">
                    <button class="action-btn-lg">
                        <i class='bx bx-bell'></i>
                        <span class="notification-badge">3</span>
                    </button>
                </div>
                <div class="topbar-action">
                    <button class="action-btn-lg">
                        <i class='bx bx-search'></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-wrapper">
            <!-- Permis en attente Section -->
            <section id="permis-attente" class="form-section">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Permis en attente</h1>
                        <p class="content-subtitle">Permis en attente d'autorisation de début de travail</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-outline" onclick="refreshPermisAttente()">
                            <i class='bx bx-refresh btn-icon'></i>
                            Actualiser
                        </button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <input type="text" id="searchPermisAttente" placeholder="Rechercher un permis..." onkeyup="searchPermisAttente()">
                    <button onclick="searchPermisAttente()">Rechercher</button>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>N° PERMIS</th>
                            <th>DEMANDEUR</th>
                            <th>TYPE DE TRAVAIL</th>
                            <th>ZONE</th>
                            <th>DATE DÉBUT</th>
                            <th>DATE FIN</th>
                            <th>STATUT</th>
                            <th>ACTIONS</th>
                        </tr>
                    </thead>
                    <tbody id="permisAttenteTableBody">
                        <tr data-id="PT-2024-001">
                            <td>PT-2024-001</td>
                            <td>Ahmed Benali</td>
                            <td>Travaux à chaud</td>
                            <td>Zone A - Production</td>
                            <td>20/01/2024</td>
                            <td>22/01/2024</td>
                            <td><span class="badge badge-warning">En attente autorisation</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-delete" onclick="rejeterPermis('PT-2024-001')">
                                        Rejeter
                                    </button>
                                    <button class="btn-table btn-success" onclick="autoriserDebutTravail('PT-2024-001')">
                                        Autoriser début travail
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-003">
                            <td>PT-2024-003</td>
                            <td>Karim Messaoud</td>
                            <td>Travaux d'urgence</td>
                            <td>Zone C - Laboratoire</td>
                            <td>18/01/2024</td>
                            <td>21/01/2024</td>
                            <td><span class="badge badge-warning">En attente autorisation</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-delete" onclick="rejeterPermis('PT-2024-003')">
                                        Rejeter
                                    </button>
                                    <button class="btn-table btn-success" onclick="autoriserDebutTravail('PT-2024-003')">
                                        Autoriser début travail
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-005">
                            <td>PT-2024-005</td>
                            <td>Samira Khelifi</td>
                            <td>Travaux routiniers à faibles risques</td>
                            <td>Zone B - Stockage</td>
                            <td>19/01/2024</td>
                            <td>20/01/2024</td>
                            <td><span class="badge badge-warning">En attente autorisation</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-delete" onclick="rejeterPermis('PT-2024-005')">
                                        Rejeter
                                    </button>
                                    <button class="btn-table btn-success" onclick="autoriserDebutTravail('PT-2024-005')">
                                        Autoriser début travail
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- Prolongation Section -->
            <section id="prolongation" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Prolongation</h1>
                        <p class="content-subtitle">Demandes de prolongation par le responsable d'exécution</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-outline" onclick="refreshProlongation()">
                            <i class='bx bx-refresh btn-icon'></i>
                            Actualiser
                        </button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <input type="text" id="searchProlongation" placeholder="Rechercher une demande de prolongation..." onkeyup="searchProlongation()">
                    <button onclick="searchProlongation()">Rechercher</button>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>N° PERMIS</th>
                            <th>DEMANDEUR</th>
                            <th>TYPE DE TRAVAIL</th>
                            <th>ZONE</th>
                            <th>DATE FIN ACTUELLE</th>
                            <th>NOUVELLE DATE FIN</th>
                            <th>DURÉE</th>
                            <th>STATUT</th>
                            <th>ACTIONS</th>
                        </tr>
                    </thead>
                    <tbody id="prolongationTableBody">
                        <tr data-id="PT-2024-001">
                            <td>PT-2024-001</td>
                            <td>Ahmed Benali</td>
                            <td>Travaux à chaud</td>
                            <td>Zone A - Production</td>
                            <td>22/01/2024</td>
                            <td>25/01/2024</td>
                            <td>3 jours</td>
                            <td><span class="badge badge-warning">En attente approbation</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-delete" onclick="rejeterProlongation('PT-2024-001')">
                                        Rejeter
                                    </button>
                                    <button class="btn-table btn-view" onclick="voirDemandeProlongation('PT-2024-001')">
                                        Voir
                                    </button>
                                    <button class="btn-table btn-success" onclick="approuverProlongation('PT-2024-001')">
                                        Approuver
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-003">
                            <td>PT-2024-003</td>
                            <td>Karim Messaoud</td>
                            <td>Travaux d'urgence</td>
                            <td>Zone C - Laboratoire</td>
                            <td>21/01/2024</td>
                            <td>23/01/2024</td>
                            <td>2 jours</td>
                            <td><span class="badge badge-warning">En attente approbation</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-delete" onclick="rejeterProlongation('PT-2024-003')">
                                        Rejeter
                                    </button>
                                    <button class="btn-table btn-view" onclick="voirDemandeProlongation('PT-2024-003')">
                                        Voir
                                    </button>
                                    <button class="btn-table btn-success" onclick="approuverProlongation('PT-2024-003')">
                                        Approuver
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-004">
                            <td>PT-2024-004</td>
                            <td>Yacine Benaissa</td>
                            <td>Travaux routiniers à faibles risques</td>
                            <td>Zone B - Stockage</td>
                            <td>20/01/2024</td>
                            <td>24/01/2024</td>
                            <td>4 jours</td>
                            <td><span class="badge badge-warning">En attente approbation</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-delete" onclick="rejeterProlongation('PT-2024-004')">
                                        Rejeter
                                    </button>
                                    <button class="btn-table btn-view" onclick="voirDemandeProlongation('PT-2024-004')">
                                        Voir
                                    </button>
                                    <button class="btn-table btn-success" onclick="approuverProlongation('PT-2024-004')">
                                        Approuver
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- Fin de travail Section -->
            <section id="fin-travail" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Fin de travail</h1>
                        <p class="content-subtitle">Déclarations de fin de travail par le responsable d'exécution</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-outline" onclick="refreshFinTravail()">
                            <i class='bx bx-refresh btn-icon'></i>
                            Actualiser
                        </button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <input type="text" id="searchFinTravail" placeholder="Rechercher une déclaration..." onkeyup="searchFinTravail()">
                    <button onclick="searchFinTravail()">Rechercher</button>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>N° PERMIS</th>
                            <th>DEMANDEUR</th>
                            <th>TYPE DE TRAVAIL</th>
                            <th>ZONE</th>
                            <th>DATE DÉBUT</th>
                            <th>DATE FIN PRÉVUE</th>
                            <th>DATE DÉCLARATION</th>
                            <th>STATUT</th>
                            <th>ACTIONS</th>
                        </tr>
                    </thead>
                    <tbody id="finTravailTableBody">
                        <tr data-id="PT-2024-002">
                            <td>PT-2024-002</td>
                            <td>Fatima Zohra</td>
                            <td>Travaux électriques</td>
                            <td>Zone B - Stockage</td>
                            <td>18/01/2024</td>
                            <td>19/01/2024</td>
                            <td>19/01/2024 14:30</td>
                            <td><span class="badge badge-warning">En attente confirmation</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="voirDeclarationFin('PT-2024-002')">
                                        Voir
                                    </button>
                                    <button class="btn-table btn-success" onclick="confirmerFinTravail('PT-2024-002')">
                                        Confirmer
                                    </button>
                                    <button class="btn-table btn-delete" onclick="nonConfirmerFinTravail('PT-2024-002')">
                                        Non confirmé
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-006">
                            <td>PT-2024-006</td>
                            <td>Mohamed Larbi</td>
                            <td>Travaux en hauteur</td>
                            <td>Zone C - Laboratoire</td>
                            <td>17/01/2024</td>
                            <td>18/01/2024</td>
                            <td>18/01/2024 16:45</td>
                            <td><span class="badge badge-warning">En attente confirmation</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="voirDeclarationFin('PT-2024-006')">
                                        Voir
                                    </button>
                                    <button class="btn-table btn-success" onclick="confirmerFinTravail('PT-2024-006')">
                                        Confirmer
                                    </button>
                                    <button class="btn-table btn-delete" onclick="nonConfirmerFinTravail('PT-2024-006')">
                                        Non confirmé
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-007">
                            <td>PT-2024-007</td>
                            <td>Nadia Bensalem</td>
                            <td>Travaux de maintenance</td>
                            <td>Zone A - Production</td>
                            <td>19/01/2024</td>
                            <td>20/01/2024</td>
                            <td>20/01/2024 17:20</td>
                            <td><span class="badge badge-warning">En attente confirmation</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="voirDeclarationFin('PT-2024-007')">
                                        Voir
                                    </button>
                                    <button class="btn-table btn-success" onclick="confirmerFinTravail('PT-2024-007')">
                                        Confirmer
                                    </button>
                                    <button class="btn-table btn-delete" onclick="nonConfirmerFinTravail('PT-2024-007')">
                                        Non confirmé
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- Profile Section -->
            <section id="profile" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Mon Profil</h1>
                        <p class="content-subtitle">Gérez vos informations personnelles</p>
                    </div>
                </div>

                <div class="profile-form" style="max-width: 600px;">
                    <div style="text-align: center; margin-bottom: 32px;">
                        <div class="user-avatar" style="width: 80px; height: 80px; font-size: 32px; margin: 0 auto 16px;">
                            AZ
                        </div>
                        <h3 style="margin-bottom: 8px;">Représentant de l'Autorité de Zone</h3>
                        <p style="color: var(--gray-600);">Autorité de Zone - Secteur Production</p>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Nom complet</label>
                            <input type="text" value="Autorité Zone" readonly>
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" value="<EMAIL>" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Rôle</label>
                            <input type="text" value="Représentant de l'Autorité de Zone" readonly>
                        </div>
                        <div class="form-group">
                            <label>Zone de responsabilité</label>
                            <input type="text" value="Toutes les zones" readonly>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Parametres Section -->
            <section id="parametres" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Paramètres</h1>
                        <p class="content-subtitle">Configurez vos préférences</p>
                    </div>
                </div>

                <div class="profile-form" style="max-width: 600px;">
                    <div class="form-section-title">
                        <i class='bx bx-cog'></i>
                        Préférences du système
                    </div>

                    <div class="form-group">
                        <label>Notifications</label>
                        <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px; border: 1px solid var(--gray-200); border-radius: var(--radius-md); background-color: var(--gray-50);">
                            <span>Notifications par email</span>
                            <input type="checkbox" checked>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Langue d'interface</label>
                        <select>
                            <option value="fr" selected>Français</option>
                            <option value="ar">العربية</option>
                            <option value="en">English</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Fuseau horaire</label>
                        <select>
                            <option value="africa/algiers" selected>Afrique/Alger (GMT+1)</option>
                            <option value="utc">UTC (GMT+0)</option>
                            <option value="europe/paris">Europe/Paris (GMT+1)</option>
                        </select>
                    </div>

                    <div style="margin-top: 32px;">
                        <button class="btn btn-primary">
                            <i class='bx bx-save btn-icon'></i>
                            Sauvegarder les paramètres
                        </button>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script>
        // Navigation functionality
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.form-section').forEach(section => {
                section.classList.add('hidden');
            });

            // Show selected section
            document.getElementById(sectionId).classList.remove('hidden');

            // Update page title
            const titles = {
                'permis-attente': 'PERMIS EN ATTENTE',
                'prolongation': 'PROLONGATION',
                'fin-travail': 'FIN DE TRAVAIL',
                'profile': 'MON PROFIL',
                'parametres': 'PARAMÈTRES'
            };
            document.getElementById("page-title").textContent = titles[sectionId];

            // Update active menu item
            document.querySelectorAll(".menu-link").forEach(link => link.classList.remove('active'));
            document.querySelector(`.menu-link[onclick="showSection('${sectionId}')"]`).classList.add('active');
        }

        // Search functions
        function searchPermisAttente() {
            searchTable('searchPermisAttente', 'permisAttenteTableBody');
        }

        function searchProlongation() {
            searchTable('searchProlongation', 'prolongationTableBody');
        }

        function searchFinTravail() {
            searchTable('searchFinTravail', 'finTravailTableBody');
        }

        function searchTable(inputId, tableBodyId) {
            let input, filter, table, tr, td, i, txtValue;
            input = document.getElementById(inputId);
            filter = input.value.toUpperCase();
            table = document.getElementById(tableBodyId);
            tr = table.getElementsByTagName("tr");

            for (i = 0; i < tr.length; i++) {
                tr[i].style.display = "none";
                td = tr[i].getElementsByTagName("td");
                for (let j = 0; j < td.length; j++) {
                    if (td[j]) {
                        txtValue = td[j].textContent || td[j].innerText;
                        if (txtValue.toUpperCase().indexOf(filter) > -1) {
                            tr[i].style.display = "";
                            break;
                        }
                    }
                }
            }
        }

        // Action functions for Permis en attente
        function rejeterPermis(id) {
            if (confirm(`Êtes-vous sûr de vouloir rejeter le permis ${id} ?`)) {
                alert(`Permis ${id} rejeté avec succès`);
                // Remove the row from table
                const row = document.querySelector(`tr[data-id="${id}"]`);
                if (row) row.remove();
            }
        }

        function autoriserDebutTravail(id) {
            if (confirm(`Autoriser le début de travail pour le permis ${id} ?`)) {
                alert(`Début de travail autorisé pour le permis ${id}`);
                // Update status
                const row = document.querySelector(`tr[data-id="${id}"]`);
                if (row) {
                    const statusCell = row.querySelector('.badge');
                    if (statusCell) {
                        statusCell.className = 'badge badge-success';
                        statusCell.textContent = 'AUTORISÉ';
                    }
                    // Update actions
                    const actionsCell = row.querySelector('.table-actions');
                    if (actionsCell) {
                        actionsCell.innerHTML = '<span style="color: var(--success); font-weight: 600;">Autorisé</span>';
                    }
                }
            }
        }

        // Action functions for Prolongation
        function rejeterProlongation(id) {
            if (confirm(`Êtes-vous sûr de vouloir rejeter la demande de prolongation pour le permis ${id} ?`)) {
                alert(`Demande de prolongation rejetée pour le permis ${id}`);
                // Remove the row from table
                const row = document.querySelector(`tr[data-id="${id}"]`);
                if (row) row.remove();
            }
        }

        function voirDemandeProlongation(id) {
            alert(`Affichage des détails de la demande de prolongation pour le permis ${id}`);
        }

        function approuverProlongation(id) {
            if (confirm(`Approuver la demande de prolongation pour le permis ${id} ?`)) {
                alert(`Demande de prolongation approuvée pour le permis ${id}`);
                // Update status
                const row = document.querySelector(`tr[data-id="${id}"]`);
                if (row) {
                    const statusCell = row.querySelector('.badge');
                    if (statusCell) {
                        statusCell.className = 'badge badge-success';
                        statusCell.textContent = 'APPROUVÉE';
                    }
                    // Update actions
                    const actionsCell = row.querySelector('.table-actions');
                    if (actionsCell) {
                        actionsCell.innerHTML = '<span style="color: var(--success); font-weight: 600;">Approuvée</span>';
                    }
                }
            }
        }

        // Action functions for Fin de travail
        function voirDeclarationFin(id) {
            alert(`Affichage des détails de la déclaration de fin de travail pour le permis ${id}`);
        }

        function confirmerFinTravail(id) {
            if (confirm(`Confirmer la fin de travail pour le permis ${id} ?`)) {
                alert(`Fin de travail confirmée pour le permis ${id}`);
                // Update status
                const row = document.querySelector(`tr[data-id="${id}"]`);
                if (row) {
                    const statusCell = row.querySelector('.badge');
                    if (statusCell) {
                        statusCell.className = 'badge badge-success';
                        statusCell.textContent = 'CONFIRMÉ';
                    }
                    // Update actions
                    const actionsCell = row.querySelector('.table-actions');
                    if (actionsCell) {
                        actionsCell.innerHTML = '<span style="color: var(--success); font-weight: 600;">Confirmé</span>';
                    }
                }
            }
        }

        function nonConfirmerFinTravail(id) {
            if (confirm(`Marquer comme non confirmé la fin de travail pour le permis ${id} ?`)) {
                alert(`Fin de travail marquée comme non confirmée pour le permis ${id}`);
                // Update status
                const row = document.querySelector(`tr[data-id="${id}"]`);
                if (row) {
                    const statusCell = row.querySelector('.badge');
                    if (statusCell) {
                        statusCell.className = 'badge badge-danger';
                        statusCell.textContent = 'NON CONFIRMÉ';
                    }
                    // Update actions
                    const actionsCell = row.querySelector('.table-actions');
                    if (actionsCell) {
                        actionsCell.innerHTML = '<span style="color: var(--danger); font-weight: 600;">Non confirmé</span>';
                    }
                }
            }
        }

        // Refresh functions
        function refreshPermisAttente() {
            alert('Actualisation des permis en attente...');
            location.reload();
        }

        function refreshProlongation() {
            alert('Actualisation des demandes de prolongation...');
            location.reload();
        }

        function refreshFinTravail() {
            alert('Actualisation des déclarations de fin de travail...');
            location.reload();
        }

        // Sidebar functionality
        function logout() {
            if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                alert('Déconnexion...');
                window.location.href = 'login.html';
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar toggle functionality
            const sidebarToggle = document.getElementById('sidebarToggle');
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.querySelector('.sidebar');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                });
            }

            if (menuToggle) {
                menuToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                if (window.innerWidth <= 1024) {
                    if (!sidebar.contains(event.target) && !menuToggle.contains(event.target)) {
                        sidebar.classList.remove('active');
                    }
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 1024) {
                    sidebar.classList.remove('active');
                }
            });
        });
    </script>
</body>
</html>
