<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Système de Gestion des Permis de Travail - Initiateur</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"/>
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet"/>
    <style>
        :root {
            --primary: #3b82f6;
            --primary-dark: #2563eb;
            --primary-light: #93c5fd;
            --secondary: #4f46e5;
            --accent: #3b82f6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --sidebar-width: 280px;
            --topbar-height: 70px;
            --sidebar-collapsed-width: 80px;
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;
            --transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--gray-50);
            color: var(--gray-900);
            display: flex;
            min-height: 100vh;
            line-height: 1.5;
            font-size: 15px;
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--gray-900);
            color: var(--gray-100);
            position: fixed;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
            transition: var(--transition-slow);
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            border-right: 1px solid rgba(255, 255, 255, 0.05);
            overflow: hidden;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar.collapsed .logo-text,
        .sidebar.collapsed .menu-title,
        .sidebar.collapsed .menu-text,
        .sidebar.collapsed .menu-badge,
        .sidebar.collapsed .user-info,
        .sidebar.collapsed .user-actions {
            opacity: 0;
            width: 0;
            height: 0;
            margin: 0;
            padding: 0;
            overflow: hidden;
            position: absolute;
        }

        .sidebar.collapsed .menu-link {
            justify-content: center;
            padding: 12px 0;
        }

        .sidebar.collapsed .menu-icon {
            margin-right: 0;
            font-size: 24px;
        }

        .sidebar-header {
            padding: 0 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: var(--topbar-height);
            min-height: var(--topbar-height);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 18px;
            font-weight: 700;
            color: white;
            white-space: nowrap;
        }

        .logo-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            flex-shrink: 0;
        }

        .logo-text {
            transition: var(--transition-slow);
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--gray-400);
            font-size: 20px;
            cursor: pointer;
            transition: var(--transition);
            padding: 8px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar-toggle:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-menu {
            flex: 1;
            padding: 16px 0;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--gray-700) var(--gray-800);
        }

        .sidebar-menu::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar-menu::-webkit-scrollbar-track {
            background: var(--gray-800);
        }

        .sidebar-menu::-webkit-scrollbar-thumb {
            background-color: var(--gray-700);
            border-radius: var(--radius-full);
        }

        .menu-group {
            margin-bottom: 24px;
        }

        .menu-title {
            padding: 0 24px 12px;
            font-size: 12px;
            font-weight: 600;
            color: var(--gray-400);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: var(--transition-slow);
        }

        .menu-items {
            list-style: none;
        }

        .menu-item {
            margin: 2px 0;
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: var(--gray-300);
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: var(--transition);
            font-size: 14px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .menu-link:hover {
            background-color: var(--gray-800);
            color: white;
        }

        .menu-link.active {
            background-color: rgba(37, 99, 235, 0.1);
            border-left-color: var(--primary);
            color: white;
        }

        .menu-link.active .menu-icon {
            color: var(--primary-light);
        }

        .menu-icon {
            margin-right: 12px;
            font-size: 20px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-400);
            transition: var(--transition);
            flex-shrink: 0;
        }

        .menu-text {
            transition: var(--transition-slow);
            white-space: nowrap;
        }
        
        .menu-badge {
            background-color: var(--danger);
            color: white;
            font-size: 10px;
            font-weight: 700;
            height: 18px;
            min-width: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-full);
            padding: 0 6px;
            margin-left: auto;
        }

        .sidebar-footer {
            padding: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: auto;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px;
            border-radius: var(--radius-md);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .user-profile:hover {
            background-color: var(--gray-800);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            flex-shrink: 0;
            position: relative;
        }

        .user-info {
            flex: 1;
            min-width: 0;
            transition: var(--transition-slow);
        }

        .user-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-role {
            font-size: 12px;
            color: var(--gray-400);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-actions {
            display: flex;
            gap: 8px;
            transition: var(--transition-slow);
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--gray-400);
            cursor: pointer;
            transition: var(--transition);
            border: none;
            position: relative;
        }

        .action-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 18px;
            height: 18px;
            background-color: var(--danger);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 700;
            color: white;
            border: 2px solid var(--gray-900);
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: var(--transition-slow);
            display: flex;
            flex-direction: column;
        }

        .sidebar.collapsed ~ .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        /* Top Navigation */
        .topbar {
            height: var(--topbar-height);
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            box-shadow: var(--shadow-sm);
            border-bottom: 1px solid var(--gray-200);
            position: sticky;
            top: 0;
            z-index: 900;
        }

        .topbar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .menu-toggle {
            background: none;
            border: none;
            color: var(--gray-500);
            font-size: 24px;
            cursor: pointer;
            transition: var(--transition);
            display: none;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
        }

        .menu-toggle:hover {
            background-color: var(--gray-100);
            color: var(--gray-700);
        }

        .breadcrumbs {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .breadcrumb-item {
            color: var(--gray-500);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .breadcrumb-item:not(:last-child)::after {
            content: '/';
            color: var(--gray-300);
            margin-left: 8px;
        }

        .breadcrumb-item.active {
            color: var(--gray-700);
            font-weight: 500;
        }

        .topbar-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .topbar-action {
            position: relative;
            cursor: pointer;
        }

        .action-btn-lg {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            color: var(--gray-500);
            cursor: pointer;
            transition: var(--transition);
            border: none;
            position: relative;
        }

        .action-btn-lg:hover {
            background-color: var(--gray-100);
            color: var(--gray-700);
        }

        .action-btn-lg .notification-badge {
            border-color: white;
        }

        .user-dropdown {
            position: absolute;
            top: calc(100% + 10px);
            right: 0;
            width: 280px;
            background-color: white;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-xl);
            padding: 8px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: var(--transition);
        }

        .user-dropdown.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-header {
            padding: 12px 16px;
            border-bottom: 1px solid var(--gray-100);
            margin-bottom: 8px;
        }

        .dropdown-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--gray-900);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            border-radius: var(--radius-sm);
            color: var(--gray-700);
            text-decoration: none;
            font-size: 14px;
            transition: var(--transition);
            cursor: pointer;
        }

        .dropdown-item:hover {
            background-color: var(--gray-50);
            color: var(--primary);
        }
        
        .dropdown-item i {
            margin-right: 12px;
            font-size: 18px;
            width: 20px;
            color: var(--gray-500);
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--gray-100);
            margin: 8px 0;
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--gray-200);
            margin: 6px 16px;
            opacity: 0.7;
        }
        
        /* Settings Styles */
        .checkbox-label {
            display: flex;
            align-items: center;
            font-weight: 600;
            color: var(--gray-800);
            cursor: pointer;
            margin-bottom: 4px;
        }
        
        .checkbox-label input[type="checkbox"] {
            margin-right: 10px;
            width: 18px;
            height: 18px;
            border: 2px solid var(--primary);
            border-radius: 4px;
            cursor: pointer;
        }
        
        .form-text {
            font-size: 13px;
            color: var(--gray-500);
            margin-top: 4px;
            margin-left: 28px;
            margin-bottom: 16px;
        }
        
        .profile-section {
            background: white;
            border-radius: var(--radius-lg);
            margin-bottom: 24px;
            border: 1px solid var(--gray-200);
            overflow: hidden;
        }
        
        .profile-section-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--gray-200);
            background-color: var(--gray-50);
        }
        
        .profile-section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
        }
        
        .profile-section-body {
            padding: 24px;
        }
        
        .profile-actions {
            display: flex;
            justify-content: flex-end;
            padding-top: 16px;
            margin-top: 24px;
            border-top: 1px solid var(--gray-200);
        }

        /* Content Area */
        .content-wrapper {
            flex: 1;
            padding: 24px;
            background-color: var(--gray-50);
        }

        .content-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
        }

        .content-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
        }

        .content-subtitle {
            font-size: 16px;
            color: var(--gray-600);
            margin-top: 8px;
        }

        .content-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 20px;
            border-radius: var(--radius-md);
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: var(--transition);
            border: 1px solid transparent;
            text-decoration: none;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .btn-outline {
            background-color: transparent;
            border-color: var(--gray-300);
            color: var(--gray-700);
        }

        .btn-outline:hover {
            background-color: var(--gray-50);
            border-color: var(--gray-400);
        }

        .btn-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        .btn-secondary {
            background-color: var(--gray-100);
            color: var(--gray-700);
            border-color: var(--gray-300);
        }

        .btn-secondary:hover {
            background-color: var(--gray-200);
            border-color: var(--gray-400);
        }

        .btn-danger {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }

        .btn-danger:hover {
            background-color: #dc2626;
            border-color: #dc2626;
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-md);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            font-size: 16px;
        }

        .view-btn {
            background-color: var(--info);
            color: white;
        }

        .view-btn:hover {
            background-color: #0891b2;
            transform: translateY(-1px);
        }

        .download-btn {
            background-color: var(--success);
            color: white;
        }

        .download-btn:hover {
            background-color: #059669;
            transform: translateY(-1px);
        }

        .edit-btn {
            background-color: var(--warning);
            color: white;
        }

        .edit-btn:hover {
            background-color: #d97706;
            transform: translateY(-1px);
        }

        .delete-btn {
            background-color: var(--danger);
            color: white;
        }

        .delete-btn:hover {
            background-color: #dc2626;
            transform: translateY(-1px);
        }

        .pause-btn {
            background-color: var(--gray-500);
            color: white;
        }

        .pause-btn:hover {
            background-color: var(--gray-600);
            transform: translateY(-1px);
        }

        .renew-btn {
            background-color: var(--primary);
            color: white;
        }

        .renew-btn:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
        }

        .archive-btn {
            background-color: var(--gray-400);
            color: white;
        }

        .archive-btn:hover {
            background-color: var(--gray-500);
            transform: translateY(-1px);
        }

        /* Status Badges */
        .status-badge {
            padding: 4px 12px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-approved {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-rejected {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .status-in-progress {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .status-expired {
            background-color: #f3f4f6;
            color: #374151;
        }

        /* Type Badges */
        .badge {
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-info {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .badge-warning {
            background-color: #fef3c7;
            color: #92400e;
        }

        .badge-danger {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .badge-success {
            background-color: #dcfce7;
            color: #166534;
        }

        .badge-secondary {
            background-color: #f1f5f9;
            color: #475569;
        }

        /* Table Actions */
        .table-actions {
            display: flex;
            gap: 8px;
            justify-content: center;
            align-items: center;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-md);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            font-size: 16px;
            background-color: var(--gray-100);
            color: var(--gray-600);
        }

        .btn-icon:hover {
            background-color: var(--primary);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-icon.danger:hover {
            background-color: var(--danger);
        }

        .btn-icon.success:hover {
            background-color: var(--success);
        }

        .btn-icon.warning:hover {
            background-color: var(--warning);
        }

        .btn-icon.info:hover {
            background-color: var(--info);
        }

        .btn-icon.success {
            background-color: var(--success);
            color: white;
        }

        .btn-icon.warning {
            background-color: var(--warning);
            color: white;
        }

        .btn-icon.info {
            background-color: var(--info);
            color: white;
        }

        .btn-icon.danger {
            background-color: var(--danger);
            color: white;
        }

        /* Modal Detail Styles */
        .permit-details, .draft-preview {
            background-color: var(--gray-50);
            padding: 20px;
            border-radius: var(--radius-md);
            margin: 15px 0;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--gray-200);
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-row strong {
            color: var(--gray-700);
            min-width: 150px;
        }

        /* Additional status styles */
        .status-cancelled {
            background-color: #f3f4f6;
            color: #6b7280;
        }

        /* Enhanced Status Badge Styles (to match administrateur.html) */
        .status-badge {
            padding: 4px 12px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-inactive {
            background-color: rgba(107, 114, 128, 0.1);
            color: var(--gray-600);
            border: 1px solid rgba(107, 114, 128, 0.2);
        }

        .status-low {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-medium {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .status-high {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .status-critical {
            background-color: rgba(127, 29, 29, 0.1);
            color: #7f1d1d;
            border: 1px solid rgba(127, 29, 29, 0.2);
        }

        /* Form Styles */
        .form-section {
            background-color: white;
            padding: 32px;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            margin-bottom: 24px;
            border: 1px solid var(--gray-200);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-row-4 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--gray-700);
        }

        .label-icon {
            margin-right: 6px;
            font-size: 16px;
            color: var(--primary);
        }

        input[type="text"],
        input[type="email"],
        input[type="password"],
        input[type="date"],
        input[type="time"],
        textarea,
        select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 14px;
            background-color: white;
            transition: var(--transition);
            font-family: inherit;
        }

        input:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 120px;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 12px;
            margin-top: 12px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            transition: var(--transition);
            cursor: pointer;
        }

        .checkbox-item:hover {
            background-color: var(--gray-50);
            border-color: var(--primary);
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary);
            margin: 0;
        }

        .checkbox-item label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            font-size: 14px;
        }

        table {
            width: 90%; /* Reduced table width */
            border-collapse: collapse;
            margin: 0 auto 20px; /* Center the table */
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        th,
        td {
            padding: 10px; /* Reduced padding */
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
            font-size: 13px; /* Reduced font size */
        }

        th {
            background-color: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
            font-size: 13px;
        }

        td input {
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-sm);
            padding: 8px 10px; /* Reduced padding */
            font-size: 14px;
            width: 100%;
        }

        /* Profile Styles */
        .profile-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            padding: 32px;
            border-radius: var(--radius-xl);
            margin-bottom: 32px;
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .profile-info {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: var(--radius-full);
            background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 28px;
            flex-shrink: 0;
            box-shadow: var(--shadow-md);
            border: 3px solid rgba(255,255,255,0.2);
        }

        .profile-details {
            flex: 1;
        }

        .profile-name {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .profile-username {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 12px;
        }

        .profile-role {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border-radius: var(--radius-full);
            font-size: 14px;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .profile-form {
            background-color: white;
            padding: 32px;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
        }

        .form-section-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* Section Headers */
        .section-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--gray-200);
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--gray-900);
        }

        .section-icon {
            font-size: 24px;
            color: var(--primary);
        }

        /* Success/Error Messages */
        .message {
            padding: 16px 20px;
            border-radius: var(--radius-md);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
        }

        .message-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .message-error {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Required field indicator */
        .required {
            color: var(--danger);
            margin-left: 4px;
        }

        /* Dashboard Styles */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .stats-card {
            background-color: white;
            border-radius: var(--radius-lg);
            padding: 24px;
            box-shadow: var(--shadow-sm);
            display: flex;
            align-items: center;
            gap: 16px;
            transition: var(--transition);
        }
        
        .stats-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }
        
        .stats-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .stats-content {
            flex: 1;
        }
        
        .stats-title {
            font-size: 14px;
            color: var(--gray-500);
            margin-bottom: 4px;
        }
        
        .stats-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 4px;
        }
        
        .stats-change {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .increase {
            color: var(--success);
        }
        
        .decrease {
            color: var(--danger);
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
        }
        
        .dashboard-card {
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }
        
        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-800);
        }

        /* Table Styles */
        .table-container {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .data-table {
            width: 90%; /* Reduced table width to match administrateur */
            border-collapse: collapse;
            margin: 0 auto 20px; /* Center the table */
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .data-table th,
        .data-table td {
            padding: 10px; /* Reduced padding to match administrateur */
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
            font-size: 13px; /* Reduced font size to match administrateur */
        }

        .data-table th {
            background-color: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
            font-size: 13px;
        }

        .data-table tbody tr:hover {
            background-color: var(--gray-50);
        }

        /* Badge Styles */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 500;
        }

        .badge-info {
            background-color: rgba(59, 130, 246, 0.1);
            color: var(--primary);
        }

        .badge-warning {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning);
        }

        .badge-secondary {
            background-color: rgba(107, 114, 128, 0.1);
            color: var(--gray-600);
        }

        /* Status Badge Styles */
        .status-badge {
            padding: 4px 12px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-inactive {
            background-color: rgba(107, 114, 128, 0.1);
            color: var(--gray-600);
            border: 1px solid rgba(107, 114, 128, 0.2);
        }

        .status-low {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-medium {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .status-high {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .status-approved {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-pending {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .status-rejected {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .status-in-progress {
            background-color: rgba(59, 130, 246, 0.1);
            color: var(--primary);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .status-zone0 {
            background-color: rgba(220, 38, 38, 0.1);
            color: #dc2626;
            border: 1px solid rgba(220, 38, 38, 0.2);
        }

        .status-zone1 {
            background-color: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .status-zone2 {
            background-color: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .status-expired {
            background-color: rgba(107, 114, 128, 0.1);
            color: var(--gray-600);
        }

        /* Action Buttons - Match administrateur.html exactly */
        .action-buttons {
            display: flex;
            gap: 6px; /* Reduced gap to match administrateur */
            justify-content: center;
        }

        .action-btn {
            padding: 6px 10px; /* Reduced padding to match administrateur */
            border-radius: var(--radius-sm);
            font-size: 12px; /* Reduced font size to match administrateur */
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 4px; /* Reduced gap to match administrateur */
        }

        .view-btn {
            background-color: var(--primary);
            color: white;
        }

        .view-btn:hover {
            background-color: var(--primary-dark);
        }

        .download-btn {
            background-color: var(--success);
            color: white;
        }

        .download-btn:hover {
            background-color: var(--success);
            opacity: 0.8;
        }

        .edit-btn {
            background-color: var(--warning);
            color: var(--gray-900);
        }

        .edit-btn:hover {
            background-color: var(--warning);
            opacity: 0.8;
        }

        .pause-btn {
            background-color: var(--gray-500);
            color: white;
        }

        .pause-btn:hover {
            background-color: var(--gray-600);
        }

        .renew-btn {
            background-color: var(--primary);
            color: white;
        }

        .renew-btn:hover {
            background-color: var(--primary-dark);
        }

        .archive-btn {
            background-color: var(--gray-500);
            color: white;
        }

        .archive-btn:hover {
            background-color: var(--gray-600);
        }

        .delete-btn {
            background-color: var(--danger);
            color: white;
        }

        .delete-btn:hover {
            background-color: var(--danger);
            opacity: 0.8;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 48px 24px;
        }

        .empty-icon {
            font-size: 64px;
            color: var(--gray-400);
            margin-bottom: 16px;
        }

        .empty-state h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 8px;
        }

        .empty-state p {
            color: var(--gray-500);
            margin-bottom: 24px;
        }

        /* Readonly fields */
        input[readonly] {
            background-color: var(--gray-50);
            color: var(--gray-600);
            cursor: not-allowed;
        }

        /* Success/Error Messages */
        .message {
            padding: 16px 20px;
            border-radius: var(--radius-md);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
        }

        .message-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .message-error {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Section Headers */
        .section-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--gray-200);
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--gray-900);
        }

        .section-icon {
            font-size: 24px;
            color: var(--primary);
        }

        /* Content Header Styles */
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 32px;
            padding-bottom: 24px;
            border-bottom: 1px solid var(--gray-200);
        }

        .content-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 8px;
        }

        .content-subtitle {
            font-size: 16px;
            color: var(--gray-600);
            margin: 0;
        }

        .content-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        /* Required field indicator */
        .required {
            color: var(--danger);
            margin-left: 4px;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            :root {
                --sidebar-width: 240px;
            }
            
            .stats-container {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 1100;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .menu-toggle {
                display: flex;
            }

            .sidebar-toggle {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .topbar {
                padding: 0 16px;
            }

            .content-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .content-actions {
                width: 100%;
                justify-content: flex-end;
            }

            .form-row,
            .form-row-3,
            .form-row-4 {
                grid-template-columns: 1fr;
            }
            
            /* Dashboard Responsive */
            .stats-container {
                grid-template-columns: 1fr 1fr;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 767px) {
            .stats-container {
                grid-template-columns: 1fr;
            }

            .profile-info {
                flex-direction: column;
                text-align: center;
            }

            .checkbox-group {
                grid-template-columns: 1fr;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 576px) {
            .topbar-actions {
                gap: 8px;
            }

            .action-btn-lg {
                width: 36px;
                height: 36px;
            }

            .content-wrapper {
                padding: 16px;
            }

            .form-section,
            .profile-form {
                padding: 20px;
            }

            .profile-header {
                padding: 24px;
            }

            .dashboard-card {
                margin-bottom: 16px;
            }

            .card-header {
                padding: 12px 16px;
            }

            .stats-card {
                padding: 16px;
            }

            .action-buttons {
                flex-wrap: wrap;
            }

            .data-table th,
            .data-table td {
                padding: 12px 8px;
                font-size: 13px;
            }
        }

        /* Animation */
        @keyframes fadeIn {
            from { 
                opacity: 0; 
                transform: translateY(20px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }

        .form-section,
        .profile-header,
        .profile-form {
            animation: fadeIn 0.5s ease-out;
        }

        /* Hidden sections */
        .hidden {
            display: none;
        }

        /* Loading states */
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn:disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }
        
        /* Data Table Styles */
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
        }
        
        .data-table {
            width: 90%; /* Match administrateur.html */
            margin: 0 auto 20px; /* Center the table */
            border-collapse: collapse;
            border-spacing: 0;
            background-color: white;
            border-radius: var(--radius-md);
            overflow: hidden;
        }
        
        .data-table thead {
            background-color: var(--gray-100);
        }
        
        .data-table th {
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: var(--gray-700);
            border-bottom: 2px solid var(--gray-200);
            white-space: nowrap;
        }
        
        .data-table td {
            padding: 16px;
            border-bottom: 1px solid var(--gray-200);
            color: var(--gray-700);
        }
        
        .data-table tbody tr:hover {
            background-color: var(--gray-50);
        }
        
        .data-table .btn-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--gray-100);
            color: var(--gray-600);
            cursor: pointer;
            transition: var(--transition);
            border: none;
            font-size: 18px;
            margin-right: 4px;
            position: relative;
        }
        
        .data-table .btn-icon:hover {
            background-color: var(--gray-200);
            color: var(--gray-800);
        }
        
        .data-table .btn-icon.btn-danger {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
        }
        
        .data-table .btn-icon.btn-danger:hover {
            background-color: rgba(239, 68, 68, 0.2);
        }
        
        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }
        
        .modal.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal-content {
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            width: 600px;
            max-width: 90%;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
        }
        
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--gray-500);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .modal-close:hover {
            color: var(--gray-700);
        }
        
        .modal-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }
        
        .modal-footer {
            padding: 20px;
            border-top: 1px solid var(--gray-200);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        /* Preview Styles */
        .preview-section {
            margin-bottom: 20px;
            background-color: var(--gray-50);
            padding: 15px;
            border-radius: var(--radius-md);
            border: 1px solid var(--gray-200);
        }
        
        .preview-section h4 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
            color: var(--gray-800);
            border-bottom: 1px solid var(--gray-300);
            padding-bottom: 8px;
        }
        
        .preview-field {
            margin-bottom: 10px;
            display: flex;
        }
        
        .preview-label {
            font-weight: 500;
            color: var(--gray-700);
            width: 150px;
            flex-shrink: 0;
        }
        
        .preview-value {
            color: var(--gray-900);
        }
        
        /* Toast Notification System */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            gap: 10px;
            max-width: 350px;
        }
        
        .toast {
            padding: 16px;
            border-radius: var(--radius-md);
            background-color: white;
            color: var(--gray-700);
            box-shadow: var(--shadow-lg);
            animation: slideIn 0.3s ease-out forwards;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 10px;
            border-left: 4px solid var(--gray-500);
        }
        
        .toast.success {
            border-left-color: var(--success);
        }
        
        .toast.error {
            border-left-color: var(--danger);
        }
        
        .toast.warning {
            border-left-color: var(--warning);
        }
        
        .toast.info {
            border-left-color: var(--info);
        }
        
        .toast-icon {
            font-size: 24px;
            flex-shrink: 0;
        }
        
        .toast.success .toast-icon {
            color: var(--success);
        }
        
        .toast.error .toast-icon {
            color: var(--danger);
        }
        
        .toast.warning .toast-icon {
            color: var(--warning);
        }
        
        .toast.info .toast-icon {
            color: var(--info);
        }
        
        .toast-content {
            flex: 1;
        }
        
        .toast-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: var(--gray-900);
        }
        
        .toast-message {
            font-size: 13px;
        }
        
        .toast-close {
            background: none;
            border: none;
            font-size: 18px;
            color: var(--gray-500);
            cursor: pointer;
            transition: var(--transition);
            padding: 4px;
            margin-left: auto;
            flex-shrink: 0;
        }
        
        .toast-close:hover {
            color: var(--gray-700);
        }
        
        .toast-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background-color: rgba(0, 0, 0, 0.1);
            width: 100%;
        }
        
        .toast-progress-bar {
            height: 100%;
            background-color: var(--gray-500);
            width: 100%;
            animation: shrink 5s linear forwards;
        }
        
        .toast.success .toast-progress-bar {
            background-color: var(--success);
        }
        
        .toast.error .toast-progress-bar {
            background-color: var(--danger);
        }
        
        .toast.warning .toast-progress-bar {
            background-color: var(--warning);
        }
        
        .toast.info .toast-progress-bar {
            background-color: var(--info);
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes shrink {
            from {
                width: 100%;
            }
            to {
                width: 0;
            }
        }
        
        /* Notification badge pulse animation */
        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }
        
        .pulse {
            animation: pulse 1.5s infinite;
        }
        
        /* Notification Items */
        .notification-item {
            display: flex;
            align-items: flex-start;
            padding: 15px;
            border-bottom: 1px solid var(--gray-200);
            transition: var(--transition);
            cursor: pointer;
        }
        
        .notification-item:last-child {
            border-bottom: none;
        }
        
        .notification-item:hover {
            background-color: var(--gray-50);
        }
        
        .notification-item.unread {
            background-color: rgba(59, 130, 246, 0.05);
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
            font-size: 20px;
        }
        
        .notification-icon.success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }
        
        .notification-icon.warning {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning);
        }
        
        .notification-icon.error {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
        }
        
        .notification-icon.info {
            background-color: rgba(6, 182, 212, 0.1);
            color: var(--info);
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: var(--gray-900);
        }
        
        .notification-item.unread .notification-title {
            color: var(--primary);
        }
        
        .notification-message {
            font-size: 13px;
            color: var(--gray-700);
            margin-bottom: 4px;
        }
        
        .notification-time {
            font-size: 12px;
            color: var(--gray-500);
        }
        
        .table-actions {
            display: flex;
            gap: 8px;
        }
        
        /* Status Badges */
        .status-badge {
            padding: 6px 12px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            white-space: nowrap;
        }
        
        .status-pending {
            background-color: var(--warning-light, rgba(245, 158, 11, 0.1));
            color: var(--warning);
        }
        
        .status-approved {
            background-color: var(--success-light, rgba(16, 185, 129, 0.1));
            color: var(--success);
        }
        
        .status-rejected {
            background-color: var(--danger-light, rgba(239, 68, 68, 0.1));
            color: var(--danger);
        }
        
        .status-expired {
            background-color: var(--gray-100);
            color: var(--gray-600);
        }
        
        /* Empty State */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 48px 24px;
            text-align: center;
        }
        
        .empty-icon {
            font-size: 48px;
            color: var(--gray-300);
            margin-bottom: 16px;
        }
        
        .empty-state h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 8px;
        }
        
        .empty-state p {
            color: var(--gray-500);
            margin-bottom: 24px;
        }
        
        /* Rejection comment styles */
        .rejection-comment {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            background-color: rgba(239, 68, 68, 0.05);
            padding: 8px 12px;
            border-radius: var(--radius-md);
            border-left: 3px solid var(--danger);
        }
        
        .rejection-comment i {
            color: var(--danger);
            font-size: 18px;
            margin-top: 2px;
        }
        
        .rejection-comment span {
            font-size: 13px;
            color: var(--gray-700);
            line-height: 1.4;
        }
        
        /* Validation message styles */
        .validation-message {
            color: var(--danger);
            font-size: 12px;
            margin-top: 4px;
            min-height: 18px;
        }
        
        /* Dashboard Stats Styles */
        .dashboard-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            flex: 1;
            min-width: 220px;
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            background-color: white;
        }
        
        .stat-card-body {
            display: flex;
            align-items: center;
            padding: 20px;
            gap: 16px;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card-body.primary {
            background-color: rgba(59, 130, 246, 0.1);
            border-left: 4px solid var(--primary);
        }
        
        .stat-card-body.success {
            background-color: rgba(16, 185, 129, 0.1);
            border-left: 4px solid var(--success);
        }
        
        .stat-card-body.warning {
            background-color: rgba(245, 158, 11, 0.1);
            border-left: 4px solid var(--warning);
        }
        
        .stat-card-body.danger {
            background-color: rgba(239, 68, 68, 0.1);
            border-left: 4px solid var(--danger);
        }
        
        .stat-card-body.info {
            background-color: rgba(6, 182, 212, 0.1);
            border-left: 4px solid var(--info);
        }
        
        .stat-card-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            border-radius: var(--radius-full);
            font-size: 24px;
            flex-shrink: 0;
        }
        
        .stat-card-body.primary .stat-card-icon {
            background-color: rgba(59, 130, 246, 0.15);
            color: var(--primary);
        }
        
        .stat-card-body.success .stat-card-icon {
            background-color: rgba(16, 185, 129, 0.15);
            color: var(--success);
        }
        
        .stat-card-body.warning .stat-card-icon {
            background-color: rgba(245, 158, 11, 0.15);
            color: var(--warning);
        }
        
        .stat-card-body.danger .stat-card-icon {
            background-color: rgba(239, 68, 68, 0.15);
            color: var(--danger);
        }
        
        .stat-card-body.info .stat-card-icon {
            background-color: rgba(6, 182, 212, 0.15);
            color: var(--info);
        }
        
        .stat-card-info {
            flex: 1;
        }
        
        .stat-card-title {
            font-size: 14px;
            color: var(--gray-500);
            margin-bottom: 4px;
        }
        
        .stat-card-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--gray-800);
        }
    </style>
</head>
<body>
    <!-- Sidebar Navigation -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <div class="logo-icon">
                    <img src="sonatrach-logo.png" alt="Logo Sonatrach" style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;"/>
                </div>
                <span class="logo-text">PT Pro</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class='bx bx-chevron-left'></i>
            </button>
        </div>
        <nav class="sidebar-menu">
            <div class="menu-group">
                <h3 class="menu-title">Navigation</h3>
                <ul class="menu-items">
                    <li class="menu-item">
                        <div class="menu-link active" onclick="showSection('mespermis')">
                            <i class='bx bxs-dashboard menu-icon'></i>
                            <span class="menu-text">Mes Permis</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('initiation')">
                            <i class='bx bxs-file-doc menu-icon'></i>
                            <span class="menu-text">Demande du permis</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('corrections')">
                            <i class='bx bxs-edit-alt menu-icon'></i>
                            <span class="menu-text">À Corriger</span>
                            <span class="menu-badge">2</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('brouillon')">
                            <i class='bx bxs-file menu-icon'></i>
                            <span class="menu-text">Brouillons</span>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="menu-group">
                <h3 class="menu-title">Compte</h3>
                <ul class="menu-items">
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('profile')">
                            <i class='bx bxs-user menu-icon'></i>
                            <span class="menu-text">Mon Profil</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('settings')">
                            <i class='bx bx-cog menu-icon'></i>
                            <span class="menu-text">Paramètres</span>
                        </div>
                    </li>
                </ul>
            </div>
        </nav>
        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">
                    JD
                    <span class="notification-badge">3</span>
                </div>
                <div class="user-info">
                    <div class="user-name">John Doe</div>
                    <div class="user-role">Initiateur</div>
                </div>
                <div class="user-actions">
                    <button class="action-btn" onclick="logout()">
                        <i class='bx bx-log-out'></i>
                    </button>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content Area -->
    <!-- Toast Notifications Container -->
    <div id="toastContainer" class="toast-container"></div>
    
    <main class="main-content">
        <!-- Top Navigation Bar -->
        <div class="topbar">
            <div class="topbar-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class='bx bx-menu'></i>
                </button>
                <div class="breadcrumbs">
                    <span class="breadcrumb-item">ACCUEIL</span>
                    <span class="breadcrumb-item active" id="page-title">INITIATION DU PERMIS DE TRAVAIL</span>
                </div>
            </div>
            <div class="topbar-actions">
                <div class="topbar-action">
                    <button class="action-btn-lg">
                        <i class='bx bx-bell'></i>
                        <span class="notification-badge">1</span>
                    </button>
                </div>
                <div class="topbar-action" style="cursor: pointer; position: relative;">
                    <div class="user-avatar" style="width: 32px; height: 32px; font-size: 12px; cursor: pointer;" onclick="toggleUserDropdown(); event.stopPropagation();">
                        JD
                    </div>
                    <div class="user-dropdown" id="userDropdown">
                        <div class="dropdown-header">
                            <div class="dropdown-title">Paramètres du compte</div>
                        </div>
                        <div class="dropdown-item" onclick="showSection('profile'); toggleUserDropdown()">
                            <i class='bx bx-user'></i>
                            <span>Mon profil</span>
                        </div>
                        <div class="dropdown-divider"></div>
                        <div class="dropdown-item" onclick="logout(); toggleUserDropdown()">
                            <i class='bx bx-log-out'></i>
                            <span>Déconnexion</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-wrapper">
            <!-- Initiation Section -->
            <section id="initiation" class="form-section">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Demande du Permis de Travail</h1>
                        <p class="content-subtitle">Remplissez le formulaire pour initier une nouvelle demande de permis de travail</p>
                    </div>
                </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <i class='bx bx-building label-icon'></i>
                                Unité/Zone <span class="required">*</span>
                            </label>
                            <input type="text" name="unitZone" required placeholder="Entrez l'unité ou zone"/>
                        </div>
                        <div class="form-group">
                            <label>
                                <i class='bx bx-cog label-icon'></i>
                                Installation/équipement <span class="required">*</span>
                            </label>
                            <input type="text" name="installationEquipment" required placeholder="Décrivez l'installation ou l'équipement"/>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>
                            <i class='bx bx-edit label-icon'></i>
                            Description du travail <span class="required">*</span>
                        </label>
                        <textarea name="workDescription" required placeholder="Décrivez en détail le travail à effectuer" rows="4"></textarea>
                    </div>

                    <div class="form-group">
                        <label>
                            <i class='bx bx-calendar label-icon'></i>
                            Période prévisionnelle demandée <span class="required">*</span>
                        </label>
                        <div class="form-row-4">
                            <div>
                                <input type="date" name="dateFrom" id="dateFrom" required min="" />
                                <small style="color: var(--gray-500); font-size: 12px;">Date de début (min. 24h à l'avance)</small>
                                <div class="validation-message" id="dateFromError"></div>
                            </div>
                            <div>
                                <input type="date" name="dateTo" id="dateTo" required/>
                                <small style="color: var(--gray-500); font-size: 12px;">Date de fin (max. 7 jours)</small>
                                <div class="validation-message" id="dateToError"></div>
                            </div>
                            <div>
                                <input type="time" name="timeFrom" required/>
                                <small style="color: var(--gray-500); font-size: 12px;">Heure de début</small>
                            </div>
                            <div>
                                <input type="time" name="timeTo" required/>
                                <small style="color: var(--gray-500); font-size: 12px;">Heure de fin</small>
                            </div>
                        </div>
                    </div>



                    <!-- Section 3: Tools and Equipment -->
                    <div class="section-header" style="margin-top: 20px;">
                        <i class='bx bx-wrench section-icon'></i>
                        <h2 class="section-title">Outilage/Équipement utilisé</h2>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>Outillage manuel</th>
                                <th>Machines outils</th>
                                <th>Véhicules/engins</th>
                                <th>Équipement de levage</th>
                                <th>Travaux en hauteur</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="text" name="toolsManual" placeholder="Outils manuels"/></td>
                                <td><input type="text" name="toolsMachines" placeholder="Machines"/></td>
                                <td><input type="text" name="toolsVehicles" placeholder="Véhicules"/></td>
                                <td><input type="text" name="toolsLifting" placeholder="Équipement levage"/></td>
                                <td><input type="text" name="toolsHeight" placeholder="Équipement hauteur"/></td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Section 4: Execution Details -->
                    <div class="section-header">
                        <i class='bx bx-group section-icon'></i>
                        <h2 class="section-title">Exécution</h2>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <i class='bx bx-user-check label-icon'></i>
                                Responsable d'exécution <span class="required">*</span>
                            </label>
                            <select name="executionResponsible" required>
                                <option value="">Sélectionner un responsable</option>
                                <option value="jean-dupont">Jean Dupont</option>
                                <option value="marie-martin">Marie Martin</option>
                                <option value="pierre-durand">Pierre Durand</option>
                                <option value="autre">Autre</option>
                            </select>
                        </div>
                    </div>

                    <!-- Section 5: Initiator Information -->
                    <div class="section-header">
                        <i class='bx bx-user section-icon'></i>
                        <h2 class="section-title">L'initiateur de permis</h2>
                    </div>

                    <div class="form-row-4">
                        <div class="form-group">
                            <label>
                                <i class='bx bx-user label-icon'></i>
                                Nom
                            </label>
                            <input type="text" value="John Doe" readonly/>
                        </div>
                        <div class="form-group">
                            <label>
                                <i class='bx bx-briefcase label-icon'></i>
                                Fonction
                            </label>
                            <input type="text" value="Initiateur" readonly/>
                        </div>
                        <div class="form-group">
                            <label>
                                <i class='bx bx-calendar label-icon'></i>
                                Date <span class="required">*</span>
                            </label>
                            <input type="date" name="initiationDate" required/>
                        </div>
                        <div class="form-group">
                            <label>
                                <i class='bx bx-time label-icon'></i>
                                Heure <span class="required">*</span>
                            </label>
                            <input type="time" name="initiationTime" required/>
                        </div>
                    </div>

                    <!-- Submit Message -->
                    <div id="submitMessage" class="hidden"></div>

                    <!-- Submit Buttons -->
                    <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 32px; padding-top: 24px; border-top: 1px solid var(--gray-200);">
                        <button type="button" class="btn btn-secondary" id="draftBtn">
                            <i class='bx bx-edit-alt btn-icon'></i>
                            Enregistrer comme brouillon
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class='bx bx-send btn-icon'></i>
                            Soumettre la demande
                        </button>
                    </div>
                </form>
            </section>
            
            <!-- Settings Section -->
            <section id="settings" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Paramètres de compte</h1>
                        <p class="content-subtitle">Gérez vos préférences de notification et les paramètres de compte</p>
                    </div>
                </div>

                <div class="profile-section">
                    <div class="profile-section-header">
                        <h3 class="profile-section-title">Préférences de notification</h3>
                    </div>
                    <div class="profile-section-body">
                        <form id="notificationForm">
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked id="emailNotif">
                                    <span>Notifications par email</span>
                                </label>
                                <p class="form-text">Recevez des emails pour les mises à jour importantes</p>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked id="permitNotif">
                                    <span>Notifications de permis</span>
                                </label>
                                <p class="form-text">Soyez notifié du statut de vos demandes de permis</p>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked id="statusNotif">
                                    <span>Mises à jour de statut</span>
                                </label>
                                <p class="form-text">Recevez des notifications quand le statut d'un permis change</p>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="reminderNotif">
                                    <span>Rappels</span>
                                </label>
                                <p class="form-text">Recevez des rappels pour les permis en attente d'action</p>
                            </div>
                            
                            <div class="profile-actions">
                                <button type="submit" class="btn btn-primary" id="saveNotifBtn">
                                    <i class="bx bx-save btn-icon"></i>Enregistrer les préférences
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </section>

            <!-- Mes Permis Section -->
            <section id="mespermis" class="form-section">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Mes Permis de Travail</h1>
                        <p class="content-subtitle">Consultez tous les permis que vous avez soumis et suivez leur statut actuel</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-outline" onclick="refreshPermits()" title="Actualiser">
                            <i class='bx bx-refresh btn-icon'></i>
                            Actualiser
                        </button>
                        <button class="btn btn-primary" onclick="showSection('initiation')">
                            <i class='bx bx-plus btn-icon'></i>
                            Nouveau permis
                        </button>
                    </div>
                </div>
                    
                    <!-- Statistics Cards -->
                    <div class="stats-container">
                        <div class="stats-card">
                            <div class="stats-icon" style="background-color: rgba(59, 130, 246, 0.1); color: var(--primary);">
                                <i class='bx bxs-dashboard'></i>
                            </div>
                            <div class="stats-content">
                                <div class="stats-title">Total des permis</div>
                                <div class="stats-value" id="totalPermitsCount">0</div>
                                <div class="stats-change increase">
                                    <i class='bx bx-trending-up'></i>
                                    <span>+12% ce mois</span>
                                </div>
                            </div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-icon" style="background-color: rgba(245, 158, 11, 0.1); color: var(--warning);">
                                <i class='bx bxs-time'></i>
                            </div>
                            <div class="stats-content">
                                <div class="stats-title">En attente</div>
                                <div class="stats-value" id="pendingPermitsCount">0</div>
                                <div class="stats-change">
                                    <i class='bx bx-minus'></i>
                                    <span>Stable</span>
                                </div>
                            </div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-icon" style="background-color: rgba(16, 185, 129, 0.1); color: var(--success);">
                                <i class='bx bxs-check-circle'></i>
                            </div>
                            <div class="stats-content">
                                <div class="stats-title">Approuvés</div>
                                <div class="stats-value" id="approvedPermitsCount">0</div>
                                <div class="stats-change increase">
                                    <i class='bx bx-trending-up'></i>
                                    <span>+8% ce mois</span>
                                </div>
                            </div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-icon" style="background-color: rgba(239, 68, 68, 0.1); color: var(--danger);">
                                <i class='bx bxs-x-circle'></i>
                            </div>
                            <div class="stats-content">
                                <div class="stats-title">Rejetés</div>
                                <div class="stats-value" id="rejectedPermitsCount">0</div>
                                <div class="stats-change decrease">
                                    <i class='bx bx-trending-down'></i>
                                    <span>-3% ce mois</span>
                                </div>
                            </div>
                        </div>
                    </div>
                        
                <!-- Filters and Search -->
                <div class="dashboard-card" style="margin-bottom: 24px;">
                    <div class="card-header">
                        <h3 class="card-title">Filtres et Recherche</h3>
                    </div>
                    <div style="padding: 24px;">
                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-filter label-icon'></i>
                                    Filtrer par statut
                                </label>
                                <select id="statusFilter">
                                    <option value="all">Tous les statuts</option>
                                    <option value="pending">En attente</option>
                                    <option value="approved">Approuvé</option>
                                    <option value="rejected">Rejeté</option>
                                    <option value="expired">Expiré</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-sort label-icon'></i>
                                    Trier par
                                </label>
                                <select id="sortOrder">
                                    <option value="recent">Plus récent</option>
                                    <option value="oldest">Plus ancien</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-search label-icon'></i>
                                    Rechercher
                                </label>
                                <div style="display: flex; gap: 12px;">
                                    <input type="search" id="permitSearch" placeholder="Rechercher par titre, lieu, type..." 
                                           style="flex: 1;" onkeyup="if(event.key==='Enter') searchPermits(this.value)">
                                    <button type="button" class="btn btn-outline" onclick="searchPermits(document.getElementById('permitSearch').value)" title="Rechercher">
                                        <i class='bx bx-search'></i>
                                    </button>
                                    <button type="button" class="btn btn-outline" onclick="clearSearch()" title="Effacer">
                                        <i class='bx bx-x'></i>
                                    </button>
                                </div>
                            </div>
                            <div class="form-group">
                                <label style="opacity: 0;">Actions</label>
                                <div style="display: flex; gap: 12px;">
                                    <button class="btn btn-outline" onclick="exportPermits()" title="Exporter">
                                        <i class='bx bx-export btn-icon'></i>
                                        Exporter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                        
                <!-- Permits Table -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Liste des Permis</h3>
                    </div>
                    <div style="padding: 0;">
                        <div class="table-container">
                            <table class="data-table" id="permitsTable">
                                <thead>
                                    <tr>
                                        <th>N° Permis</th>
                                        <th>Date de soumission</th>
                                        <th>Type de permis</th>
                                        <th>Lieu</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="permitsTableBody">
                                    <!-- Sample permits with actions -->
                                    <tr>
                                        <td><strong>#PT2024001</strong></td>
                                        <td>15/01/2024</td>
                                        <td><span class="badge badge-info">Permis de travail général</span></td>
                                        <td>Zone A - Atelier mécanique</td>
                                        <td><span class="status-badge status-approved">Approuvé</span></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="action-btn view-btn" title="Voir les détails">
                                                    <i class='bx bx-show'></i>
                                                </button>
                                                <button class="action-btn download-btn" title="Télécharger PDF">
                                                    <i class='bx bx-download'></i>
                                                </button>
                                                <button class="action-btn edit-btn" title="Modifier">
                                                    <i class='bx bx-edit'></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>#PT2024002</strong></td>
                                        <td>18/01/2024</td>
                                        <td><span class="badge badge-warning">Permis de travail en hauteur</span></td>
                                        <td>Zone B - Tour de refroidissement</td>
                                        <td><span class="status-badge status-pending">En attente</span></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="action-btn view-btn" title="Voir les détails">
                                                    <i class='bx bx-show'></i>
                                                </button>
                                                <button class="action-btn edit-btn" title="Modifier">
                                                    <i class='bx bx-edit'></i>
                                                </button>
                                                <button class="action-btn delete-btn" title="Supprimer">
                                                    <i class='bx bx-trash'></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>#PT2024003</strong></td>
                                        <td>20/01/2024</td>
                                        <td><span class="badge badge-danger">Permis électrique</span></td>
                                        <td>Zone C - Salle électrique</td>
                                        <td><span class="status-badge status-rejected">Rejeté</span></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="action-btn view-btn" title="Voir les détails">
                                                    <i class='bx bx-show'></i>
                                                </button>
                                                <button class="action-btn edit-btn" title="Corriger et renvoyer">
                                                    <i class='bx bx-edit-alt'></i>
                                                </button>
                                                <button class="action-btn delete-btn" title="Supprimer">
                                                    <i class='bx bx-trash'></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>#PT2024004</strong></td>
                                        <td>22/01/2024</td>
                                        <td><span class="badge badge-success">Permis d'excavation</span></td>
                                        <td>Zone D - Parking extérieur</td>
                                        <td><span class="status-badge status-in-progress">En cours</span></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="action-btn view-btn" title="Voir les détails">
                                                    <i class='bx bx-show'></i>
                                                </button>
                                                <button class="action-btn download-btn" title="Télécharger PDF">
                                                    <i class='bx bx-download'></i>
                                                </button>
                                                <button class="action-btn pause-btn" title="Suspendre">
                                                    <i class='bx bx-pause'></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>#PT2024005</strong></td>
                                        <td>25/01/2024</td>
                                        <td><span class="badge badge-secondary">Permis de levage</span></td>
                                        <td>Zone E - Cour principale</td>
                                        <td><span class="status-badge status-expired">Expiré</span></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="action-btn view-btn" title="Voir les détails">
                                                    <i class='bx bx-show'></i>
                                                </button>
                                                <button class="action-btn renew-btn" title="Renouveler">
                                                    <i class='bx bx-refresh'></i>
                                                </button>
                                                <button class="action-btn archive-btn" title="Archiver">
                                                    <i class='bx bx-archive'></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div id="noPermitsMessage" class="hidden">
                            <div class="empty-state">
                                <i class='bx bx-file-blank empty-icon'></i>
                                <h3>Aucun permis trouvé</h3>
                                <p>Vous n'avez pas encore soumis de permis de travail</p>
                                <button class="btn btn-primary" onclick="showSection('initiation')">
                                    <i class='bx bx-plus btn-icon'></i>
                                    Initier un nouveau permis
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                    </div>
                </div>
            </section>
            
            <!-- Corrections Section -->
            <section id="corrections" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Permis à Corriger</h1>
                        <p class="content-subtitle">Les permis suivants ont été rejetés par le coordinateur et nécessitent des corrections</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-outline" onclick="refreshCorrections()" title="Actualiser">
                            <i class='bx bx-refresh btn-icon'></i>
                            Actualiser
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-container">
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(245, 158, 11, 0.1); color: var(--warning);">
                            <i class='bx bxs-time-five'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">En attente de correction</div>
                            <div class="stats-value">2</div>
                            <div class="stats-change">
                                <i class='bx bx-minus'></i>
                                <span>Nécessite action</span>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(16, 185, 129, 0.1); color: var(--success);">
                            <i class='bx bxs-check-circle'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">Corrigés ce mois</div>
                            <div class="stats-value">5</div>
                            <div class="stats-change increase">
                                <i class='bx bx-trending-up'></i>
                                <span>+25% ce mois</span>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(239, 68, 68, 0.1); color: var(--danger);">
                            <i class='bx bxs-x-circle'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">Rejetés définitivement</div>
                            <div class="stats-value">1</div>
                            <div class="stats-change decrease">
                                <i class='bx bx-trending-down'></i>
                                <span>-50% ce mois</span>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(107, 114, 128, 0.1); color: var(--gray-600);">
                            <i class='bx bxs-hourglass'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">Temps moyen correction</div>
                            <div class="stats-value">2.5j</div>
                            <div class="stats-change">
                                <i class='bx bx-minus'></i>
                                <span>Stable</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Corrections Table -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Permis Nécessitant des Corrections</h3>
                    </div>
                    <div style="padding: 0;">
                        <div class="table-container">
                            <table class="data-table" id="correctionsTable">
                                <thead>
                                    <tr>
                                        <th>N° Permis</th>
                                        <th>Date de rejet</th>
                                        <th>Type de permis</th>
                                        <th>Commentaires</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="correctionsTableBody">
                                    <!-- Example rejected permits - would be loaded dynamically -->
                                    <tr>
                                        <td><strong>#PT7825</strong></td>
                                        <td>12/05/2023</td>
                                        <td>Travail à chaud</td>
                                        <td>
                                            <div class="rejection-comment">
                                                <i class='bx bx-comment-error'></i>
                                                <span>Information manquante concernant l'équipement de protection individuelle</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="btn-icon" onclick="correctPermit('PT7825')" title="Corriger">
                                                    <i class='bx bx-edit'></i>
                                                </button>
                                                <button class="btn-icon" onclick="viewPermitDetails('PT7825')" title="Voir les détails">
                                                    <i class='bx bx-show'></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>#PT7901</strong></td>
                                        <td>15/05/2023</td>
                                        <td>Travail en hauteur</td>
                                        <td>
                                            <div class="rejection-comment">
                                                <i class='bx bx-comment-error'></i>
                                                <span>Document d'analyse des risques incomplet. Veuillez préciser les mesures de prévention des chutes.</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="btn-icon" onclick="correctPermit('PT7901')" title="Corriger">
                                                    <i class='bx bx-edit'></i>
                                                </button>
                                                <button class="btn-icon" onclick="viewPermitDetails('PT7901')" title="Voir les détails">
                                                    <i class='bx bx-show'></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Brouillons Section -->
            <section id="brouillon" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Mes Brouillons</h1>
                        <p class="content-subtitle">Consultez et complétez vos permis enregistrés comme brouillons</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-outline" onclick="clearAllDrafts()" title="Supprimer tous les brouillons">
                            <i class='bx bx-trash btn-icon'></i>
                            Tout supprimer
                        </button>
                        <button class="btn btn-primary" onclick="showSection('initiation')">
                            <i class='bx bx-plus btn-icon'></i>
                            Nouveau permis
                        </button>
                    </div>
                </div>
                    
                <!-- Statistics Cards -->
                <div class="stats-container">
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(59, 130, 246, 0.1); color: var(--primary);">
                            <i class='bx bxs-file'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">Brouillons enregistrés</div>
                            <div class="stats-value" id="draftsCount">0</div>
                            <div class="stats-change">
                                <i class='bx bx-minus'></i>
                                <span>En attente</span>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(16, 185, 129, 0.1); color: var(--success);">
                            <i class='bx bxs-check-circle'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">Complétés ce mois</div>
                            <div class="stats-value">8</div>
                            <div class="stats-change increase">
                                <i class='bx bx-trending-up'></i>
                                <span>+33% ce mois</span>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(245, 158, 11, 0.1); color: var(--warning);">
                            <i class='bx bxs-time'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">Anciens (>30j)</div>
                            <div class="stats-value">2</div>
                            <div class="stats-change">
                                <i class='bx bx-minus'></i>
                                <span>À réviser</span>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(107, 114, 128, 0.1); color: var(--gray-600);">
                            <i class='bx bxs-hourglass'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">Temps moyen</div>
                            <div class="stats-value">5.2j</div>
                            <div class="stats-change">
                                <i class='bx bx-minus'></i>
                                <span>Stable</span>
                            </div>
                        </div>
                    </div>
                </div>
                        
                <!-- Filters and Search -->
                <div class="dashboard-card" style="margin-bottom: 24px;">
                    <div class="card-header">
                        <h3 class="card-title">Filtres et Recherche</h3>
                    </div>
                    <div style="padding: 24px;">
                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-search label-icon'></i>
                                    Rechercher
                                </label>
                                <input type="text" id="draftSearchInput" placeholder="Rechercher un brouillon..." onkeyup="filterDrafts()">
                            </div>
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-sort label-icon'></i>
                                    Trier par
                                </label>
                                <select id="draftSortOrder" onchange="loadMyDrafts()">
                                    <option value="recent">Plus récent</option>
                                    <option value="oldest">Plus ancien</option>
                                    <option value="title">Titre (A-Z)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Drafts Table -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Liste des Brouillons</h3>
                    </div>
                    <div style="padding: 0;">
                        <div class="table-container">
                            <table class="data-table" id="draftsTable">
                                <thead>
                                    <tr>
                                        <th>Titre</th>
                                        <th>Date d'enregistrement</th>
                                        <th>Type de permis</th>
                                        <th>Lieu</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="draftsTableBody">
                                    <!-- Drafts will be loaded dynamically -->
                                </tbody>
                            </table>
                        </div>
                        
                        <div id="noDraftsMessage" class="hidden">
                            <div class="empty-state">
                                <i class='bx bx-edit empty-icon'></i>
                                <h3>Aucun brouillon trouvé</h3>
                                <p>Vous n'avez pas de brouillons enregistrés</p>
                                <button class="btn btn-primary" onclick="showSection('initiation')">
                                    <i class='bx bx-plus btn-icon'></i>
                                    Initier un nouveau permis
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <section id="profile" class="hidden">
                <!-- Profile Header -->
                <div class="profile-header">
                    <div class="profile-info">
                        <div class="profile-avatar">JD</div>
                        <div class="profile-details">
                            <h1 class="profile-name">John Doe</h1>
                            <div class="profile-username">@johndoe</div>
                            <span class="profile-role">
                                <i class='bx bx-shield'></i>
                                Initiateur
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Profile Form -->
                <div class="profile-form">
                    <h2 class="form-section-title">
                        <i class='bx bx-user'></i>
                        Informations du Profil
                    </h2>
                    
                    <form id="profileForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-user label-icon'></i>
                                    Nom complet
                                </label>
                                <input type="text" value="John Doe" readonly/>
                            </div>
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-user label-icon'></i>
                                    Nom d'utilisateur
                                </label>
                                <input type="text" value="johndoe" readonly/>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-briefcase label-icon'></i>
                                    Fonction
                                </label>
                                <input type="text" value="Initiateur" readonly/>
                            </div>
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-envelope label-icon'></i>
                                    Email <span class="required">*</span>
                                </label>
                                <input type="email" id="userEmail" value="<EMAIL>" required placeholder="Entrez votre adresse email"/>
                            </div>
                        </div>

                        <!-- Success/Error Message -->
                        <div id="profileMessage" class="hidden"></div>

                        <!-- Submit Button -->
                        <div style="display: flex; justify-content: flex-end; margin-top: 24px;">
                            <button type="submit" class="btn btn-primary" id="profileSubmitBtn">
                                <i class='bx bx-save btn-icon'></i>
                                Mettre à jour l'email
                            </button>
                        </div>
                    </form>
                </div>
            </section>
        </div>
    </main>

    <script>
        // Global function to toggle user dropdown directly
        function toggleUserDropdown() {
            const dropdown = document.getElementById('userDropdown');
            if (dropdown.classList.contains('active')) {
                dropdown.classList.remove('active');
            } else {
                dropdown.classList.add('active');
                
                // Add a one-time event listener to close when clicking outside
                setTimeout(() => {
                    document.addEventListener('click', function closeDropdown(e) {
                        if (!dropdown.contains(e.target) && !e.target.closest('.user-avatar')) {
                            dropdown.classList.remove('active');
                            document.removeEventListener('click', closeDropdown);
                        }
                    });
                }, 10);
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // Add sample data for demonstration
            initializeDemoData();
            
            // Show Mes Permis as default section when the user connects
            showSection('mespermis');
            
            // Load my permits and drafts data
            loadMyPermits();
            loadMyDrafts();
            
            // Initialize current date and time
            const now = new Date();
            const currentDate = now.toISOString().split('T')[0];
            const currentTime = now.toTimeString().slice(0, 5);
            
            document.querySelector('input[name="initiationDate"]').value = currentDate;
            document.querySelector('input[name="initiationTime"]').value = currentTime;
            
            // Set up date validation rules
            const dateFrom = document.getElementById('dateFrom');
            const dateTo = document.getElementById('dateTo');
            
            // Calculate minimum start date (24 hours from now)
            const minDate = new Date();
            minDate.setDate(minDate.getDate() + 1); // Add 24 hours
            const minDateString = minDate.toISOString().split('T')[0];
            
            // Set minimum date attribute
            dateFrom.setAttribute('min', minDateString);
            
            // Validate dates when they change
            dateFrom.addEventListener('change', validateDates);
            dateTo.addEventListener('change', validateDates);
            
            function validateDates() {
                const start = new Date(dateFrom.value);
                const end = new Date(dateTo.value);
                const dateFromError = document.getElementById('dateFromError');
                const dateToError = document.getElementById('dateToError');
                
                // Reset validation messages
                dateFrom.setCustomValidity('');
                dateTo.setCustomValidity('');
                dateFromError.textContent = '';
                dateToError.textContent = '';
                
                // Check if start date is before minimum required date
                if (dateFrom.value && new Date(dateFrom.value) < minDate) {
                    const errorMsg = 'La date de début doit être au moins 24 heures à l\'avance';
                    dateFrom.setCustomValidity(errorMsg);
                    dateFromError.textContent = errorMsg;
                }
                
                // Check end date only if both dates are set
                if (dateFrom.value && dateTo.value) {
                    // Ensure end date is not before start date
                    if (end < start) {
                        const errorMsg = 'La date de fin ne peut pas être antérieure à la date de début';
                        dateTo.setCustomValidity(errorMsg);
                        dateToError.textContent = errorMsg;
                    } 
                    // Ensure the period doesn't exceed 7 days
                    else {
                        const diffTime = Math.abs(end - start);
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                        
                        if (diffDays > 7) {
                            const errorMsg = 'La période ne peut pas dépasser 7 jours';
                            dateTo.setCustomValidity(errorMsg);
                            dateToError.textContent = errorMsg;
                        }
                    }
                }
                
                // If start date changes, set minimum for end date
                if (dateFrom.value) {
                    dateTo.setAttribute('min', dateFrom.value);
                    
                    // Set maximum date (start date + 7 days)
                    const maxDate = new Date(dateFrom.value);
                    maxDate.setDate(maxDate.getDate() + 7);
                    const maxDateString = maxDate.toISOString().split('T')[0];
                    dateTo.setAttribute('max', maxDateString);
                }
                
                return dateFrom.validity.valid && dateTo.validity.valid;
            }
            
            // Initial validation
            validateDates();

            // Toggle sidebar on mobile
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.querySelector('.sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');

            menuToggle.addEventListener('click', function() {
                sidebar.classList.toggle('active');
            });

            // Toggle sidebar collapse/expand
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                const icon = sidebarToggle.querySelector('i');
                if (sidebar.classList.contains('collapsed')) {
                    icon.classList.remove('bx-chevron-left');
                    icon.classList.add('bx-chevron-right');
                } else {
                    icon.classList.remove('bx-chevron-right');
                    icon.classList.add('bx-chevron-left');
                }
            });

            // Initialize dropdowns
            const userDropdown = document.getElementById('userDropdown');
            if (userDropdown) {
                userDropdown.addEventListener('click', function(e) {
                    e.stopPropagation(); // Prevent clicks inside dropdown from closing it
                });
            }

            // Notification button action
            const notificationBtns = document.querySelectorAll('.action-btn-lg .bx-bell');
            notificationBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Create notification panel
                    const modal = document.createElement('div');
                    modal.className = 'modal active';
                    modal.innerHTML = `
                        <div class="modal-content" style="max-width: 500px;">
                            <div class="modal-header">
                                <h3>Notifications</h3>
                                <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                            </div>
                            <div class="modal-body">
                                <div class="notification-item unread">
                                    <div class="notification-icon success">
                                        <i class='bx bx-check-circle'></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">Permis approuvé</div>
                                        <div class="notification-message">Votre permis #PT7825 a été approuvé</div>
                                        <div class="notification-time">Il y a 2 heures</div>
                                    </div>
                                </div>
                                <div class="notification-item">
                                    <div class="notification-icon warning">
                                        <i class='bx bx-time'></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">Permis expire bientôt</div>
                                        <div class="notification-message">Votre permis #PT7901 expire dans 2 jours</div>
                                        <div class="notification-time">Hier</div>
                                    </div>
                                </div>
                                <div class="notification-item">
                                    <div class="notification-icon info">
                                        <i class='bx bx-info-circle'></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">Maintenance système</div>
                                        <div class="notification-message">Le système sera en maintenance le 15/06/2023</div>
                                        <div class="notification-time">Il y a 3 jours</div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button class="btn btn-secondary" onclick="markAllAsRead(); this.closest('.modal').remove();">
                                    <i class='bx bx-check-double'></i>
                                    Tout marquer comme lu
                                </button>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(modal);
                    
                    // Remove notification badge
                    const badge = document.querySelector('.notification-badge');
                    if (badge) {
                        badge.style.display = 'none';
                    }
                });
            });
            
            // Function to mark all notifications as read
            window.markAllAsRead = function() {
                showToast('Toutes les notifications ont été marquées comme lues', 'success');
            };

            // Form submission handling
            const initiationForm = document.getElementById('initiationForm');
            const submitBtn = document.getElementById('submitBtn');
            const submitMessage = document.getElementById('submitMessage');

            // Get draft button
            const draftBtn = document.getElementById('draftBtn');
            
            // Handle draft button click
            draftBtn.addEventListener('click', function() {
                // Validate form fields
                if (!initiationForm.checkValidity()) {
                    initiationForm.reportValidity();
                    return;
                }
                
                draftBtn.disabled = true;
                draftBtn.innerHTML = '<i class="bx bx-loader-alt bx-spin btn-icon"></i>Enregistrement...';
                
                // Get form data
                const formData = new FormData(initiationForm);
                const draftData = {};
                
                // Convert FormData to object
                for (let [key, value] of formData.entries()) {
                    draftData[key] = value;
                }
                
                // Add metadata
                draftData.createdAt = new Date().toISOString();
                draftData.id = 'draft-' + Date.now();
                draftData.title = draftData.workTitle || 'Brouillon sans titre';
                
                // Get existing drafts from localStorage or initialize empty array
                const existingDrafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
                existingDrafts.push(draftData);
                
                // Save updated drafts to localStorage
                localStorage.setItem('permitDrafts', JSON.stringify(existingDrafts));
                
                // Simulate API call
                setTimeout(() => {
                    submitMessage.className = 'message message-success';
                    submitMessage.innerHTML = '<i class="bx bx-check-circle"></i>Brouillon enregistré avec succès !';
                    submitMessage.classList.remove('hidden');
                    
                    setTimeout(() => {
                        submitMessage.classList.add('hidden');
                        showSection('brouillon');
                        loadMyDrafts(); // Refresh drafts list
                    }, 1500);
                    
                    draftBtn.disabled = false;
                    draftBtn.innerHTML = '<i class="bx bx-edit-alt btn-icon"></i>Enregistrer comme brouillon';
                }, 1000);
            });
            
            // Handle form submission
            initiationForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                // Perform additional validation
                validateDates();
                
                // Check if the form is valid
                if (!initiationForm.checkValidity()) {
                    initiationForm.reportValidity();
                    return;
                }
                
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="bx bx-loader-alt bx-spin btn-icon"></i>Soumission en cours...';
                
                // Get form data
                const formData = new FormData(initiationForm);
                const permitData = {};
                
                // Convert FormData to object
                for (let [key, value] of formData.entries()) {
                    permitData[key] = value;
                }
                
                // Check if this is a correction resubmission
                const isCorrection = initiationForm.dataset.correctingPermitId;
                
                // Add metadata
                permitData.createdAt = new Date().toISOString();
                permitData.id = isCorrection ? initiationForm.dataset.correctingPermitId : 'permit-' + Date.now();
                permitData.status = 'pending'; // Initial status is pending
                permitData.title = permitData.workTitle || 'Permis sans titre';
                
                if (isCorrection) {
                    // Add correction metadata
                    permitData.corrected = true;
                    permitData.correctionDate = new Date().toISOString();
                    
                    // Remove from corrections list (would be handled by the backend in a real app)
                    // For demo purposes, we'll just simulate this
                    console.log(`Correction submitted for permit ID: ${permitData.id}`);
                    
                    // Clean up the form
                    delete initiationForm.dataset.correctingPermitId;
                }
                
                // Get existing permits from localStorage or initialize empty array
                const existingPermits = JSON.parse(localStorage.getItem('myPermits') || '[]');
                
                // If this is a correction, replace the existing permit
                if (isCorrection) {
                    const index = existingPermits.findIndex(p => p.id === permitData.id);
                    if (index !== -1) {
                        existingPermits[index] = permitData;
                    } else {
                        existingPermits.push(permitData);
                    }
                } else {
                    existingPermits.push(permitData);
                }
                
                // Save updated permits to localStorage
                localStorage.setItem('myPermits', JSON.stringify(existingPermits));
                
                // Simulate API call
                try {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    submitMessage.className = 'message message-success';
                    submitMessage.innerHTML = '<i class="bx bx-check-circle"></i>Demande de permis soumise avec succès !';
                    submitMessage.classList.remove('hidden');
                    
                    // Reset form after 3 seconds
                    setTimeout(() => {
                        initiationForm.reset();
                        document.querySelector('input[name="initiationDate"]').value = currentDate;
                        document.querySelector('input[name="initiationTime"]').value = currentTime;
                        submitMessage.classList.add('hidden');
                        showSection('mespermis');
                        loadMyPermits(); // Refresh permits list
                    }, 1500);
                    
                } catch (error) {
                    submitMessage.className = 'message message-error';
                    submitMessage.innerHTML = '<i class="bx bx-error-circle"></i>Erreur lors de la soumission de la demande';
                    submitMessage.classList.remove('hidden');
                } finally {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="bx bx-save btn-icon"></i>Soumettre la demande';
                }
            });

            // Profile form submission
            const profileForm = document.getElementById('profileForm');
            const profileSubmitBtn = document.getElementById('profileSubmitBtn');
            const profileMessage = document.getElementById('profileMessage');
            const userEmail = document.getElementById('userEmail');
            const originalEmail = userEmail.value;

            profileForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                if (userEmail.value === originalEmail) {
                    profileMessage.className = 'message message-error';
                    profileMessage.innerHTML = '<i class="bx bx-info-circle"></i>Aucune modification détectée';
                    profileMessage.classList.remove('hidden');
                    return;
                }
                
                profileSubmitBtn.disabled = true;
                profileSubmitBtn.innerHTML = '<i class="bx bx-loader-alt bx-spin btn-icon"></i>Mise à jour...';
                
                try {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    profileMessage.className = 'message message-success';
                    profileMessage.innerHTML = '<i class="bx bx-check-circle"></i>Email mis à jour avec succès !';
                    profileMessage.classList.remove('hidden');
                    
                    setTimeout(() => {
                        profileMessage.classList.add('hidden');
                    }, 3000);
                    
                } catch (error) {
                    profileMessage.className = 'message message-error';
                    profileMessage.innerHTML = '<i class="bx bx-error-circle"></i>Erreur lors de la mise à jour de l\'email';
                    profileMessage.classList.remove('hidden');
                } finally {
                    profileSubmitBtn.disabled = false;
                    profileSubmitBtn.innerHTML = '<i class="bx bx-save btn-icon"></i>Mettre à jour l\'email';
                }
            });
            
            // Notification settings form submission
            const notificationForm = document.getElementById('notificationForm');
            const saveNotifBtn = document.getElementById('saveNotifBtn');
            
            if (notificationForm) {
                notificationForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    saveNotifBtn.disabled = true;
                    saveNotifBtn.innerHTML = '<i class="bx bx-loader-alt bx-spin btn-icon"></i>Enregistrement...';
                    
                    try {
                        // Simulate API call
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        
                        alert('Préférences de notification enregistrées avec succès!');
                    } catch (error) {
                        alert('Erreur lors de l\'enregistrement des préférences. Veuillez réessayer.');
                    } finally {
                        saveNotifBtn.disabled = false;
                        saveNotifBtn.innerHTML = '<i class="bx bx-save btn-icon"></i>Enregistrer les préférences';
                    }
                });
            }

            // Responsive handling
            function handleResize() {
                if (window.innerWidth < 992) {
                    sidebar.classList.remove('collapsed');
                    sidebarToggle.style.display = 'none';
                    menuToggle.style.display = 'flex';
                } else {
                    menuToggle.style.display = 'none';
                    sidebarToggle.style.display = 'flex';
                }
            }

            window.addEventListener('resize', handleResize);
            handleResize();
        });

        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll("section").forEach(sec => sec.classList.add('hidden'));
            
            // Show selected section
            document.getElementById(sectionId).classList.remove('hidden');
            
            // Update page title
            const titles = {
                'initiation': 'INITIATION DU PERMIS DE TRAVAIL',
                'mespermis': 'MES PERMIS',
                'brouillon': 'MES BROUILLONS',
                'corrections': 'PERMIS À CORRIGER',
                'profile': 'MON PROFIL',
                'settings': 'PARAMÈTRES'
            };
            document.getElementById("page-title").textContent = titles[sectionId];
            
            // Update active menu item
            document.querySelectorAll(".menu-link").forEach(link => link.classList.remove('active'));
            document.querySelector(`.menu-link[onclick="showSection('${sectionId}')"]`).classList.add('active');
            
            // Update active link in sidebar
            document.querySelectorAll(".menu-link").forEach(link => {
                link.classList.remove("active");
            });
            if (event && event.currentTarget) {
                event.currentTarget.classList.add("active");
            }
            
            // Load data for specific sections
            if (sectionId === 'mespermis') {
                loadMyPermits();
            } else if (sectionId === 'brouillon') {
                loadMyDrafts();
            }
        }

        // Function to load user permits
        function loadMyPermits() {
            const permitsTableBody = document.getElementById('permitsTableBody');
            const noPermitsMessage = document.getElementById('noPermitsMessage');
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            
            // Update dashboard counters
            const totalPermitsCount = document.getElementById('totalPermitsCount');
            const pendingPermitsCount = document.getElementById('pendingPermitsCount');
            const approvedPermitsCount = document.getElementById('approvedPermitsCount');
            const rejectedPermitsCount = document.getElementById('rejectedPermitsCount');
            
            totalPermitsCount.textContent = permits.length;
            pendingPermitsCount.textContent = permits.filter(p => p.status === 'pending').length;
            approvedPermitsCount.textContent = permits.filter(p => p.status === 'approved').length;
            rejectedPermitsCount.textContent = permits.filter(p => p.status === 'rejected').length;
            
            if (permits.length === 0) {
                permitsTableBody.innerHTML = '';
                noPermitsMessage.classList.remove('hidden');
                return;
            }
            
            noPermitsMessage.classList.add('hidden');
            permitsTableBody.innerHTML = '';
            
            // Sort permits by date (newest first)
            permits.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
            
            // Apply filters (if active)
            const statusFilter = document.getElementById('statusFilter').value;
            let filteredPermits = permits;
            
            if (statusFilter !== 'all') {
                filteredPermits = permits.filter(permit => permit.status === statusFilter);
                
                if (filteredPermits.length === 0) {
                    permitsTableBody.innerHTML = '';
                    noPermitsMessage.classList.remove('hidden');
                    return;
                }
            }
            
            // Render permits to table
            filteredPermits.forEach(permit => {
                const statusClass = getStatusClass(permit.status);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><strong>#${permit.id.substring(7, 13)}</strong></td>
                    <td>${new Date(permit.createdAt).toLocaleDateString()}</td>
                    <td>${permit.permitType || 'Permis de travail'}</td>
                    <td>${permit.location || 'N/A'}</td>
                    <td><span class="status-badge ${statusClass}">${getStatusLabel(permit.status)}</span></td>
                    <td>
                        <div class="table-actions">
                            <button class="btn-icon" onclick="viewPermitDetails('${permit.id}')" title="Voir les détails">
                                <i class='bx bx-show'></i>
                            </button>
                            ${permit.status === 'rejected' ? `
                            <button class="btn-icon" onclick="correctPermit('${permit.id}')" title="Corriger et resoumettre">
                                <i class='bx bx-edit'></i>
                            </button>` : ''}
                            ${permit.status === 'approved' ? `
                            <button class="btn-icon" onclick="extendPermit('${permit.id}')" title="Demander une prolongation">
                                <i class='bx bx-time'></i>
                            </button>` : ''}
                            <button class="btn-icon" onclick="duplicatePermit('${permit.id}')" title="Dupliquer">
                                <i class='bx bx-duplicate'></i>
                            </button>
                            <button class="btn-icon" onclick="printPermit('${permit.id}')" title="Imprimer">
                                <i class='bx bx-printer'></i>
                            </button>
                            <button class="btn-icon" onclick="downloadPermitPDF('${permit.id}')" title="Télécharger PDF">
                                <i class='bx bx-download'></i>
                            </button>
                            ${permit.status !== 'pending' ? `
                            <button class="btn-icon" onclick="sharePermit('${permit.id}')" title="Partager">
                                <i class='bx bx-share-alt'></i>
                            </button>` : ''}
                        </div>
                    </td>
                `;
                permitsTableBody.appendChild(row);
            });
            
            // Add event listener to status filter if not already added
            const statusFilterEl = document.getElementById('statusFilter');
            if (!statusFilterEl.hasEventListener) {
                statusFilterEl.addEventListener('change', function() {
                    loadMyPermits();
                });
                statusFilterEl.hasEventListener = true;
            }
            
            const sortOrderEl = document.getElementById('sortOrder');
            if (!sortOrderEl.hasEventListener) {
                sortOrderEl.addEventListener('change', function() {
                    loadMyPermits();
                });
                sortOrderEl.hasEventListener = true;
            }
        }
        
        // Function to load drafts
        function loadMyDrafts() {
            const draftsTableBody = document.getElementById('draftsTableBody');
            const noDraftsMessage = document.getElementById('noDraftsMessage');
            const draftsCountEl = document.getElementById('draftsCount');
            const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
            
            // Update drafts count
            draftsCountEl.textContent = drafts.length;
            
            if (drafts.length === 0) {
                draftsTableBody.innerHTML = '';
                noDraftsMessage.classList.remove('hidden');
                return;
            }
            
            noDraftsMessage.classList.add('hidden');
            draftsTableBody.innerHTML = '';
            
            // Sort drafts based on selected option
            const sortOrder = document.getElementById('draftSortOrder').value;
            
            switch (sortOrder) {
                case 'recent':
                    drafts.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                    break;
                case 'oldest':
                    drafts.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
                    break;
                case 'title':
                    drafts.sort((a, b) => {
                        const titleA = (a.workTitle || 'Brouillon sans titre').toLowerCase();
                        const titleB = (b.workTitle || 'Brouillon sans titre').toLowerCase();
                        return titleA.localeCompare(titleB);
                    });
                    break;
                default:
                    drafts.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
            }
            
            drafts.forEach(draft => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${draft.workTitle || 'Brouillon sans titre'}</td>
                    <td>${new Date(draft.createdAt).toLocaleDateString()}</td>
                    <td>${draft.permitType || 'N/A'}</td>
                    <td>${draft.location || 'N/A'}</td>
                    <td>
                        <div class="table-actions">
                            <button class="btn-icon" onclick="editDraft('${draft.id}')" title="Modifier">
                                <i class='bx bx-edit-alt'></i>
                            </button>
                            <button class="btn-icon" onclick="previewDraft('${draft.id}')" title="Aperçu">
                                <i class='bx bx-show'></i>
                            </button>
                            <button class="btn-icon" onclick="submitDraftDirectly('${draft.id}')" title="Soumettre directement">
                                <i class='bx bx-send'></i>
                            </button>
                            <button class="btn-icon" onclick="copyDraft('${draft.id}')" title="Dupliquer">
                                <i class='bx bx-duplicate'></i>
                            </button>
                            <button class="btn-icon" onclick="exportDraftAsPDF('${draft.id}')" title="Exporter en PDF">
                                <i class='bx bx-export'></i>
                            </button>
                            <button class="btn-icon btn-danger" onclick="deleteDraft('${draft.id}')" title="Supprimer">
                                <i class='bx bx-trash'></i>
                            </button>
                        </div>
                    </td>
                `;
                draftsTableBody.appendChild(row);
            });
        }
        
        // Function to download permit as PDF
        window.downloadPermitPDF = function(permitId) {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }
            
            showToast(`Préparation du PDF pour le permis #${permitId.substring(7, 13)}...`, 'info');
            
            // Simulate PDF generation
            setTimeout(() => {
                showToast(`Le PDF pour le permis #${permitId.substring(7, 13)} a été téléchargé avec succès`, 'success');
            }, 1500);
            
            // In a real application, this would generate and download a PDF
        };
        
        // Function to print permit
        window.printPermit = function(permitId) {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }
            
            showToast(`Préparation de l'impression du permis #${permitId.substring(7, 13)}...`, 'info');
            
            // Simulate printing
            setTimeout(() => {
                showToast(`Le permis #${permitId.substring(7, 13)} a été envoyé à l'imprimante`, 'success');
            }, 1500);
        };
        
        // Function to duplicate a permit
        window.duplicatePermit = function(permitId) {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }
            
            // Create confirmation modal
            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 500px;">
                    <div class="modal-header">
                        <h3>Dupliquer le permis</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div style="display: flex; align-items: flex-start; margin-bottom: 20px;">
                            <i class='bx bx-duplicate' style="font-size: 24px; color: var(--primary); margin-right: 12px; margin-top: 3px;"></i>
                            <div>
                                <p>Vous êtes sur le point de créer une copie du permis #${permitId.substring(7, 13)} comme brouillon.</p>
                                <p style="margin-top: 10px">La copie sera créée comme brouillon que vous pourrez modifier avant de soumettre.</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove();">Annuler</button>
                        <button class="btn btn-primary" onclick="confirmDuplicatePermit('${permitId}'); this.closest('.modal').remove();">
                            <i class='bx bx-duplicate'></i>
                            Dupliquer
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };
        
        // Function to confirm and execute permit duplication
        window.confirmDuplicatePermit = function(permitId) {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }
            
            showToast(`Duplication du permis #${permitId.substring(7, 13)} en cours...`, 'info');
            
            // Simulate duplication delay
            setTimeout(() => {
                // Create draft from permit
                const draftId = 'draft-' + Date.now();
                const draft = {
                    ...permit,
                    id: draftId,
                    createdAt: new Date().toISOString(),
                    workTitle: (permit.workTitle || 'Permis sans titre') + ' (copie)',
                    status: undefined // Remove status as it's a draft
                };
                
                // Add to drafts
                const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
                drafts.push(draft);
                localStorage.setItem('permitDrafts', JSON.stringify(drafts));
                
                // Refresh drafts list
                loadMyDrafts();
                
                // Show success message
                showToast(`Le permis #${permitId.substring(7, 13)} a été dupliqué en brouillon avec succès`, 'success', 'Duplication réussie');
                
                // Switch to drafts view
                showSection('brouillon');
            }, 1500);
        };
        
        // Function to share a permit
        window.sharePermit = function(permitId) {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }
            
            // Create sharing modal
            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 500px;">
                    <div class="modal-header">
                        <h3>Partager le permis #${permitId.substring(7, 13)}</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>
                                <i class='bx bx-link label-icon'></i>
                                Lien de partage
                            </label>
                            <div style="display: flex; gap: 10px;">
                                <input type="text" value="https://permisdetravail.com/partage/${permitId}" id="shareLink" readonly class="form-control" style="flex: 1;">
                                <button class="btn btn-secondary" onclick="copyShareLink()">
                                    <i class='bx bx-copy'></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-group" style="margin-top: 20px;">
                            <label>
                                <i class='bx bx-envelope label-icon'></i>
                                Partager par email
                            </label>
                            <input type="email" placeholder="Entrez l'adresse email" class="form-control">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove();">Annuler</button>
                        <button class="btn btn-primary" onclick="sendShareEmail('${permitId}'); this.closest('.modal').remove();">
                            <i class='bx bx-envelope'></i>
                            Envoyer
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };
        
        // Function to copy share link
        window.copyShareLink = function() {
            const shareLink = document.getElementById('shareLink');
            shareLink.select();
            document.execCommand('copy');
            showToast('Lien copié dans le presse-papiers', 'success');
        };
        
        // Function to send share email
        window.sendShareEmail = function(permitId) {
            showToast(`Envoi du permis #${permitId.substring(7, 13)} par email...`, 'info');
            
            // Simulate sending email
            setTimeout(() => {
                showToast('Le permis a été partagé par email avec succès', 'success');
            }, 1500);
        };
        
        // Function to extend a permit
        window.extendPermit = function(permitId) {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }
            
            // Create extension modal
            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <div class="modal-header">
                        <h3>Demande de prolongation - Permis #${permitId.substring(7, 13)}</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>
                                <i class='bx bx-calendar label-icon'></i>
                                Date actuelle de fin
                            </label>
                            <input type="text" value="${permit.dateTo || 'Non spécifiée'}" readonly class="form-control">
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <i class='bx bx-calendar-plus label-icon'></i>
                                Nouvelle date de fin souhaitée <span class="required">*</span>
                            </label>
                            <input type="date" id="extensionDate" required class="form-control" min="${new Date().toISOString().split('T')[0]}">
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <i class='bx bx-edit label-icon'></i>
                                Motif de la prolongation <span class="required">*</span>
                            </label>
                            <textarea id="extensionReason" required placeholder="Veuillez expliquer pourquoi vous avez besoin d'une prolongation" rows="3" class="form-control"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove();">Annuler</button>
                        <button class="btn btn-primary" onclick="submitExtensionRequest('${permitId}'); this.closest('.modal').remove();">
                            <i class='bx bx-send'></i>
                            Soumettre la demande
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };
        
        // Function to submit extension request
        window.submitExtensionRequest = function(permitId) {
            const extensionDate = document.getElementById('extensionDate').value;
            const extensionReason = document.getElementById('extensionReason').value;
            
            if (!extensionDate || !extensionReason) {
                showToast('Veuillez remplir tous les champs obligatoires', 'error');
                return;
            }
            
            showToast('Soumission de votre demande de prolongation en cours...', 'info');
            
            // Simulate extension request
            setTimeout(() => {
                showToast('Votre demande de prolongation a été soumise avec succès', 'success', 'Demande envoyée');
                
                // In a real app, this would submit the extension request to the server
            }, 1500);
        };
        
        // Function to view permit details
        window.viewPermitDetails = function(permitId) {
            // Handle demo permits with string IDs
            if (typeof permitId === 'string' && permitId.startsWith('PT')) {
                // Demo permit, create mock data
                const demoPermit = {
                    id: 'permit-' + Date.now(),
                    permitNumber: permitId,
                    workTitle: 'Maintenance des vannes principales',
                    createdAt: new Date().toISOString(),
                    permitType: 'Travail à chaud',
                    location: 'Zone B - Bâtiment 3',
                    dateFrom: '2023-06-15',
                    dateTo: '2023-06-20',
                    timeFrom: '08:00',
                    timeTo: '17:00',
                    description: 'Maintenance préventive des vannes principales et inspection des conduites.',
                    status: permitId === 'PT7825' ? 'approved' : 'rejected',
                    requestor: 'Jean Dupont',
                    approver: 'Marie Lambert',
                    approvedDate: '2023-06-10',
                    rejectReason: permitId === 'PT7901' ? 'Documentation incomplète, veuillez fournir les spécifications techniques.' : '',
                    comments: [
                        {
                            user: 'Pierre Martin',
                            role: 'Superviseur',
                            date: '2023-06-09',
                            text: 'Veuillez confirmer que toutes les procédures de sécurité ont été suivies.'
                        },
                        {
                            user: 'Jean Dupont',
                            role: 'Initiateur',
                            date: '2023-06-09',
                            text: 'Toutes les procédures de sécurité ont été validées et documentées.'
                        }
                    ]
                };
                
                showPermitDetailsModal(demoPermit);
                return;
            }
            
            // Get real permit data
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }
            
            showPermitDetailsModal(permit);
        };
        
        // Function to display permit details in a modal
        function showPermitDetailsModal(permit) {
            // Format date
            const formatDate = (dateString) => {
                if (!dateString) return 'Non spécifié';
                const date = new Date(dateString);
                return date.toLocaleDateString('fr-FR', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            };
            
            // Get status info
            const statusClass = getStatusClass(permit.status);
            const statusLabel = getStatusLabel(permit.status);
            
            // Create the modal
            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 800px; width: 90%;">
                    <div class="modal-header">
                        <h3>Détails du permis #${permit.permitNumber || permit.id.substring(7, 13)}</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                        <div class="permit-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <div>
                                <span class="status-badge ${statusClass}" style="font-size: 14px;">${statusLabel}</span>
                                <h2 style="margin: 10px 0;">${permit.workTitle || 'Sans titre'}</h2>
                                <p style="color: var(--gray-600);">Soumis le ${formatDate(permit.createdAt)}</p>
                            </div>
                            <div>
                                <div class="qr-code" style="width: 80px; height: 80px; background-color: var(--gray-200); display: flex; align-items: center; justify-content: center;">
                                    <i class='bx bx-qr' style="font-size: 40px; color: var(--gray-700);"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="permit-details" style="margin-bottom: 30px;">
                            <h4 style="border-bottom: 1px solid var(--gray-200); padding-bottom: 10px; margin-bottom: 15px;">Informations générales</h4>
                            
                            <div class="detail-row" style="display: flex; margin-bottom: 15px;">
                                <div class="detail-col" style="flex: 1;">
                                    <div class="detail-item" style="margin-bottom: 15px;">
                                        <div class="detail-label" style="font-weight: 500; color: var(--gray-600); margin-bottom: 5px;">Type de permis</div>
                                        <div class="detail-value">${permit.permitType || 'Non spécifié'}</div>
                                    </div>
                                    <div class="detail-item" style="margin-bottom: 15px;">
                                        <div class="detail-label" style="font-weight: 500; color: var(--gray-600); margin-bottom: 5px;">Lieu</div>
                                        <div class="detail-value">${permit.location || 'Non spécifié'}</div>
                                    </div>
                                </div>
                                <div class="detail-col" style="flex: 1;">
                                    <div class="detail-item" style="margin-bottom: 15px;">
                                        <div class="detail-label" style="font-weight: 500; color: var(--gray-600); margin-bottom: 5px;">Date de début</div>
                                        <div class="detail-value">${permit.dateFrom || 'Non spécifié'}</div>
                                    </div>
                                    <div class="detail-item" style="margin-bottom: 15px;">
                                        <div class="detail-label" style="font-weight: 500; color: var(--gray-600); margin-bottom: 5px;">Date de fin</div>
                                        <div class="detail-value">${permit.dateTo || 'Non spécifié'}</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="detail-item" style="margin-bottom: 15px;">
                                <div class="detail-label" style="font-weight: 500; color: var(--gray-600); margin-bottom: 5px;">Description du travail</div>
                                <div class="detail-value" style="padding: 10px; background-color: var(--gray-50); border-radius: var(--radius-md);">
                                    ${permit.description || 'Aucune description fournie'}
                                </div>
                            </div>
                        </div>
                        
                        ${permit.status === 'rejected' ? `
                        <div class="permit-rejection" style="margin-bottom: 30px; background-color: rgba(239, 68, 68, 0.05); padding: 15px; border-radius: var(--radius-md); border-left: 4px solid var(--danger);">
                            <h4 style="color: var(--danger); margin-bottom: 10px;">Motif de rejet</h4>
                            <p>${permit.rejectReason || 'Aucun motif spécifié'}</p>
                        </div>
                        ` : ''}
                        
                        ${permit.status === 'approved' ? `
                        <div class="permit-approval" style="margin-bottom: 30px; background-color: rgba(16, 185, 129, 0.05); padding: 15px; border-radius: var(--radius-md); border-left: 4px solid var(--success);">
                            <h4 style="color: var(--success); margin-bottom: 10px;">Approbation</h4>
                            <p>Approuvé par: ${permit.approver || 'Non spécifié'}</p>
                            <p>Date d'approbation: ${formatDate(permit.approvedDate)}</p>
                        </div>
                        ` : ''}
                        
                        ${permit.comments && permit.comments.length > 0 ? `
                        <div class="permit-comments" style="margin-bottom: 30px;">
                            <h4 style="border-bottom: 1px solid var(--gray-200); padding-bottom: 10px; margin-bottom: 15px;">Commentaires</h4>
                            
                            ${permit.comments.map(comment => `
                            <div class="comment" style="margin-bottom: 15px; padding: 15px; border-radius: var(--radius-md); background-color: var(--gray-50);">
                                <div class="comment-header" style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                    <div class="comment-author">
                                        <strong>${comment.user}</strong> <span style="color: var(--gray-600);">(${comment.role})</span>
                                    </div>
                                    <div class="comment-date" style="color: var(--gray-600); font-size: 12px;">
                                        ${comment.date}
                                    </div>
                                </div>
                                <div class="comment-text">
                                    ${comment.text}
                                </div>
                            </div>
                            `).join('')}
                        </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove();">Fermer</button>
                        ${permit.status === 'approved' ? `
                        <button class="btn btn-primary" onclick="downloadPermitPDF('${permit.id}'); this.closest('.modal').remove();">
                            <i class='bx bx-download'></i>
                            Télécharger PDF
                        </button>
                        ` : ''}
                        ${permit.status === 'rejected' ? `
                        <button class="btn btn-primary" onclick="correctPermit('${permit.id}'); this.closest('.modal').remove();">
                            <i class='bx bx-edit'></i>
                            Corriger et resoumettre
                        </button>
                        ` : ''}
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }
        
        // Function to filter drafts based on search input
        function filterDrafts() {
            const searchInput = document.getElementById('draftSearchInput').value.toLowerCase();
            const rows = document.querySelectorAll('#draftsTableBody tr');
            
            rows.forEach(row => {
                const title = row.cells[0].textContent.toLowerCase();
                const type = row.cells[2].textContent.toLowerCase();
                const location = row.cells[3].textContent.toLowerCase();
                
                if (title.includes(searchInput) || type.includes(searchInput) || location.includes(searchInput)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        // Function to clear all drafts
        window.clearAllDrafts = function() {
            const draftsCount = JSON.parse(localStorage.getItem('permitDrafts') || '[]').length;
            
            if (draftsCount === 0) {
                showToast('Il n\'y a aucun brouillon à supprimer.', 'info');
                return;
            }
            
            // Create confirmation modal
            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 500px;">
                    <div class="modal-header">
                        <h3>Confirmation de suppression</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div style="display: flex; align-items: center; margin-bottom: 20px;">
                            <i class='bx bx-error-circle' style="font-size: 24px; color: var(--danger); margin-right: 12px;"></i>
                            <p>Êtes-vous sûr de vouloir supprimer tous les brouillons (${draftsCount})? Cette action est irréversible.</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove();">Annuler</button>
                        <button class="btn btn-danger" onclick="confirmClearAllDrafts(); this.closest('.modal').remove();">
                            <i class='bx bx-trash'></i>
                            Tout supprimer
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };
        
        // Function to confirm and execute clear all drafts
        window.confirmClearAllDrafts = function() {
            localStorage.setItem('permitDrafts', '[]');
            loadMyDrafts();
            showToast('Tous les brouillons ont été supprimés avec succès.', 'success');
        };
        
        // Function to delete a single draft
        window.deleteDraft = function(draftId) {
            const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
            const draft = drafts.find(d => d.id === draftId);
            
            if (!draft) {
                showToast('Brouillon introuvable', 'error');
                return;
            }
            
            // Create confirmation modal
            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 500px;">
                    <div class="modal-header">
                        <h3>Confirmation de suppression</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div style="display: flex; align-items: center; margin-bottom: 20px;">
                            <i class='bx bx-error-circle' style="font-size: 24px; color: var(--danger); margin-right: 12px;"></i>
                            <p>Êtes-vous sûr de vouloir supprimer le brouillon "${draft.workTitle || 'Sans titre'}"?</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove();">Annuler</button>
                        <button class="btn btn-danger" onclick="confirmDeleteDraft('${draftId}'); this.closest('.modal').remove();">
                            <i class='bx bx-trash'></i>
                            Supprimer
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };
        
        // Function to confirm and execute draft deletion
        window.confirmDeleteDraft = function(draftId) {
            const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
            const draft = drafts.find(d => d.id === draftId);
            const draftTitle = draft ? (draft.workTitle || 'Sans titre') : 'Brouillon';
            
            const updatedDrafts = drafts.filter(d => d.id !== draftId);
            localStorage.setItem('permitDrafts', JSON.stringify(updatedDrafts));
            loadMyDrafts();
            showToast(`Le brouillon "${draftTitle}" a été supprimé avec succès`, 'success');
        };
        
        // Function to preview a draft
        window.previewDraft = function(draftId) {
            const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
            const draft = drafts.find(d => d.id === draftId);
            
            if (!draft) {
                alert('Brouillon introuvable');
                return;
            }
            
            // Create and show a modal with the draft preview
            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="width: 800px; max-width: 90%;">
                    <div class="modal-header">
                        <h3>Aperçu du brouillon: ${draft.workTitle || 'Sans titre'}</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                        <div class="preview-section">
                            <h4>Informations générales</h4>
                            <div class="preview-field">
                                <span class="preview-label">Titre:</span>
                                <span class="preview-value">${draft.workTitle || 'Non spécifié'}</span>
                            </div>
                            <div class="preview-field">
                                <span class="preview-label">Type de permis:</span>
                                <span class="preview-value">${draft.permitType || 'Non spécifié'}</span>
                            </div>
                            <div class="preview-field">
                                <span class="preview-label">Lieu:</span>
                                <span class="preview-value">${draft.location || 'Non spécifié'}</span>
                            </div>
                            <div class="preview-field">
                                <span class="preview-label">Date de création:</span>
                                <span class="preview-value">${new Date(draft.createdAt).toLocaleString()}</span>
                            </div>
                            <div class="preview-field">
                                <span class="preview-label">Description:</span>
                                <span class="preview-value">${draft.description || 'Non spécifié'}</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove();">Fermer</button>
                        <button class="btn btn-primary" onclick="editDraft('${draft.id}'); this.closest('.modal').remove();">Modifier</button>
                        <button class="btn btn-success" onclick="submitDraftDirectly('${draft.id}'); this.closest('.modal').remove();">Soumettre</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };
        
        // Function to submit a draft directly
        window.submitDraftDirectly = function(draftId) {
            const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
            const draft = drafts.find(d => d.id === draftId);
            
            if (!draft) {
                showToast('Brouillon introuvable', 'error');
                return;
            }
            
            // Create confirmation modal
            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 500px;">
                    <div class="modal-header">
                        <h3>Confirmation de soumission</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div style="display: flex; align-items: flex-start; margin-bottom: 20px;">
                            <i class='bx bx-info-circle' style="font-size: 24px; color: var(--primary); margin-right: 12px; margin-top: 3px;"></i>
                            <div>
                                <p>Vous êtes sur le point de soumettre le brouillon "<strong>${draft.workTitle || 'Sans titre'}</strong>" comme permis de travail.</p>
                                <p style="margin-top: 10px">Une fois soumis, le brouillon sera supprimé et le permis sera envoyé pour approbation.</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove();">Annuler</button>
                        <button class="btn btn-primary" onclick="confirmSubmitDraft('${draftId}'); this.closest('.modal').remove();">
                            <i class='bx bx-send'></i>
                            Soumettre
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };
        
        // Function to confirm and execute draft submission
        window.confirmSubmitDraft = function(draftId) {
            const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
            const draft = drafts.find(d => d.id === draftId);
            
            if (!draft) {
                showToast('Brouillon introuvable', 'error');
                return;
            }
            
            showToast(`Soumission du brouillon "${draft.workTitle || 'Sans titre'}" en cours...`, 'info');
            
            // Simulate submission delay
            setTimeout(() => {
                // Convert draft to permit
                const permitId = 'permit-' + Date.now();
                const shortId = permitId.substring(7, 13);
                const permit = {
                    ...draft,
                    id: permitId,
                    status: 'pending',
                    createdAt: new Date().toISOString(),
                    submittedBy: 'Jean Dupont', // In a real app, this would be the current user
                    permitNumber: `PT${shortId}`
                };
                
                // Add to permits
                const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
                permits.push(permit);
                localStorage.setItem('myPermits', JSON.stringify(permits));
                
                // Remove from drafts
                const updatedDrafts = drafts.filter(d => d.id !== draftId);
                localStorage.setItem('permitDrafts', JSON.stringify(updatedDrafts));
                
                // Refresh both lists
                loadMyDrafts();
                loadMyPermits();
                
                // Show success message with permit number
                showToast(`Permis #PT${shortId} soumis avec succès`, 'success', 'Soumission réussie');
                
                // Switch to permits view
                showSection('mespermis');
            }, 1500);
        };
        
        // Function to export a draft as PDF
        window.exportDraftAsPDF = function(draftId) {
            const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
            const draft = drafts.find(d => d.id === draftId);
            
            if (!draft) {
                showToast('Brouillon introuvable', 'error');
                return;
            }
            
            showToast(`Préparation de l'exportation du brouillon "${draft.workTitle || 'Sans titre'}"...`, 'info');
            
            // Simulate PDF generation
            setTimeout(() => {
                showToast(`Le brouillon "${draft.workTitle || 'Sans titre'}" a été exporté en PDF avec succès`, 'success');
            }, 1500);
            
            // In a real application, this would generate and download a PDF
        };
        
        // Function to edit a draft
        window.editDraft = function(draftId) {
            const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
            const draft = drafts.find(d => d.id === draftId);
            
            if (!draft) {
                showToast('Brouillon introuvable', 'error');
                return;
            }
            
            // Show loading toast
            showToast(`Chargement du brouillon "${draft.workTitle || 'Sans titre'}"...`, 'info');
            
            setTimeout(() => {
                // Switch to initiation form
                showSection('initiation');
                
                // Populate form with draft data
                const form = document.getElementById('initiationForm');
                
                // Set form fields from draft data
                if (draft.workTitle) form.querySelector('input[name="workTitle"]').value = draft.workTitle;
                if (draft.location) form.querySelector('input[name="location"]').value = draft.location;
                if (draft.description) form.querySelector('textarea[name="workDescription"]').value = draft.description;
                if (draft.dateFrom) form.querySelector('input[name="dateFrom"]').value = draft.dateFrom;
                if (draft.dateTo) form.querySelector('input[name="dateTo"]').value = draft.dateTo;
                if (draft.timeFrom) form.querySelector('input[name="timeFrom"]').value = draft.timeFrom;
                if (draft.timeTo) form.querySelector('input[name="timeTo"]').value = draft.timeTo;
                
                // Set permit type checkboxes
                if (draft.permitType) {
                    const permitTypeInput = form.querySelector(`input[name="permitType"][value="${draft.permitType}"]`);
                    if (permitTypeInput) permitTypeInput.checked = true;
                }
                
                // Store the draft ID for update
                form.dataset.editingDraftId = draftId;
                
                // Update buttons text
                const submitBtn = form.querySelector('button[type="submit"]');
                const draftBtn = document.getElementById('draftBtn');
                submitBtn.innerHTML = '<i class="bx bx-send btn-icon"></i>Mettre à jour et soumettre';
                draftBtn.innerHTML = '<i class="bx bx-save btn-icon"></i>Mettre à jour le brouillon';
                
                // Show a success notification that we're editing a draft
                showToast(`Vous êtes en train d'éditer le brouillon "${draft.workTitle || 'Sans titre'}"`, 'success', 'Mode édition');
                
                // Scroll to top of form
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }, 500);
        };
        
        // Function to copy a draft
        window.copyDraft = function(draftId) {
            const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
            const draft = drafts.find(d => d.id === draftId);
            
            if (!draft) {
                showToast('Brouillon introuvable', 'error');
                return;
            }
            
            // Create a copy with a new ID
            const draftCopy = {...draft};
            draftCopy.id = 'draft-' + Date.now();
            draftCopy.createdAt = new Date().toISOString();
            draftCopy.workTitle = (draft.workTitle || 'Brouillon sans titre') + ' (copie)';
            
            // Add to drafts
            drafts.push(draftCopy);
            localStorage.setItem('permitDrafts', JSON.stringify(drafts));
            
            // Refresh drafts list
            loadMyDrafts();
            
            // Show success message
            showToast('Le brouillon a été dupliqué avec succès', 'success');
        };
        
        // Function to handle corrections to rejected permits
        window.correctPermit = function(permitId) {
            // In a real application, you would fetch the permit data from the server
            // For this demo, we'll use dummy data
            let permitData = {};
            
            if (permitId === 'PT7825') {
                permitData = {
                    id: 'PT7825',
                    workTitle: 'Réparation des conduites de vapeur',
                    permitType: 'Travail à chaud',
                    location: 'Zone B - Maintenance',
                    startDate: '2023-05-15',
                    endDate: '2023-05-16',
                    description: 'Soudure des conduites de vapeur endommagées',
                    rejectionReason: 'Information manquante concernant l\'équipement de protection individuelle',
                    rejectedBy: 'Marie Dupont, Coordinateur'
                };
            } else if (permitId === 'PT7901') {
                permitData = {
                    id: 'PT7901',
                    workTitle: 'Installation d\'éclairage au plafond',
                    permitType: 'Travail en hauteur',
                    location: 'Zone C - Production',
                    startDate: '2023-05-18',
                    endDate: '2023-05-19',
                    description: 'Installation de nouveaux luminaires LED au plafond',
                    rejectionReason: 'Document d\'analyse des risques incomplet. Veuillez préciser les mesures de prévention des chutes.',
                    rejectedBy: 'Marie Dupont, Coordinateur'
                };
            }
            
            // Show the correction interface
            showSection('initiation');
            
            // Fill the form with the permit data
            const initiationForm = document.getElementById('initiationForm');
            
            // Reset form first
            initiationForm.reset();
            
            // Set field values from the permit data
            if (permitData.workTitle) {
                initiationForm.elements['workTitle'].value = permitData.workTitle;
            }
            if (permitData.permitType) {
                initiationForm.elements['permitType'].value = permitData.permitType;
            }
            if (permitData.location) {
                initiationForm.elements['location'].value = permitData.location;
            }
            if (permitData.description) {
                initiationForm.elements['workDescription'].value = permitData.description;
            }
            
            // Show a correction message
            const submitMessage = document.getElementById('submitMessage');
            submitMessage.className = 'message message-warning';
            submitMessage.innerHTML = `
                <i class="bx bx-error-circle"></i>
                <div>
                    <strong>Correction requise</strong>
                    <p>${permitData.rejectionReason}</p>
                    <small>Rejeté par: ${permitData.rejectedBy}</small>
                </div>
            `;
            submitMessage.classList.remove('hidden');
            
            // Change the submit button text to reflect that it's a resubmission
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.innerHTML = '<i class="bx bx-send btn-icon"></i>Soumettre les corrections';
            
            // Store the permit ID for resubmission
            initiationForm.dataset.correctingPermitId = permitId;
        };
        
        // Toast notification system
        function showToast(message, type = 'info', title = '', duration = 5000) {
            const toastContainer = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            
            let iconClass = 'bx-info-circle';
            if (type === 'success') iconClass = 'bx-check-circle';
            if (type === 'error') iconClass = 'bx-x-circle';
            if (type === 'warning') iconClass = 'bx-error';
            
            // Default titles if not provided
            if (!title) {
                if (type === 'success') title = 'Succès';
                if (type === 'error') title = 'Erreur';
                if (type === 'warning') title = 'Attention';
                if (type === 'info') title = 'Information';
            }
            
            toast.innerHTML = `
                <div class="toast-icon">
                    <i class='bx ${iconClass}'></i>
                </div>
                <div class="toast-content">
                    <div class="toast-title">${title}</div>
                    <div class="toast-message">${message}</div>
                </div>
                <button class="toast-close" onclick="this.parentElement.remove()">
                    <i class='bx bx-x'></i>
                </button>
                <div class="toast-progress">
                    <div class="toast-progress-bar"></div>
                </div>
            `;
            
            toastContainer.appendChild(toast);
            
            // Remove toast after duration
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(50px)';
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 300);
            }, duration);
            
            // Make the notification badge pulse
            const notificationBadge = document.querySelector('.notification-badge');
            if (notificationBadge) {
                notificationBadge.classList.add('pulse');
                setTimeout(() => {
                    notificationBadge.classList.remove('pulse');
                }, 3000);
            }
            
            return toast;
        }
        
        // Initialize demo data
        function initializeDemoData() {
            // Only add demo data if no data exists
            if (!localStorage.getItem('myPermits')) {
                const demoPermits = [
                    {
                        id: 'permit-1683901234567',
                        createdAt: '2023-05-12T14:30:00.000Z',
                        workTitle: 'Réparation des conduites de vapeur',
                        permitType: 'Travail à chaud',
                        location: 'Zone B - Maintenance',
                        status: 'approved',
                        dateFrom: '2023-05-15',
                        dateTo: '2023-05-16',
                        description: 'Soudure des conduites de vapeur endommagées'
                    },
                    {
                        id: 'permit-1684165234567',
                        createdAt: '2023-05-15T09:45:00.000Z',
                        workTitle: 'Installation d\'éclairage au plafond',
                        permitType: 'Travail en hauteur',
                        location: 'Zone C - Production',
                        status: 'pending',
                        dateFrom: '2023-05-18',
                        dateTo: '2023-05-19',
                        description: 'Installation de nouveaux luminaires LED au plafond'
                    },
                    {
                        id: 'permit-1684251634567',
                        createdAt: '2023-05-16T10:20:00.000Z',
                        workTitle: 'Maintenance des équipements électriques',
                        permitType: 'Consignation électrique',
                        location: 'Zone A - Salle des machines',
                        status: 'rejected',
                        dateFrom: '2023-05-20',
                        dateTo: '2023-05-21',
                        description: 'Vérification et remplacement des disjoncteurs défectueux'
                    },
                    {
                        id: 'permit-1685547634567',
                        createdAt: '2023-05-31T15:30:00.000Z',
                        workTitle: 'Peinture des structures métalliques',
                        permitType: 'Permis de travail général',
                        location: 'Zone D - Extérieur',
                        status: 'approved',
                        dateFrom: '2023-06-05',
                        dateTo: '2023-06-09',
                        description: 'Application de peinture anticorrosion sur structures métalliques'
                    },
                    {
                        id: 'permit-1687275634567',
                        createdAt: '2023-06-20T08:15:00.000Z',
                        workTitle: 'Excavation pour pose de câbles',
                        permitType: 'Permis d\'Excavation',
                        location: 'Parking - Entrée principale',
                        status: 'pending',
                        dateFrom: '2023-06-25',
                        dateTo: '2023-06-28',
                        description: 'Creusement de tranchée pour pose de nouveaux câbles électriques'
                    }
                ];
                
                localStorage.setItem('myPermits', JSON.stringify(demoPermits));
            }
            
            // Add draft demo data if none exists
            if (!localStorage.getItem('permitDrafts')) {
                const demoDrafts = [
                    {
                        id: 'draft-1687707634567',
                        createdAt: '2023-06-25T11:45:00.000Z',
                        workTitle: 'Nettoyage des cuves de stockage',
                        permitType: 'Espace confiné',
                        location: 'Zone E - Stockage',
                        description: 'Nettoyage et inspection des cuves de stockage de produits chimiques'
                    },
                    {
                        id: 'draft-1687880434567',
                        createdAt: '2023-06-27T14:30:00.000Z',
                        workTitle: 'Remplacement des filtres de ventilation',
                        permitType: 'Permis de travail général',
                        location: 'Bâtiment Administratif',
                        description: 'Changement périodique des filtres du système de ventilation'
                    }
                ];
                
                localStorage.setItem('permitDrafts', JSON.stringify(demoDrafts));
            }
        }
        
        function logout() {
            // Create confirmation modal
            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 400px;">
                    <div class="modal-header">
                        <h3>Confirmation de déconnexion</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div style="display: flex; align-items: center; margin-bottom: 20px;">
                            <i class='bx bx-log-out' style="font-size: 24px; color: var(--primary); margin-right: 12px;"></i>
                            <p>Êtes-vous sûr de vouloir vous déconnecter ?</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove();">Annuler</button>
                        <button class="btn btn-primary" onclick="confirmLogout(); this.closest('.modal').remove();">
                            <i class='bx bx-log-out'></i>
                            Déconnexion
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }
        
        function confirmLogout() {
            showToast('Déconnexion en cours...', 'info');
            
            // Simulate logout process
            setTimeout(function() {
                window.location.href = 'login.html'; // Redirect to login page
            }, 1000);
        }
        // Function to handle corrections to rejected permits
        window.correctPermit = function(permitId) {
            // Handle demo permits
            if (typeof permitId === 'string' && permitId.startsWith('PT')) {
                // Show toast notification
                showToast(`Préparation du formulaire de correction pour le permis #${permitId}...`, 'info');
                
                // In a real application, you would fetch the permit data from the server
                // For this demo, we'll use dummy data
                let permitData = {};
                
                if (permitId === 'PT7825') {
                    permitData = {
                        id: 'PT7825',
                        workTitle: 'Réparation des conduites de vapeur',
                        permitType: 'Travail à chaud',
                        location: 'Zone B - Maintenance',
                        dateFrom: '2023-05-15',
                        dateTo: '2023-05-16',
                        description: 'Soudure des conduites de vapeur endommagées',
                        rejectionReason: 'Information manquante concernant l\'équipement de protection individuelle',
                        rejectedBy: 'Marie Dupont, Coordinateur'
                    };
                } else if (permitId === 'PT7901') {
                    permitData = {
                        id: 'PT7901',
                        workTitle: 'Installation des vannes de contrôle',
                        permitType: 'Travail électrique',
                        location: 'Zone A - Salle de contrôle',
                        dateFrom: '2023-05-20',
                        dateTo: '2023-05-21',
                        description: 'Installation et test des nouvelles vannes de contrôle automatisées',
                        rejectionReason: 'Spécifications techniques manquantes pour les vannes à installer',
                        rejectedBy: 'Thomas Martin, Superviseur'
                    };
                }
                
                // Create a modal with rejection information and guidance
                const modal = document.createElement('div');
                modal.className = 'modal active';
                modal.innerHTML = `
                    <div class="modal-content" style="max-width: 700px;">
                        <div class="modal-header">
                            <h3>Correction du permis rejeté #${permitId}</h3>
                            <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="rejection-info" style="background-color: rgba(239, 68, 68, 0.1); border-left: 4px solid var(--danger); padding: 15px; margin-bottom: 20px; border-radius: var(--radius-md);">
                                <h4 style="margin-bottom: 10px; color: var(--danger);">Motif du rejet</h4>
                                <p>${permitData.rejectionReason}</p>
                                <p style="margin-top: 10px; color: var(--gray-600); font-size: 13px;">Rejeté par: ${permitData.rejectedBy}</p>
                            </div>
                            
                            <div class="correction-guide">
                                <h4 style="margin-bottom: 15px;">Instructions pour la correction</h4>
                                <ol style="padding-left: 20px; margin-bottom: 20px;">
                                    <li style="margin-bottom: 10px;">Examinez attentivement le motif du rejet.</li>
                                    <li style="margin-bottom: 10px;">Apportez les corrections nécessaires au formulaire.</li>
                                    <li style="margin-bottom: 10px;">Soumettez à nouveau le permis pour approbation.</li>
                                </ol>
                                <p>Le formulaire sera pré-rempli avec les informations existantes.</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-secondary" onclick="this.closest('.modal').remove();">Annuler</button>
                            <button class="btn btn-primary" onclick="proceedWithCorrection('${permitId}'); this.closest('.modal').remove();">
                                <i class='bx bx-edit'></i>
                                Procéder à la correction
                            </button>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
                return;
            }
            
            // Handle real permits
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }
            
            if (permit.status !== 'rejected') {
                showToast('Seuls les permis rejetés peuvent être corrigés', 'warning');
                return;
            }
            
            // Create rejection info modal
            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 700px;">
                    <div class="modal-header">
                        <h3>Correction du permis rejeté #${permitId.substring(7, 13)}</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="rejection-info" style="background-color: rgba(239, 68, 68, 0.1); border-left: 4px solid var(--danger); padding: 15px; margin-bottom: 20px; border-radius: var(--radius-md);">
                            <h4 style="margin-bottom: 10px; color: var(--danger);">Motif du rejet</h4>
                            <p>${permit.rejectReason || 'Aucun motif spécifié'}</p>
                            <p style="margin-top: 10px; color: var(--gray-600); font-size: 13px;">Rejeté le: ${new Date(permit.rejectedDate || permit.updatedDate || permit.createdAt).toLocaleDateString()}</p>
                        </div>
                        
                        <div class="correction-guide">
                            <h4 style="margin-bottom: 15px;">Instructions pour la correction</h4>
                            <ol style="padding-left: 20px; margin-bottom: 20px;">
                                <li style="margin-bottom: 10px;">Examinez attentivement le motif du rejet.</li>
                                <li style="margin-bottom: 10px;">Apportez les corrections nécessaires au formulaire.</li>
                                <li style="margin-bottom: 10px;">Soumettez à nouveau le permis pour approbation.</li>
                            </ol>
                            <p>Le formulaire sera pré-rempli avec les informations existantes.</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove();">Annuler</button>
                        <button class="btn btn-primary" onclick="proceedWithCorrection('${permitId}'); this.closest('.modal').remove();">
                            <i class='bx bx-edit'></i>
                            Procéder à la correction
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };
        
        // Function to proceed with correction
        window.proceedWithCorrection = function(permitId) {
            showToast('Préparation du formulaire de correction...', 'info');
            
            // Get permit data
            let permitData = {};
            
            if (permitId.startsWith('PT')) {
                // Demo permit
                if (permitId === 'PT7825') {
                    permitData = {
                        workTitle: 'Réparation des conduites de vapeur',
                        permitType: 'Travail à chaud',
                        location: 'Zone B - Maintenance',
                        dateFrom: '2023-05-15',
                        dateTo: '2023-05-16',
                        description: 'Soudure des conduites de vapeur endommagées'
                    };
                } else if (permitId === 'PT7901') {
                    permitData = {
                        workTitle: 'Installation des vannes de contrôle',
                        permitType: 'Travail électrique',
                        location: 'Zone A - Salle de contrôle',
                        dateFrom: '2023-05-20',
                        dateTo: '2023-05-21',
                        description: 'Installation et test des nouvelles vannes de contrôle automatisées'
                    };
                }
            } else {
                // Real permit
                const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
                const permit = permits.find(p => p.id === permitId);
                
                if (permit) {
                    permitData = {
                        workTitle: permit.workTitle,
                        permitType: permit.permitType,
                        location: permit.location,
                        dateFrom: permit.dateFrom,
                        dateTo: permit.dateTo,
                        timeFrom: permit.timeFrom,
                        timeTo: permit.timeTo,
                        description: permit.description
                    };
                }
            }
            
            // Navigate to initiation form
            showSection('initiation');
            
            // Wait for form to be visible
            setTimeout(() => {
                // Populate form with permit data
                const form = document.getElementById('initiationForm');
                
                if (permitData.workTitle) form.querySelector('input[name="workTitle"]').value = permitData.workTitle;
                if (permitData.location) form.querySelector('input[name="location"]').value = permitData.location;
                if (permitData.description) form.querySelector('textarea[name="workDescription"]').value = permitData.description;
                if (permitData.dateFrom) form.querySelector('input[name="dateFrom"]').value = permitData.dateFrom;
                if (permitData.dateTo) form.querySelector('input[name="dateTo"]').value = permitData.dateTo;
                if (permitData.timeFrom) form.querySelector('input[name="timeFrom"]').value = permitData.timeFrom;
                if (permitData.timeTo) form.querySelector('input[name="timeTo"]').value = permitData.timeTo;
                
                // Set permit type
                if (permitData.permitType) {
                    const permitTypeInput = form.querySelector(`input[name="permitType"][value="${permitData.permitType}"]`);
                    if (permitTypeInput) permitTypeInput.checked = true;
                }
                
                // Store the permit ID for resubmission
                form.dataset.correctingPermitId = permitId;
                
                // Update submit button text
                const submitBtn = form.querySelector('button[type="submit"]');
                submitBtn.innerHTML = '<i class="bx bx-send btn-icon"></i>Corriger et soumettre';
                
                // Show notification
                showToast('Formulaire prêt pour correction', 'success', 'Mode correction');
                
                // Scroll to top of form
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }, 500);
        };

        // Additional utility functions for better UX
        
        // Function to refresh permits table
        window.refreshPermits = function() {
            showToast('Actualisation des permis...', 'info');
            setTimeout(() => {
                loadMyPermits();
                showToast('Permis actualisés', 'success');
            }, 1000);
        };

        // Function to export permits data
        window.exportPermits = function() {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            if (permits.length === 0) {
                showToast('Aucun permis à exporter', 'warning');
                return;
            }

            const dataStr = JSON.stringify(permits, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `mes_permis_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
            
            showToast('Export terminé', 'success');
        };

        // Function to search permits
        window.searchPermits = function(query) {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const filteredPermits = permits.filter(permit => 
                permit.workTitle.toLowerCase().includes(query.toLowerCase()) ||
                permit.location.toLowerCase().includes(query.toLowerCase()) ||
                permit.permitType.toLowerCase().includes(query.toLowerCase()) ||
                permit.description.toLowerCase().includes(query.toLowerCase())
            );
            
            // Update table with filtered results
            const permitsTableBody = document.getElementById('permitsTableBody');
            permitsTableBody.innerHTML = '';
            
            if (filteredPermits.length === 0) {
                permitsTableBody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: var(--gray-500);">Aucun permis trouvé</td></tr>';
                return;
            }
            
            filteredPermits.forEach(permit => {
                const statusClass = getStatusClass(permit.status);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><strong>#${permit.id.substring(7, 13)}</strong></td>
                    <td>${new Date(permit.createdAt).toLocaleDateString()}</td>
                    <td>${permit.permitType || 'Permis de travail'}</td>
                    <td>${permit.location || 'N/A'}</td>
                    <td><span class="status-badge ${statusClass}">${getStatusLabel(permit.status)}</span></td>
                    <td>
                        <div class="table-actions">
                            <button class="btn-icon" onclick="viewPermitDetails('${permit.id}')" title="Voir les détails">
                                <i class='bx bx-show'></i>
                            </button>
                            ${permit.status === 'rejected' ? `
                            <button class="btn-icon warning" onclick="correctPermit('${permit.id}')" title="Corriger et resoumettre">
                                <i class='bx bx-edit'></i>
                            </button>` : ''}
                            ${permit.status === 'approved' ? `
                            <button class="btn-icon info" onclick="extendPermit('${permit.id}')" title="Demander une prolongation">
                                <i class='bx bx-time'></i>
                            </button>` : ''}
                            <button class="btn-icon" onclick="duplicatePermit('${permit.id}')" title="Dupliquer">
                                <i class='bx bx-duplicate'></i>
                            </button>
                            <button class="btn-icon success" onclick="downloadPermitPDF('${permit.id}')" title="Télécharger PDF">
                                <i class='bx bx-download'></i>
                            </button>
                            ${permit.status !== 'pending' ? `
                            <button class="btn-icon info" onclick="sharePermit('${permit.id}')" title="Partager">
                                <i class='bx bx-share-alt'></i>
                            </button>` : ''}
                        </div>
                    </td>
                `;
                permitsTableBody.appendChild(row);
            });
        };

        // Function to clear search
        window.clearSearch = function() {
            const searchInput = document.querySelector('input[type="search"]');
            if (searchInput) {
                searchInput.value = '';
            }
            loadMyPermits();
        };

        // Function to handle keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+N for new permit
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                showSection('initiation');
                showToast('Nouveau permis', 'info');
            }
            
            // Ctrl+R for refresh
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                const currentSection = document.querySelector('section:not(.hidden)');
                if (currentSection && currentSection.id === 'mespermis') {
                    refreshPermits();
                }
            }
            
            // Escape to close modals
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => modal.remove());
            }
        });

        // Auto-save draft functionality
        let autoSaveTimer;
        function setupAutoSave() {
            const form = document.getElementById('initiationForm');
            if (!form) return;

            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    clearTimeout(autoSaveTimer);
                    autoSaveTimer = setTimeout(() => {
                        saveDraft(true); // true for auto-save
                    }, 30000); // Auto-save after 30 seconds of inactivity
                });
            });
        }

        // Enhanced save draft function
        function saveDraft(isAutoSave = false) {
            const form = document.getElementById('initiationForm');
            if (!form) return;

            const formData = new FormData(form);
            const draftData = {
                id: 'draft-' + Date.now(),
                createdAt: new Date().toISOString(),
                workTitle: formData.get('workTitle') || 'Brouillon sans titre',
                permitType: formData.get('permitType') || 'N/A',
                location: formData.get('location') || 'N/A',
                dateFrom: formData.get('dateFrom'),
                dateTo: formData.get('dateTo'),
                timeFrom: formData.get('timeFrom'),
                timeTo: formData.get('timeTo'),
                description: formData.get('workDescription'),
                executionResponsible: formData.get('executionResponsible'),
                isAutoSave: isAutoSave
            };

            // Only save if there's meaningful content
            if (!draftData.workTitle || draftData.workTitle === 'Brouillon sans titre') {
                if (!isAutoSave) {
                    showToast('Veuillez saisir au moins un titre pour sauvegarder', 'warning');
                }
                return;
            }

            const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
            drafts.push(draftData);
            localStorage.setItem('permitDrafts', JSON.stringify(drafts));

            const message = isAutoSave ? 'Brouillon sauvegardé automatiquement' : 'Brouillon sauvegardé';
            showToast(message, 'success');
        }

        // Initialize auto-save when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(setupAutoSave, 1000);
        });

        // Function to show confirmation dialogs
        window.showConfirmDialog = function(title, message, onConfirm, onCancel) {
            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 400px;">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove(); ${onCancel ? onCancel + '()' : ''}">Annuler</button>
                        <button class="btn btn-primary" onclick="this.closest('.modal').remove(); ${onConfirm}()">Confirmer</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };

        // ===== PERMIT ACTION FUNCTIONS =====

        // Function to view permit details
        window.viewPermitDetails = function(permitId) {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 800px;">
                    <div class="modal-header">
                        <h3>Détails du permis #${permit.id.substring(7, 13)}</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="permit-details">
                            <div class="detail-row">
                                <strong>Titre des travaux:</strong> ${permit.workTitle}
                            </div>
                            <div class="detail-row">
                                <strong>Type de permis:</strong> ${permit.permitType}
                            </div>
                            <div class="detail-row">
                                <strong>Lieu:</strong> ${permit.location}
                            </div>
                            <div class="detail-row">
                                <strong>Statut:</strong> <span class="status-badge ${getStatusClass(permit.status)}">${getStatusLabel(permit.status)}</span>
                            </div>
                            <div class="detail-row">
                                <strong>Date de début:</strong> ${permit.dateFrom}
                            </div>
                            <div class="detail-row">
                                <strong>Date de fin:</strong> ${permit.dateTo}
                            </div>
                            <div class="detail-row">
                                <strong>Description:</strong> ${permit.description || 'N/A'}
                            </div>
                            <div class="detail-row">
                                <strong>Date de création:</strong> ${new Date(permit.createdAt).toLocaleString()}
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove();">Fermer</button>
                        <button class="btn btn-primary" onclick="downloadPermitPDF('${permit.id}'); this.closest('.modal').remove();">
                            <i class='bx bx-download'></i> Télécharger PDF
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };

        // Function to download permit as PDF
        window.downloadPermitPDF = function(permitId) {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }

            showToast('Génération du PDF en cours...', 'info');
            
            // Simulate PDF generation
            setTimeout(() => {
                const pdfContent = `
PERMIS DE TRAVAIL #${permit.id.substring(7, 13)}

Titre: ${permit.workTitle}
Type: ${permit.permitType}
Lieu: ${permit.location}
Statut: ${getStatusLabel(permit.status)}
Date début: ${permit.dateFrom}
Date fin: ${permit.dateTo}
Description: ${permit.description || 'N/A'}
Date de création: ${new Date(permit.createdAt).toLocaleString()}

---
Document généré le ${new Date().toLocaleString()}
                `;
                
                const blob = new Blob([pdfContent], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `permis_${permit.id.substring(7, 13)}.txt`;
                link.click();
                URL.revokeObjectURL(url);
                
                showToast('PDF téléchargé avec succès', 'success');
            }, 2000);
        };

        // Function to duplicate permit
        window.duplicatePermit = function(permitId) {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }

            showConfirmDialog(
                'Dupliquer le permis',
                'Voulez-vous créer une copie de ce permis ? Vous pourrez modifier les détails avant de le soumettre.',
                function() {
                    // Navigate to initiation form
                    showSection('initiation');
                    
                    // Wait for form to be visible
                    setTimeout(() => {
                        const form = document.getElementById('initiationForm');
                        
                        // Populate form with permit data
                        if (permit.workTitle) form.querySelector('input[name="workTitle"]').value = permit.workTitle + ' (Copie)';
                        if (permit.location) form.querySelector('input[name="location"]').value = permit.location;
                        if (permit.description) form.querySelector('textarea[name="workDescription"]').value = permit.description;
                        
                        // Set permit type
                        if (permit.permitType) {
                            const permitTypeInput = form.querySelector(`input[name="permitType"][value="${permit.permitType}"]`);
                            if (permitTypeInput) permitTypeInput.checked = true;
                        }
                        
                        showToast('Permis dupliqué - Modifiez les détails si nécessaire', 'success');
                        
                        // Scroll to top of form
                        window.scrollTo({
                            top: 0,
                            behavior: 'smooth'
                        });
                    }, 500);
                }
            );
        };

        // Function to print permit
        window.printPermit = function(permitId) {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }

            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>Permis de Travail #${permit.id.substring(7, 13)}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .detail-row { margin: 10px 0; }
                        .status { padding: 5px 10px; border-radius: 5px; }
                        .status-approved { background: #dcfce7; color: #166534; }
                        .status-pending { background: #fef3c7; color: #92400e; }
                        .status-rejected { background: #fee2e2; color: #991b1b; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>PERMIS DE TRAVAIL</h1>
                        <h2>#${permit.id.substring(7, 13)}</h2>
                    </div>
                    <div class="content">
                        <div class="detail-row"><strong>Titre:</strong> ${permit.workTitle}</div>
                        <div class="detail-row"><strong>Type:</strong> ${permit.permitType}</div>
                        <div class="detail-row"><strong>Lieu:</strong> ${permit.location}</div>
                        <div class="detail-row"><strong>Statut:</strong> <span class="status status-${permit.status}">${getStatusLabel(permit.status)}</span></div>
                        <div class="detail-row"><strong>Date début:</strong> ${permit.dateFrom}</div>
                        <div class="detail-row"><strong>Date fin:</strong> ${permit.dateTo}</div>
                        <div class="detail-row"><strong>Description:</strong> ${permit.description || 'N/A'}</div>
                        <div class="detail-row"><strong>Date de création:</strong> ${new Date(permit.createdAt).toLocaleString()}</div>
                    </div>
                    <div style="margin-top: 50px; text-align: center; font-size: 12px; color: #666;">
                        Document généré le ${new Date().toLocaleString()}
                    </div>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
            
            showToast('Impression lancée', 'success');
        };

        // Function to share permit
        window.sharePermit = function(permitId) {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }

            const shareData = {
                title: `Permis de Travail #${permit.id.substring(7, 13)}`,
                text: `${permit.workTitle} - ${permit.location}`,
                url: window.location.href
            };

            if (navigator.share) {
                navigator.share(shareData).then(() => {
                    showToast('Permis partagé', 'success');
                }).catch(() => {
                    fallbackShare(permit);
                });
            } else {
                fallbackShare(permit);
            }
        };

        function fallbackShare(permit) {
            const shareText = `Permis de Travail #${permit.id.substring(7, 13)}\n${permit.workTitle}\nLieu: ${permit.location}\nStatut: ${getStatusLabel(permit.status)}`;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(shareText).then(() => {
                    showToast('Informations copiées dans le presse-papiers', 'success');
                });
            } else {
                const modal = document.createElement('div');
                modal.className = 'modal active';
                modal.innerHTML = `
                    <div class="modal-content" style="max-width: 500px;">
                        <div class="modal-header">
                            <h3>Partager le permis</h3>
                            <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                        </div>
                        <div class="modal-body">
                            <p>Copiez ces informations pour partager le permis:</p>
                            <textarea readonly style="width: 100%; height: 100px; margin: 10px 0;">${shareText}</textarea>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-primary" onclick="this.closest('.modal').remove();">Fermer</button>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
            }
        }

        // Function to extend permit
        window.extendPermit = function(permitId) {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }

            if (permit.status !== 'approved') {
                showToast('Seuls les permis approuvés peuvent être prolongés', 'warning');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <div class="modal-header">
                        <h3>Demande de prolongation</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p><strong>Permis:</strong> #${permit.id.substring(7, 13)} - ${permit.workTitle}</p>
                        <p><strong>Date de fin actuelle:</strong> ${permit.dateTo}</p>
                        
                        <div style="margin: 20px 0;">
                            <label><strong>Nouvelle date de fin:</strong></label>
                            <input type="date" id="newEndDate" style="width: 100%; padding: 8px; margin: 5px 0;" min="${permit.dateTo}">
                        </div>
                        
                        <div style="margin: 20px 0;">
                            <label><strong>Justification de la prolongation:</strong></label>
                            <textarea id="extensionReason" placeholder="Expliquez pourquoi vous avez besoin de prolonger ce permis..." style="width: 100%; height: 80px; padding: 8px; margin: 5px 0;"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove();">Annuler</button>
                        <button class="btn btn-primary" onclick="submitExtensionRequest('${permit.id}'); this.closest('.modal').remove();">
                            <i class='bx bx-send'></i> Demander la prolongation
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };

        // Function to submit extension request
        window.submitExtensionRequest = function(permitId) {
            const newEndDate = document.getElementById('newEndDate').value;
            const extensionReason = document.getElementById('extensionReason').value;
            
            if (!newEndDate || !extensionReason) {
                showToast('Veuillez remplir tous les champs', 'warning');
                return;
            }

            showToast('Demande de prolongation envoyée', 'success');
            
            // In a real application, this would send the request to the server
            // For demo purposes, we'll just show a success message
            setTimeout(() => {
                showToast('Votre demande sera examinée par le coordinateur', 'info');
            }, 2000);
        };

        // Function to cancel permit (for pending permits)
        window.cancelPermit = function(permitId) {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const permit = permits.find(p => p.id === permitId);
            
            if (!permit) {
                showToast('Permis introuvable', 'error');
                return;
            }

            if (permit.status !== 'pending') {
                showToast('Seuls les permis en attente peuvent être annulés', 'warning');
                return;
            }

            showConfirmDialog(
                'Annuler le permis',
                'Êtes-vous sûr de vouloir annuler ce permis ? Cette action est irréversible.',
                function() {
                    // Update permit status
                    permit.status = 'cancelled';
                    permit.cancelledAt = new Date().toISOString();
                    
                    // Save to localStorage
                    localStorage.setItem('myPermits', JSON.stringify(permits));
                    
                    // Refresh table
                    loadMyPermits();
                    
                    showToast('Permis annulé', 'success');
                }
            );
        };

        // Enhanced status and label functions
        function getStatusClass(status) {
            const statusClasses = {
                'approved': 'status-approved',
                'pending': 'status-pending',
                'rejected': 'status-rejected',
                'cancelled': 'status-cancelled',
                'expired': 'status-expired',
                'in-progress': 'status-in-progress'
            };
            return statusClasses[status] || 'status-pending';
        }

        function getStatusLabel(status) {
            const statusLabels = {
                'approved': 'Approuvé',
                'pending': 'En attente',
                'rejected': 'Rejeté',
                'cancelled': 'Annulé',
                'expired': 'Expiré',
                'in-progress': 'En cours'
            };
            return statusLabels[status] || 'En attente';
        }

        // Function to handle draft actions
        window.editDraft = function(draftId) {
            const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
            const draft = drafts.find(d => d.id === draftId);
            
            if (!draft) {
                showToast('Brouillon introuvable', 'error');
                return;
            }

            // Navigate to initiation form
            showSection('initiation');
            
            // Wait for form to be visible
            setTimeout(() => {
                const form = document.getElementById('initiationForm');
                
                // Populate form with draft data
                if (draft.workTitle) form.querySelector('input[name="workTitle"]').value = draft.workTitle;
                if (draft.location) form.querySelector('input[name="location"]').value = draft.location;
                if (draft.description) form.querySelector('textarea[name="workDescription"]').value = draft.description;
                if (draft.dateFrom) form.querySelector('input[name="dateFrom"]').value = draft.dateFrom;
                if (draft.dateTo) form.querySelector('input[name="dateTo"]').value = draft.dateTo;
                if (draft.timeFrom) form.querySelector('input[name="timeFrom"]').value = draft.timeFrom;
                if (draft.timeTo) form.querySelector('input[name="timeTo"]').value = draft.timeTo;
                
                // Set permit type
                if (draft.permitType) {
                    const permitTypeInput = form.querySelector(`input[name="permitType"][value="${draft.permitType}"]`);
                    if (permitTypeInput) permitTypeInput.checked = true;
                }
                
                // Store the draft ID for updating
                form.dataset.editingDraftId = draftId;
                
                showToast('Brouillon chargé pour modification', 'success');
                
                // Scroll to top of form
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }, 500);
        };

        window.previewDraft = function(draftId) {
            const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
            const draft = drafts.find(d => d.id === draftId);
            
            if (!draft) {
                showToast('Brouillon introuvable', 'error');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 700px;">
                    <div class="modal-header">
                        <h3>Aperçu du brouillon</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="draft-preview">
                            <div class="detail-row"><strong>Titre:</strong> ${draft.workTitle || 'N/A'}</div>
                            <div class="detail-row"><strong>Type:</strong> ${draft.permitType || 'N/A'}</div>
                            <div class="detail-row"><strong>Lieu:</strong> ${draft.location || 'N/A'}</div>
                            <div class="detail-row"><strong>Date début:</strong> ${draft.dateFrom || 'N/A'}</div>
                            <div class="detail-row"><strong>Date fin:</strong> ${draft.dateTo || 'N/A'}</div>
                            <div class="detail-row"><strong>Description:</strong> ${draft.description || 'N/A'}</div>
                            <div class="detail-row"><strong>Sauvegardé le:</strong> ${new Date(draft.createdAt).toLocaleString()}</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal').remove();">Fermer</button>
                        <button class="btn btn-primary" onclick="editDraft('${draft.id}'); this.closest('.modal').remove();">
                            <i class='bx bx-edit'></i> Modifier
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };

        window.copyDraft = function(draftId) {
            const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
            const draft = drafts.find(d => d.id === draftId);
            
            if (!draft) {
                showToast('Brouillon introuvable', 'error');
                return;
            }

            const newDraft = {
                ...draft,
                id: 'draft-' + Date.now(),
                createdAt: new Date().toISOString(),
                workTitle: (draft.workTitle || 'Brouillon') + ' (Copie)'
            };

            drafts.push(newDraft);
            localStorage.setItem('permitDrafts', JSON.stringify(drafts));
            
            loadMyDrafts();
            showToast('Brouillon dupliqué', 'success');
        };

        window.deleteDraft = function(draftId) {
            showConfirmDialog(
                'Supprimer le brouillon',
                'Êtes-vous sûr de vouloir supprimer ce brouillon ? Cette action est irréversible.',
                function() {
                    const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
                    const updatedDrafts = drafts.filter(d => d.id !== draftId);
                    localStorage.setItem('permitDrafts', JSON.stringify(updatedDrafts));
                    
                    loadMyDrafts();
                    showToast('Brouillon supprimé', 'success');
                }
            );
        };

        // ===== UTILITY FUNCTIONS FOR TESTING =====

        // Function to reset demo data (for testing purposes)
        window.resetDemoData = function() {
            showConfirmDialog(
                'Réinitialiser les données',
                'Voulez-vous supprimer toutes les données et recharger les données de démonstration ?',
                function() {
                    localStorage.removeItem('myPermits');
                    localStorage.removeItem('permitDrafts');
                    initializeDemoData();
                    loadMyPermits();
                    loadMyDrafts();
                    showToast('Données de démonstration rechargées', 'success');
                }
            );
        };

        // Function to add a quick test permit
        window.addTestPermit = function() {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const testPermit = {
                id: 'permit-' + Date.now(),
                createdAt: new Date().toISOString(),
                workTitle: 'Test - Maintenance rapide',
                permitType: 'Permis de travail général',
                location: 'Zone Test',
                status: 'pending',
                dateFrom: new Date().toISOString().split('T')[0],
                dateTo: new Date(Date.now() + 86400000).toISOString().split('T')[0],
                description: 'Permis de test créé automatiquement'
            };
            
            permits.push(testPermit);
            localStorage.setItem('myPermits', JSON.stringify(permits));
            loadMyPermits();
            showToast('Permis de test ajouté', 'success');
        };

        // Function to show debug info
        window.showDebugInfo = function() {
            const permits = JSON.parse(localStorage.getItem('myPermits') || '[]');
            const drafts = JSON.parse(localStorage.getItem('permitDrafts') || '[]');
            
            const modal = document.createElement('div');
            modal.className = 'modal active';
            modal.innerHTML = `
                <div class="modal-content" style="max-width: 600px;">
                    <div class="modal-header">
                        <h3>Informations de débogage</h3>
                        <button class="modal-close" onclick="this.closest('.modal').remove();">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="debug-info">
                            <h4>Statistiques:</h4>
                            <p><strong>Nombre de permis:</strong> ${permits.length}</p>
                            <p><strong>Nombre de brouillons:</strong> ${drafts.length}</p>
                            <p><strong>Permis approuvés:</strong> ${permits.filter(p => p.status === 'approved').length}</p>
                            <p><strong>Permis en attente:</strong> ${permits.filter(p => p.status === 'pending').length}</p>
                            <p><strong>Permis rejetés:</strong> ${permits.filter(p => p.status === 'rejected').length}</p>
                            
                            <h4 style="margin-top: 20px;">Actions de test:</h4>
                            <button class="btn btn-secondary" onclick="addTestPermit(); this.closest('.modal').remove();" style="margin: 5px;">
                                Ajouter un permis de test
                            </button>
                            <button class="btn btn-warning" onclick="resetDemoData(); this.closest('.modal').remove();" style="margin: 5px;">
                                Réinitialiser les données
                            </button>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" onclick="this.closest('.modal').remove();">Fermer</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        };

        // Add keyboard shortcut for debug (Ctrl+Shift+D)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                showDebugInfo();
            }
        });
    </script>
</body>
</html>