<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Système de Gestion des Permis de Travail - Responsable HSE</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"/>
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet"/>
    <style>
        :root {
            --primary: #3b82f6; 
            --primary-dark: #2563eb;
            --primary-light: #93c5fd;
            --secondary: #4f46e5;
            --accent: #3b82f6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --sidebar-width: 280px;
            --topbar-height: 70px;
            --sidebar-collapsed-width: 80px;
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;
            --transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--gray-50);
            color: var(--gray-900);
            display: flex;
            min-height: 100vh;
            line-height: 1.5;
            font-size: 15px;
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--gray-900);
            color: var(--gray-100);
            position: fixed;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
            transition: var(--transition-slow);
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            border-right: 1px solid rgba(255, 255, 255, 0.05);
            overflow: hidden;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar.collapsed .logo-text,
        .sidebar.collapsed .menu-title,
        .sidebar.collapsed .menu-text,
        .sidebar.collapsed .menu-badge,
        .sidebar.collapsed .user-info,
        .sidebar.collapsed .user-actions {
            opacity: 0;
            width: 0;
            height: 0;
            margin: 0;
            padding: 0;
            overflow: hidden;
            position: absolute;
        }

        .sidebar.collapsed .menu-link {
            justify-content: center;
            padding: 12px 0;
        }

        .sidebar.collapsed .menu-icon {
            margin-right: 0;
            font-size: 24px;
        }

        .sidebar-header {
            padding: 0 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: var(--topbar-height);
            min-height: var(--topbar-height);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 18px;
            font-weight: 700;
            color: white;
            white-space: nowrap;
        }

        .logo-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            flex-shrink: 0;
        }

        .logo-text {
            transition: var(--transition-slow);
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--gray-400);
            font-size: 20px;
            cursor: pointer;
            transition: var(--transition);
            padding: 8px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar-toggle:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-menu {
            flex: 1;
            padding: 16px 0;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--gray-700) var(--gray-800);
        }

        .sidebar-menu::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar-menu::-webkit-scrollbar-track {
            background: var(--gray-800);
        }

        .sidebar-menu::-webkit-scrollbar-thumb {
            background-color: var(--gray-700);
            border-radius: var(--radius-full);
        }

        .menu-group {
            margin-bottom: 24px;
        }

        .menu-title {
            padding: 0 24px 12px;
            font-size: 12px;
            font-weight: 600;
            color: var(--gray-400);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: var(--transition-slow);
        }

        .menu-items {
            list-style: none;
        }

        .menu-item {
            margin: 2px 0;
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: var(--gray-300);
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: var(--transition);
            font-size: 14px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .menu-link:hover {
            background-color: var(--gray-800);
            color: white;
        }

        .menu-link.active {
            background-color: rgba(37, 99, 235, 0.1);
            border-left-color: var(--primary);
            color: white;
        }

        .menu-link.active .menu-icon {
            color: var(--primary-light);
        }

        .menu-icon {
            margin-right: 12px;
            font-size: 20px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-400);
            transition: var(--transition);
            flex-shrink: 0;
        }

        .menu-text {
            transition: var(--transition-slow);
            white-space: nowrap;
        }

        .sidebar-footer {
            padding: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: auto;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px;
            border-radius: var(--radius-md);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .user-profile:hover {
            background-color: var(--gray-800);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            flex-shrink: 0;
            position: relative;
        }

        .user-info {
            flex: 1;
            min-width: 0;
            transition: var(--transition-slow);
        }

        .user-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-role {
            font-size: 12px;
            color: var(--gray-400);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-actions {
            display: flex;
            gap: 8px;
            transition: var(--transition-slow);
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--gray-400);
            cursor: pointer;
            transition: var(--transition);
            border: none;
            position: relative;
        }

        .action-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 18px;
            height: 18px;
            background-color: var(--danger);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 700;
            color: white;
            border: 2px solid var(--gray-900);
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: var(--transition-slow);
            display: flex;
            flex-direction: column;
        }

        .sidebar.collapsed ~ .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        /* Top Navigation */
        .topbar {
            height: var(--topbar-height);
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            box-shadow: var(--shadow-sm);
            border-bottom: 1px solid var(--gray-200);
            position: sticky;
            top: 0;
            z-index: 900;
        }

        .topbar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .menu-toggle {
            background: none;
            border: none;
            color: var(--gray-500);
            font-size: 24px;
            cursor: pointer;
            transition: var(--transition);
            display: none;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
        }

        .menu-toggle:hover {
            background-color: var(--gray-100);
            color: var(--gray-700);
        }

        .breadcrumbs {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .breadcrumb-item {
            color: var(--gray-500);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .breadcrumb-item:not(:last-child)::after {
            content: '/';
            color: var(--gray-300);
            margin-left: 8px;
        }

        .breadcrumb-item.active {
            color: var(--gray-700);
            font-weight: 500;
        }

        .topbar-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .topbar-action {
            position: relative;
            cursor: pointer;
        }

        .action-btn-lg {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            color: var(--gray-500);
            cursor: pointer;
            transition: var(--transition);
            border: none;
            position: relative;
        }

        .action-btn-lg:hover {
            background-color: var(--gray-100);
            color: var(--gray-700);
        }

        .action-btn-lg .notification-badge {
            border-color: white;
        }

        .user-dropdown {
            position: absolute;
            top: calc(100% + 10px);
            right: 0;
            width: 280px;
            background-color: white;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-xl);
            padding: 8px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: var(--transition);
        }

        .user-dropdown.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-header {
            padding: 12px 16px;
            border-bottom: 1px solid var(--gray-100);
            margin-bottom: 8px;
        }

        .dropdown-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--gray-900);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            border-radius: var(--radius-sm);
            color: var(--gray-700);
            text-decoration: none;
            font-size: 14px;
            transition: var(--transition);
            cursor: pointer;
        }

        .dropdown-item:hover {
            background-color: var(--gray-50);
            color: var(--primary);
        }

        .dropdown-item i {
            margin-right: 12px;
            font-size: 18px;
            width: 20px;
            color: var(--gray-500);
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--gray-100);
            margin: 8px 0;
        }
        
        /* Settings Styles */
        .checkbox-label {
            display: flex;
            align-items: center;
            font-weight: 600;
            color: var(--gray-800);
            cursor: pointer;
            margin-bottom: 4px;
        }
        
        .checkbox-label input[type="checkbox"] {
            margin-right: 10px;
            width: 18px;
            height: 18px;
            border: 2px solid var(--primary);
            border-radius: 4px;
            cursor: pointer;
        }
        
        .form-text {
            font-size: 13px;
            color: var(--gray-500);
            margin-top: 4px;
            margin-left: 28px;
            margin-bottom: 16px;
        }
        
        .profile-section {
            background: white;
            border-radius: var(--radius-lg);
            margin-bottom: 24px;
            border: 1px solid var(--gray-200);
            overflow: hidden;
        }
        
        .profile-section-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--gray-200);
            background-color: var(--gray-50);
        }
        
        .profile-section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
        }
        
        .profile-section-body {
            padding: 24px;
        }
        
        .profile-actions {
            display: flex;
            justify-content: flex-end;
            padding-top: 16px;
            margin-top: 24px;
            border-top: 1px solid var(--gray-200);
        }

        /* Content Area */
        .content-wrapper {
            flex: 1;
            padding: 24px;
            background-color: var(--gray-50);
        }

        .content-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
        }

        .content-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
        }

        .content-subtitle {
            font-size: 16px;
            color: var(--gray-600);
            margin-top: 8px;
        }

        .content-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 20px;
            border-radius: var(--radius-md);
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: var(--transition);
            border: 1px solid transparent;
            text-decoration: none;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .btn-outline {
            background-color: transparent;
            border-color: var(--gray-300);
            color: var(--gray-700);
        }

        .btn-outline:hover {
            background-color: var(--gray-50);
            border-color: var(--gray-400);
        }

        .btn-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: var(--radius-sm);
        }

        .btn-danger {
            background-color: var(--danger);
            color: white;
            border: 1px solid var(--danger);
        }

        .btn-danger:hover {
            background-color: #dc2626;
            border-color: #dc2626;
        }

        /* Table Styles */
        .table-container {
            overflow-x: auto;
            border-radius: var(--radius-lg);
            border: 1px solid var(--gray-200);
            margin-top: 24px;
        }

        .compact-table {
            font-size: 12px;
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .compact-table th {
            background: var(--gray-50);
            padding: 8px 12px;
            text-align: left;
            font-weight: 600;
            color: var(--gray-700);
            border-bottom: 2px solid var(--gray-200);
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .compact-table td {
            padding: 8px 12px;
            border-bottom: 1px solid var(--gray-100);
            color: var(--gray-600);
            vertical-align: middle;
        }

        .compact-table tr:hover {
            background-color: var(--gray-50);
        }

        .compact-table .table-actions {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
        }

        .compact-table .btn-table {
            padding: 4px 8px;
            font-size: 10px;
            border-radius: var(--radius-sm);
            min-width: auto;
            white-space: nowrap;
        }

        .compact-table .btn-table i {
            font-size: 12px;
            margin-right: 4px;
        }

        /* Enhanced Button Styles */
        .btn-table {
            padding: 6px 10px;
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .btn-table.btn-view {
            background-color: var(--primary);
            color: white;
        }

        .btn-table.btn-view:hover {
            background-color: var(--primary-dark);
        }

        .btn-table.btn-edit {
            background-color: var(--warning);
            color: var(--gray-900);
        }

        .btn-table.btn-edit:hover {
            background-color: var(--warning);
            opacity: 0.8;
        }

        .btn-table.btn-delete {
            background-color: var(--danger);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .btn-table.btn-delete:hover {
            background-color: var(--danger);
            opacity: 0.9;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
        }

        .btn-table.btn-delete:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
        }

        .btn-table.btn-warning {
            background-color: var(--warning);
            color: var(--gray-900);
        }

        .btn-table.btn-warning:hover {
            background-color: var(--warning);
            opacity: 0.8;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
        }

        .btn-table.btn-warning:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
        }

        /* Status Badge Styles - matching priority badge styles */
        .status-badge {
            padding: 4px 8px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.active {
            background-color: #f0fdf4;
            color: var(--success);
        }

        .status-badge.expiring {
            background-color: #fef3c7;
            color: var(--warning);
        }

        .status-badge.suspended {
            background-color: #fef2f2;
            color: var(--danger);
        }

        .status-badge.cancelled {
            background-color: #f3f4f6;
            color: var(--gray-600);
        }

        .table-actions {
            display: flex;
            gap: 6px;
            justify-content: center;
        }

        /* Search Bar Styles */
        .search-bar {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .search-bar input[type="text"] {
            padding: 10px 14px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 13px;
            width: 250px;
            transition: var(--transition);
        }

        .search-bar input[type="text"]:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-bar button {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 10px 18px;
            border-radius: var(--radius-md);
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
        }

        .search-bar button:hover {
            background-color: var(--primary-dark);
        }

        .permit-number {
            font-weight: 600;
            color: var(--gray-600);
        }

        .priority-badge {
            padding: 4px 8px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .priority-badge.high {
            background-color: #fef2f2;
            color: var(--danger);
        }

        .priority-badge.medium {
            background-color: #fef3c7;
            color: var(--warning);
        }

        .priority-badge.low {
            background-color: #f0fdf4;
            color: var(--success);
        }

        .actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        /* Form Styles */
        .form-section {
            background-color: white;
            padding: 32px;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            margin-bottom: 24px;
            border: 1px solid var(--gray-200);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-row-4 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-card {
            background-color: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-sm);
        }

        .form-card:last-child {
            margin-bottom: 0;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid var(--gray-200);
        }

        .text-muted {
            color: var(--gray-500);
            font-size: 12px;
        }

        label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--gray-700);
        }

        .label-icon {
            margin-right: 6px;
            font-size: 16px;
            color: var(--primary);
        }

        input[type="text"],
        input[type="email"],
        input[type="password"],
        input[type="date"],
        input[type="time"],
        textarea,
        select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 14px;
            background-color: white;
            transition: var(--transition);
            font-family: inherit;
        }

        input:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 120px;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 12px;
            margin-top: 12px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            transition: var(--transition);
            cursor: pointer;
        }

        .checkbox-item:hover {
            background-color: var(--gray-50);
            border-color: var(--primary);
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary);
            margin: 0;
        }

        .checkbox-item label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            font-size: 14px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        th,
        td {
            border: 1px solid var(--gray-200);
            padding: 16px;
            text-align: left;
        }

        th {
            background-color: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
            font-size: 14px;
        }

        td input {
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-sm);
            padding: 10px 12px;
            font-size: 14px;
            width: 100%;
        }

        /* Profile Styles */
        .profile-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            padding: 32px;
            border-radius: var(--radius-xl);
            margin-bottom: 32px;
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .profile-info {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: var(--radius-full);
            background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 28px;
            flex-shrink: 0;
            box-shadow: var(--shadow-md);
            border: 3px solid rgba(255,255,255,0.2);
        }

        .profile-details {
            flex: 1;
        }

        .profile-name {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .profile-username {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 12px;
        }

        .profile-role {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border-radius: var(--radius-full);
            font-size: 14px;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .profile-form {
            background-color: white;
            padding: 32px;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
        }

        .form-section-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
        }


        /* Readonly fields */
        input[readonly] {
            background-color: var(--gray-50);
            color: var(--gray-600);
            cursor: not-allowed;
        }

        /* Success/Error Messages */
        .message {
            padding: 16px 20px;
            border-radius: var(--radius-md);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
        }

        .message-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .message-error {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Section Headers */
        .section-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--gray-200);
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--gray-900);
        }

        .section-icon {
            font-size: 24px;
            color: var(--primary);
        }

        /* Required field indicator */
        .required {
            color: var(--danger);
            margin-left: 4px;
        }

        /* Status Badges */
        .status-badge {
            padding: 4px 12px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .status-badge.completed {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-badge.cancelled {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Statistics Cards */
        .stat-card {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 20px;
            border-radius: var(--radius-lg);
            text-align: center;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            line-height: 1;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
        }

        /* Archive specific styles */
        .search-bar select {
            padding: 8px 12px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            background-color: white;
            color: var(--gray-700);
            font-size: 14px;
            transition: var(--transition);
        }

        .search-bar select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            :root {
                --sidebar-width: 240px;
            }
        }

        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 1100;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .menu-toggle {
                display: flex;
            }

            .sidebar-toggle {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .topbar {
                padding: 0 16px;
            }

            .content-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .content-actions {
                width: 100%;
                justify-content: flex-end;
            }

            .form-row,
            .form-row-3,
            .form-row-4 {
                grid-template-columns: 1fr;
            }

            .profile-info {
                flex-direction: column;
                text-align: center;
            }

            .checkbox-group {
                grid-template-columns: 1fr;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 576px) {
            .topbar-actions {
                gap: 8px;
            }

            .action-btn-lg {
                width: 36px;
                height: 36px;
            }

            .content-wrapper {
                padding: 16px;
            }

            .form-section,
            .profile-form {
                padding: 20px;
            }

            .profile-header {
                padding: 24px;
            }
        }

        /* Animation */
        @keyframes fadeIn {
            from { 
                opacity: 0; 
                transform: translateY(20px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }

        .form-section,
        .profile-header,
        .profile-form {
            animation: fadeIn 0.5s ease-out;
        }

        /* Hidden sections */
        .hidden {
            display: none;
        }

        /* Loading states */
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn:disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        /* Modal Styles for Permit Details */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(4px);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-overlay.active {
            display: flex;
        }

        @keyframes slideIn {
            from { 
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to { 
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .permit-modal {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            animation: slideIn 0.3s ease-out;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 24px;
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
            position: relative;
            overflow: hidden;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .modal-title {
            font-size: 24px;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
            z-index: 1;
        }

        .modal-title i {
            font-size: 28px;
            color: var(--primary-light);
        }

        .modal-close {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            transition: var(--transition);
            z-index: 2;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .modal-body {
            padding: 32px;
        }

        .permit-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .info-card {
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            padding: 20px;
            border-left: 4px solid var(--primary);
            transition: var(--transition);
        }

        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .info-card-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .info-card-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .info-card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--gray-200);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 500;
            color: var(--gray-600);
            font-size: 14px;
        }

        .info-value {
            font-weight: 600;
            color: var(--gray-800);
            font-size: 14px;
            text-align: right;
        }

        .status-display {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 12px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
        }

        .status-display.active {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }


    </style>
</head>
<body>
    <!-- Sidebar Navigation -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <div class="logo-icon">
                    <img src="sonatrach-logo.png" alt="Logo Sonatrach" style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;"/>
                </div>
                <span class="logo-text">PT Pro</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class='bx bx-chevron-left'></i>
            </button>
        </div>
        <nav class="sidebar-menu">
            <div class="menu-group">
                <h3 class="menu-title">Navigation</h3>
                <ul class="menu-items">
                    <li class="menu-item">
                        <div class="menu-link active" onclick="showSection('permits')">
                            <i class='bx bxs-file menu-icon'></i>
                            <span class="menu-text">Permis en attente</span>
                        </div>
                    </li>

                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('history')">
                            <i class='bx bx-block menu-icon'></i>
                            <span class="menu-text">Suspension et annulation</span>
                        </div>
                    </li>

                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('archive')">
                            <i class='bx bx-archive menu-icon'></i>
                            <span class="menu-text">Archive</span>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="menu-group">
                <h3 class="menu-title">Compte</h3>
                <ul class="menu-items">
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('profile')">
                            <i class='bx bxs-user menu-icon'></i>
                            <span class="menu-text">Mon Profil</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('settings')">
                            <i class='bx bx-cog menu-icon'></i>
                            <span class="menu-text">Paramètres</span>
                        </div>
                    </li>
                </ul>
            </div>
        </nav>
        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">
                    MS
                    <span class="notification-badge">2</span>
                </div>
                <div class="user-info">
                    <div class="user-name">Marie Sécurité</div>
                    <div class="user-role">Responsable HSE</div>
                </div>
                <div class="user-actions">
                    <button class="action-btn" onclick="logout()">
                        <i class='bx bx-log-out'></i>
                    </button>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Top Navigation -->
        <div class="topbar">
            <div class="topbar-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class='bx bx-menu'></i>
                </button>
                <div class="breadcrumbs">
                    <span class="breadcrumb-item">ACCUEIL</span>
                    <span class="breadcrumb-item active" id="page-title">PERMIS EN ATTENTE</span>
                </div>
            </div>
            <div class="topbar-actions">
                <div class="topbar-action">
                    <button class="action-btn-lg">
                        <i class='bx bx-bell'></i>
                        <span class="notification-badge">2</span>
                    </button>
                </div>
                <div class="topbar-action" id="userDropdownTrigger">
                    <button class="action-btn-lg">
                        <div class="user-avatar" style="width: 32px; height: 32px; font-size: 12px;">
                            MS
                        </div>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <div class="dropdown-header">
                            <div class="dropdown-title">Paramètres du compte</div>
                        </div>
                        <div class="dropdown-item" onclick="showSection('profile')">
                            <i class='bx bx-user'></i>
                            <span>Mon profil</span>
                        </div>
                        <div class="dropdown-divider"></div>
                        <div class="dropdown-item" onclick="logout()">
                            <i class='bx bx-log-out'></i>
                            <span>Déconnexion</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-wrapper">
            <!-- Permits Section -->
            <section id="permits" class="form-section">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Permis en attente</h1>
                        <p class="content-subtitle">Liste des permis de travail émis par le coordinateur en attente d'évaluation</p>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <input type="text" id="searchPermitsInput" placeholder="Rechercher un permis..." onkeyup="searchPermitsTable()">
                        <button onclick="searchPermitsTable()">Rechercher</button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="compact-table">
                        <thead>
                            <tr>
                                <th>ID permis</th>
                                <th>Type de travail</th>
                                <th>Zone</th>
                                <th>Demandeur</th>
                                <th>Date d'émission</th>
                                <th>Priorité</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="permitsTableBody">
                            <tr data-permit-id="PT-2024-001">
                                <td><span class="permit-number">PT-2024-001</span></td>
                                <td>Travaux dont les dangers associés sont potentiellement dangereux.</td>
                                <td>Zone A - Unité 100</td>
                                <td>Jean Dupont</td>
                                <td>15/01/2024</td>
                                <td><span class="priority-badge high">Haute</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-001')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-delete" onclick="rejectPermit('PT-2024-001')">
                                            <i class='bx bx-x'></i>
                                            Rejeter
                                        </button>
                                        <button class="btn-table btn-view" onclick="evaluatePermit('PT-2024-001')" style="background-color: var(--success); color: white;">
                                            <i class='bx bx-analyse'></i>
                                            Évaluer les risques
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-002">
                                <td><span class="permit-number">PT-2024-002</span></td>
                                <td>Travaux routiniers à faibles risques.</td>
                                <td>Zone B - Unité 200</td>
                                <td>Marie Martin</td>
                                <td>16/01/2024</td>
                                <td><span class="priority-badge medium">Moyenne</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-002')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-delete" onclick="rejectPermit('PT-2024-002')">
                                            <i class='bx bx-x'></i>
                                            Rejeter
                                        </button>
                                        <button class="btn-table btn-view" onclick="evaluatePermit('PT-2024-002')" style="background-color: var(--success); color: white;">
                                            <i class='bx bx-analyse'></i>
                                            Évaluer les risques
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-003">
                                <td><span class="permit-number">PT-2024-003</span></td>
                                <td>Travaux d'urgence.</td>
                                <td>Zone C - Unité 300</td>
                                <td>Pierre Durand</td>
                                <td>17/01/2024</td>
                                <td><span class="priority-badge low">Basse</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-003')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-delete" onclick="rejectPermit('PT-2024-003')">
                                            <i class='bx bx-x'></i>
                                            Rejeter
                                        </button>
                                        <button class="btn-table btn-view" onclick="evaluatePermit('PT-2024-003')" style="background-color: var(--success); color: white;">
                                            <i class='bx bx-analyse'></i>
                                            Évaluer les risques
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-004">
                                <td><span class="permit-number">PT-2024-004</span></td>
                                <td>Travaux dispensés du permis de travail</td>
                                <td>Zone D - Unité 400</td>
                                <td>Sophie Leblanc</td>
                                <td>18/01/2024</td>
                                <td><span class="priority-badge low">Basse</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-004')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-delete" onclick="rejectPermit('PT-2024-004')">
                                            <i class='bx bx-x'></i>
                                            Rejeter
                                        </button>
                                        <button class="btn-table btn-view" onclick="evaluatePermit('PT-2024-004')" style="background-color: var(--success); color: white;">
                                            <i class='bx bx-analyse'></i>
                                            Évaluer les risques
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Assessment Section -->
            <section id="assessment" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Évaluation des Risques</h1>
                        <p class="content-subtitle">Évaluation des dangers et précautions nécessaires pour le permis de travail</p>
                    </div>
                </div>

                <form id="assessmentForm">
                    <div class="section-header">
                        <i class='bx bx-shield-x section-icon'></i>
                        <h2 class="section-title">Dangers</h2>
                    </div>
                    <div class="form-group">
                        <div class="checkbox-group">
                            <div class="checkbox-item"><input type="checkbox" id="danger-produits"><label for="danger-produits">Produits dangereux</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="danger-pression"><label for="danger-pression">Haute pression</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="danger-depression"><label for="danger-depression">Dépression (vide)</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="danger-temp-haute"><label for="danger-temp-haute">Haute température</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="danger-temp-basse"><label for="danger-temp-basse">Basse température</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="danger-radioactifs"><label for="danger-radioactifs">Produits radioactifs</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="danger-electrique"><label for="danger-electrique">Énergie électrique</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="danger-hauteur"><label for="danger-hauteur">Travail en hauteur</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="danger-confine"><label for="danger-confine">Espace confiné</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="danger-excavation"><label for="danger-excavation">Excavation/éboulement</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="danger-levage"><label for="danger-levage">Opération de levage</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="danger-acces"><label for="danger-acces">Accès dangereux</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="danger-obscurite"><label for="danger-obscurite">Obscurité</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="danger-bruits"><label for="danger-bruits">Bruits/vibrations</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="danger-machine"><label for="danger-machine">Machine tournante</label></div>
                        </div>
                    </div>

                    <!-- Préparation -->
                    <div class="section-header" style="margin-top: 20px;">
                        <i class='bx bx-cog section-icon'></i>
                        <h2 class="section-title">Préparation</h2>
                    </div>
                    <div class="form-group">
                        <div class="checkbox-group">
                            <div class="checkbox-item"><input type="checkbox" id="prep-depressurisation"><label for="prep-depressurisation">Dépressurisation</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="prep-vidange"><label for="prep-vidange">Vidange</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="prep-lavage"><label for="prep-lavage">Lavage à l'eau</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="prep-nettoyage"><label for="prep-nettoyage">Nettoyage</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="prep-inertage-vapeur"><label for="prep-inertage-vapeur">Inertage à la vapeur</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="prep-inertage-azote"><label for="prep-inertage-azote">Inertage à l'azote</label></div>
                        </div>
                    </div>

                    <div class="section-header" style="margin-top: 20px;">
                        <i class='bx bx-shield-quarter section-icon'></i>
                        <h2 class="section-title">Précautions par Autorité de Zone</h2>
                    </div>
                    <div class="form-group">
                        <div class="checkbox-group">
                            <div class="checkbox-item"><input type="checkbox" id="zone-feu-gaz"><label for="zone-feu-gaz">Systèmes feu et gaz inhibés</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="zone-esd"><label for="zone-esd">ESD (Arrêt d'urgence) forcés</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="zone-coordination"><label for="zone-coordination">Coordination avec Autre Autorité de Zone</label></div>
                        </div>
                    </div>

                    <div class="section-header" style="margin-top: 20px;">
                        <i class='bx bx-hard-hat section-icon'></i>
                        <h2 class="section-title">Précautions à prendre par le Responsable d’Exécution :</h2>
                    </div>
                    <div class="form-group">
                        <div class="checkbox-group">
                            <div class="checkbox-item"><input type="checkbox" id="resp-gants-bottes"><label for="resp-gants-bottes">Gants-Bottes-Lunettes</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="resp-vetements"><label for="resp-vetements">Vêtements spéciaux</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="resp-ari"><label for="resp-ari">App. Resp. Isolant (ARI)</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="resp-masque"><label for="resp-masque">Masque à cartouche</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="resp-combinaison"><label for="resp-combinaison">Combinaison anti-acide</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="resp-protection-auditive"><label for="resp-protection-auditive">Protection auditive</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="resp-couverture"><label for="resp-couverture">Couverture de protection</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="resp-balisage"><label for="resp-balisage">Zone de travail balisée</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="resp-eclairage"><label for="resp-eclairage">Éclairage adéquat</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="resp-personnel"><label for="resp-personnel">Personnel habilité</label></div>
                            <div class="checkbox-item"><input type="checkbox" id="resp-supervision"><label for="resp-supervision">Supervision renforcée</label></div>
                        </div>
                    </div>

                    <div style="display: flex; justify-content: flex-end; gap: 16px; margin-top: 32px; padding-top: 24px; border-top: 1px solid var(--gray-200);">
                        <button type="button" class="btn btn-outline" onclick="cancelAssessment()">Annuler</button>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class='bx bx-save btn-icon'></i>
                            Valider l'évaluation des risques
                        </button>
                    </div>
                </form>
            </section>
            
            <!-- Settings Section -->
            <section id="settings" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Paramètres de compte</h1>
                        <p class="content-subtitle">Gérez vos préférences de notification et les paramètres de compte</p>
                    </div>
                </div>

                <div class="profile-section">
                    <div class="profile-section-header">
                        <h3 class="profile-section-title">Préférences de notification</h3>
                    </div>
                    <div class="profile-section-body">
                        <form id="notificationForm">
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked id="emailNotif">
                                    <span>Notifications par email</span>
                                </label>
                                <p class="form-text">Recevez des emails pour les mises à jour importantes</p>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked id="permitNotif">
                                    <span>Notifications de permis</span>
                                </label>
                                <p class="form-text">Soyez notifié quand un nouveau permis nécessite votre évaluation</p>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked id="statusNotif">
                                    <span>Mises à jour de statut</span>
                                </label>
                                <p class="form-text">Recevez des notifications quand le statut d'un permis change</p>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="reminderNotif">
                                    <span>Rappels</span>
                                </label>
                                <p class="form-text">Recevez des rappels pour les permis en attente d'action</p>
                            </div>
                            
                            <div class="profile-actions">
                                <button type="submit" class="btn btn-primary" id="saveNotifBtn">
                                    <i class="bx bx-save btn-icon"></i>Enregistrer les préférences
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </section>

            <!-- Profile Section -->
            <section id="profile" class="hidden">
                <!-- Profile Header -->
                <div class="profile-header">
                    <div class="profile-info">
                        <div class="profile-avatar">MS</div>
                        <div class="profile-details">
                            <h1 class="profile-name">Marie Sécurité</h1>
                            <div class="profile-username">@msecurite</div>
                            <span class="profile-role">
                                <i class='bx bx-shield-check'></i>
                                Responsable HSE
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Profile Form -->
                <div class="profile-form">
                    <h2 class="form-section-title">
                        <i class='bx bx-user'></i>
                        Informations du Profil
                    </h2>
                    
                    <form id="profileForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-user label-icon'></i>
                                    Nom complet
                                </label>
                                <input type="text" value="Marie Sécurité" readonly/>
                            </div>
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-user label-icon'></i>
                                    Nom d'utilisateur
                                </label>
                                <input type="text" value="msecurite" readonly/>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-briefcase label-icon'></i>
                                    Fonction
                                </label>
                                <input type="text" value="Responsable HSE" readonly/>
                            </div>
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-envelope label-icon'></i>
                                    Email <span class="required">*</span>
                                </label>
                                <input type="email" id="userEmail" value="<EMAIL>" required placeholder="Entrez votre adresse email"/>
                            </div>
                        </div>

                        <!-- Success/Error Message -->
                        <div id="profileMessage" class="hidden"></div>

                        <!-- Submit Button -->
                        <div style="display: flex; justify-content: flex-end; margin-top: 24px;">
                            <button type="submit" class="btn btn-primary" id="profileSubmitBtn">
                                <i class='bx bx-save btn-icon'></i>
                                Mettre à jour l'email
                            </button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Suspension et Annulation Section -->
            <section id="history" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Suspension et Annulation</h1>
                        <p class="content-subtitle">Gestion des permis en cours - Suspension et annulation</p>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <input type="text" id="searchHistoryInput" placeholder="Rechercher un permis..." onkeyup="searchHistoryTable()">
                        <button onclick="searchHistoryTable()">Rechercher</button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="compact-table">
                        <thead>
                            <tr>
                                <th>ID permis</th>
                                <th>Type de travail</th>
                                <th>Zone</th>
                                <th>Demandeur</th>
                                <th>Date début</th>
                                <th>Date fin</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="activePermitsTableBody">
                            <tr data-permit-id="PT-2024-005">
                                <td><span class="permit-number">PT-2024-005</span></td>
                                <td>Maintenance préventive équipements</td>
                                <td>Zone A - Unité 100</td>
                                <td>Jean Dupont</td>
                                <td>20/01/2024</td>
                                <td>25/01/2024</td>
                                <td><span class="priority-badge low">En cours</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-005')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-warning" onclick="declareSuspensionAnnulation('PT-2024-005')">
                                            <i class='bx bx-block'></i>
                                            Suspension/Annulation
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-006">
                                <td><span class="permit-number">PT-2024-006</span></td>
                                <td>Réparation urgente canalisation</td>
                                <td>Zone B - Unité 200</td>
                                <td>Marie Martin</td>
                                <td>21/01/2024</td>
                                <td>23/01/2024</td>
                                <td><span class="priority-badge low">En cours</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-006')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-warning" onclick="declareSuspensionAnnulation('PT-2024-006')">
                                            <i class='bx bx-block'></i>
                                            Suspension/Annulation
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-007">
                                <td><span class="permit-number">PT-2024-007</span></td>
                                <td>Installation nouveaux équipements</td>
                                <td>Zone C - Unité 300</td>
                                <td>Pierre Durand</td>
                                <td>22/01/2024</td>
                                <td>30/01/2024</td>
                                <td><span class="priority-badge low">En cours</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-007')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-warning" onclick="declareSuspensionAnnulation('PT-2024-007')">
                                            <i class='bx bx-block'></i>
                                            Suspension/Annulation
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-008">
                                <td><span class="permit-number">PT-2024-008</span></td>
                                <td>Nettoyage industriel</td>
                                <td>Zone D - Unité 400</td>
                                <td>Sophie Leblanc</td>
                                <td>23/01/2024</td>
                                <td>24/01/2024</td>
                                <td><span class="priority-badge low">En cours</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-008')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-warning" onclick="declareSuspensionAnnulation('PT-2024-008')">
                                            <i class='bx bx-block'></i>
                                            Suspension/Annulation
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Declaration Suspension/Annulation Section -->
            <section id="suspension-declaration" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Déclaration de Suspension/Annulation</h1>
                        <p class="content-subtitle">Déclarer la suspension ou l'annulation d'un permis de travail</p>
                    </div>
                    <button class="btn btn-secondary" onclick="showSection('history')">
                        <i class='bx bx-arrow-back'></i>
                        Retour
                    </button>
                </div>

                <form id="suspensionForm">
                    <div class="form-card">
                        <div class="section-header">
                            <i class='bx bx-info-circle section-icon'></i>
                            <h2 class="section-title">Informations du permis</h2>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="permitNumber">Numéro du permis</label>
                                <input type="text" id="permitNumber" name="permitNumber" readonly>
                            </div>
                            <div class="form-group">
                                <label for="permitDescription">Description des travaux</label>
                                <input type="text" id="permitDescription" name="permitDescription" readonly>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="permitZone">Zone</label>
                                <input type="text" id="permitZone" name="permitZone" readonly>
                            </div>
                            <div class="form-group">
                                <label for="permitRequester">Demandeur</label>
                                <input type="text" id="permitRequester" name="permitRequester" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="form-card">
                        <div class="section-header">
                            <i class='bx bx-block section-icon'></i>
                            <h2 class="section-title">Déclaration</h2>
                        </div>
                        
                        <div class="form-group">
                            <label for="actionType">Type d'action</label>
                            <input type="text" id="actionType" name="actionType" value="Suspension et Annulation" readonly style="background-color: var(--gray-100); cursor: not-allowed;">
                        </div>
                        
                        <div class="form-group">
                            <label for="reason">Motif de la suspension/annulation *</label>
                            <select id="reason" name="reason" required>
                                <option value="">Sélectionner un motif</option>
                                <option value="conditions-meteorologiques">Conditions météorologiques défavorables</option>
                                <option value="incident-securite">Incident de sécurité</option>
                                <option value="non-conformite">Non-conformité détectée</option>
                                <option value="equipement-defaillant">Équipement défaillant</option>
                                <option value="personnel-indisponible">Personnel indisponible</option>
                                <option value="changement-priorite">Changement de priorité</option>
                                <option value="autre">Autre (préciser dans les commentaires)</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="urgency">Niveau d'urgence *</label>
                            <select id="urgency" name="urgency" required>
                                <option value="">Sélectionner le niveau d'urgence</option>
                                <option value="immediate">Immédiate - Arrêt immédiat requis</option>
                                <option value="urgent">Urgent - Dans les 2 heures</option>
                                <option value="normal">Normal - Fin de poste</option>
                                <option value="planifie">Planifié - Selon planning</option>
                            </select>
                        </div>
                        

                        
                        <div class="form-group">
                            <label for="comments">Commentaires et détails *</label>
                            <textarea id="comments" name="comments" rows="4" required 
                                placeholder="Décrivez en détail les circonstances et les mesures prises..."></textarea>
                        </div>
                    </div>

                    <div style="display: flex; justify-content: flex-end; margin-top: 32px; padding-top: 24px; border-top: 1px solid var(--gray-200);">
                        <button type="button" class="btn btn-outline" onclick="showSection('history')">Annuler</button>
                        <button type="submit" class="btn btn-primary" id="suspensionSubmitBtn" style="margin-left: 12px;">
                            <i class='bx bx-send btn-icon'></i>
                            Envoyer la déclaration
                        </button>
                    </div>
                </form>
            </section>

            <!-- Archive Section -->
            <section id="archive" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Archive</h1>
                        <p class="content-subtitle">Consulter et filtrer tous les permis archivés (clôturés ou annulés)</p>
                    </div>
                    <div style="display: flex; gap: 12px;">
                        <button class="btn btn-secondary" onclick="exportArchiveToExcel()">
                            <i class='bx bx-download'></i>
                            Exporter Excel
                        </button>
                        <button class="btn btn-primary" onclick="showArchiveFilters()">
                            <i class='bx bx-filter'></i>
                            Filtres avancés
                        </button>
                    </div>
                </div>

                <!-- Search and Filters Bar -->
                <div class="search-bar">
                    <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
                        <input type="text" id="searchArchiveInput" placeholder="Rechercher par ID permis ou mot-clé..." onkeyup="searchArchiveTable()" style="flex: 1; min-width: 250px;">
                        
                        <select id="archiveTypeFilter" onchange="filterArchiveTable()" style="min-width: 150px;">
                            <option value="">Tous les types</option>
                            <option value="maintenance">Maintenance</option>
                            <option value="reparation">Réparation</option>
                            <option value="installation">Installation</option>
                            <option value="nettoyage">Nettoyage</option>
                            <option value="froid">Travaux à froid</option>
                            <option value="inspection">Inspection</option>
                            <option value="calibrage">Calibrage</option>
                        </select>
                        
                        <select id="archiveStatusFilter" onchange="filterArchiveTable()" style="min-width: 120px;">
                            <option value="">Tous les statuts</option>
                            <option value="cloture">Clôturé</option>
                            <option value="annule">Annulé</option>
                        </select>
                        
                        <select id="archiveZoneFilter" onchange="filterArchiveTable()" style="min-width: 120px;">
                            <option value="">Toutes les zones</option>
                            <option value="Zone A">Zone A</option>
                            <option value="Zone B">Zone B</option>
                            <option value="Zone C">Zone C</option>
                            <option value="Zone D">Zone D</option>
                        </select>
                        
                        <button onclick="resetArchiveFilters()" class="btn btn-outline" style="white-space: nowrap;">
                            <i class='bx bx-refresh'></i>
                            Réinitialiser
                        </button>
                    </div>
                </div>

                <!-- Advanced Filters Panel (Hidden by default) -->
                <div id="advancedFilters" class="form-card" style="display: none; margin-bottom: 24px;">
                    <div class="section-header">
                        <i class='bx bx-filter section-icon'></i>
                        <h2 class="section-title">Filtres avancés</h2>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="archiveDateStart">Date de début</label>
                            <input type="date" id="archiveDateStart" onchange="filterArchiveTable()">
                        </div>
                        <div class="form-group">
                            <label for="archiveDateEnd">Date de fin</label>
                            <input type="date" id="archiveDateEnd" onchange="filterArchiveTable()">
                        </div>
                        <div class="form-group">
                            <label for="archivedByFilter">Archivé par</label>
                            <select id="archivedByFilter" onchange="filterArchiveTable()">
                                <option value="">Tous les utilisateurs</option>
                                <option value="Marie Sécurité">Marie Sécurité</option>
                                <option value="Jean Dupont">Jean Dupont</option>
                                <option value="Pierre Martin">Pierre Martin</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Archive Table -->
                <div class="table-container">
                    <table class="compact-table">
                        <thead>
                            <tr>
                                <th>ID Permis</th>
                                <th>Type de travail</th>
                                <th>Zone</th>
                                <th>Demandeur</th>
                                <th>Date début</th>
                                <th>Date fin</th>
                                <th>Statut final</th>
                                <th>Archivé par</th>
                                <th>Date archivage</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="archiveTableBody">
                            <tr data-permit-id="PT-2024-001">
                                <td><span class="permit-number">PT-2024-001</span></td>
                                <td>Maintenance préventive pompes</td>
                                <td>Zone A - Unité 100</td>
                                <td>Jean Dupont</td>
                                <td>15/01/2024</td>
                                <td>18/01/2024</td>
                                <td><span class="status-badge completed">Clôturé</span></td>
                                <td>Marie Sécurité</td>
                                <td>18/01/2024 16:30</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewArchivedPermitDetails('PT-2024-001')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-secondary" onclick="downloadPermitPDF('PT-2024-001')">
                                            <i class='bx bx-download'></i>
                                            PDF
                                        </button>
                                        <button class="btn-table btn-info" onclick="addArchiveComment('PT-2024-001')">
                                            <i class='bx bx-comment-add'></i>
                                            Commentaire
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-002">
                                <td><span class="permit-number">PT-2024-002</span></td>
                                <td>Réparation urgente valve</td>
                                <td>Zone B - Unité 200</td>
                                <td>Marie Martin</td>
                                <td>16/01/2024</td>
                                <td>17/01/2024</td>
                                <td><span class="status-badge cancelled">Annulé</span></td>
                                <td>Marie Sécurité</td>
                                <td>17/01/2024 09:15</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewArchivedPermitDetails('PT-2024-002')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-secondary" onclick="downloadPermitPDF('PT-2024-002')">
                                            <i class='bx bx-download'></i>
                                            PDF
                                        </button>
                                        <button class="btn-table btn-info" onclick="addArchiveComment('PT-2024-002')">
                                            <i class='bx bx-comment-add'></i>
                                            Commentaire
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-003">
                                <td><span class="permit-number">PT-2024-003</span></td>
                                <td>Installation nouveaux capteurs</td>
                                <td>Zone C - Unité 300</td>
                                <td>Pierre Durand</td>
                                <td>12/01/2024</td>
                                <td>15/01/2024</td>
                                <td><span class="status-badge completed">Clôturé</span></td>
                                <td>Jean Dupont</td>
                                <td>15/01/2024 17:45</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewArchivedPermitDetails('PT-2024-003')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-secondary" onclick="downloadPermitPDF('PT-2024-003')">
                                            <i class='bx bx-download'></i>
                                            PDF
                                        </button>
                                        <button class="btn-table btn-info" onclick="addArchiveComment('PT-2024-003')">
                                            <i class='bx bx-comment-add'></i>
                                            Commentaire
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-004">
                                <td><span class="permit-number">PT-2024-004</span></td>
                                <td>Nettoyage industriel réservoirs</td>
                                <td>Zone D - Unité 400</td>
                                <td>Sophie Leblanc</td>
                                <td>10/01/2024</td>
                                <td>14/01/2024</td>
                                <td><span class="status-badge completed">Clôturé</span></td>
                                <td>Marie Sécurité</td>
                                <td>14/01/2024 14:20</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewArchivedPermitDetails('PT-2024-004')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-secondary" onclick="downloadPermitPDF('PT-2024-004')">
                                            <i class='bx bx-download'></i>
                                            PDF
                                        </button>
                                        <button class="btn-table btn-info" onclick="addArchiveComment('PT-2024-004')">
                                            <i class='bx bx-comment-add'></i>
                                            Commentaire
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-005">
                                <td><span class="permit-number">PT-2024-005</span></td>
                                <td>Travaux à froid - Maintenance électrique</td>
                                <td>Zone A - Unité 100</td>
                                <td>Ahmed Benali</td>
                                <td>08/01/2024</td>
                                <td>12/01/2024</td>
                                <td><span class="status-badge completed">Clôturé</span></td>
                                <td>Marie Sécurité</td>
                                <td>12/01/2024 18:00</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewArchivedPermitDetails('PT-2024-005')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-secondary" onclick="downloadPermitPDF('PT-2024-005')">
                                            <i class='bx bx-download'></i>
                                            PDF
                                        </button>
                                        <button class="btn-table btn-info" onclick="addArchiveComment('PT-2024-005')">
                                            <i class='bx bx-comment-add'></i>
                                            Commentaire
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-006">
                                <td><span class="permit-number">PT-2024-006</span></td>
                                <td>Travaux à froid - Inspection tuyauterie</td>
                                <td>Zone B - Unité 200</td>
                                <td>Karim Messaoud</td>
                                <td>05/01/2024</td>
                                <td>09/01/2024</td>
                                <td><span class="status-badge completed">Clôturé</span></td>
                                <td>Jean Dupont</td>
                                <td>09/01/2024 16:45</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewArchivedPermitDetails('PT-2024-006')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-secondary" onclick="downloadPermitPDF('PT-2024-006')">
                                            <i class='bx bx-download'></i>
                                            PDF
                                        </button>
                                        <button class="btn-table btn-info" onclick="addArchiveComment('PT-2024-006')">
                                            <i class='bx bx-comment-add'></i>
                                            Commentaire
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-007">
                                <td><span class="permit-number">PT-2024-007</span></td>
                                <td>Travaux à froid - Calibrage instruments</td>
                                <td>Zone C - Unité 300</td>
                                <td>Fatima Zohra</td>
                                <td>03/01/2024</td>
                                <td>07/01/2024</td>
                                <td><span class="status-badge cancelled">Annulé</span></td>
                                <td>Marie Sécurité</td>
                                <td>07/01/2024 10:30</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewArchivedPermitDetails('PT-2024-007')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-secondary" onclick="downloadPermitPDF('PT-2024-007')">
                                            <i class='bx bx-download'></i>
                                            PDF
                                        </button>
                                        <button class="btn-table btn-info" onclick="addArchiveComment('PT-2024-007')">
                                            <i class='bx bx-comment-add'></i>
                                            Commentaire
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-008">
                                <td><span class="permit-number">PT-2024-008</span></td>
                                <td>Travaux à froid - Maintenance mécanique</td>
                                <td>Zone D - Unité 400</td>
                                <td>Yacine Boumediene</td>
                                <td>01/01/2024</td>
                                <td>05/01/2024</td>
                                <td><span class="status-badge completed">Clôturé</span></td>
                                <td>Pierre Martin</td>
                                <td>05/01/2024 17:15</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewArchivedPermitDetails('PT-2024-008')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-secondary" onclick="downloadPermitPDF('PT-2024-008')">
                                            <i class='bx bx-download'></i>
                                            PDF
                                        </button>
                                        <button class="btn-table btn-info" onclick="addArchiveComment('PT-2024-008')">
                                            <i class='bx bx-comment-add'></i>
                                            Commentaire
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Archive Statistics -->
                <div class="form-card" style="margin-top: 24px;">
                    <div class="section-header">
                        <i class='bx bx-bar-chart section-icon'></i>
                        <h2 class="section-title">Statistiques d'archivage</h2>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <div class="stat-card">
                                <div class="stat-number">184</div>
                                <div class="stat-label">Total archivés</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="stat-card">
                                <div class="stat-number">168</div>
                                <div class="stat-label">Clôturés</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="stat-card">
                                <div class="stat-number">16</div>
                                <div class="stat-label">Annulés</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="stat-card">
                                <div class="stat-number">91%</div>
                                <div class="stat-label">Taux de réussite</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="stat-card">
                                <div class="stat-number">42</div>
                                <div class="stat-label">Travaux à froid</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="stat-card">
                                <div class="stat-number">7.2j</div>
                                <div class="stat-label">Durée moyenne</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Modal for Permit Details -->
    <div class="modal-overlay" id="permitModal">
        <div class="permit-modal">
            <div class="modal-header">
                <h2 class="modal-title">
                    <i class='bx bx-file-blank'></i>
                    <span id="modalPermitTitle">Détails du Permis</span>
                </h2>
                <button class="modal-close" onclick="closePermitModal()">
                    <i class='bx bx-x'></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="permit-info-grid">
                    <div class="info-card">
                        <div class="info-card-header">
                            <div class="info-card-icon">
                                <i class='bx bx-info-circle'></i>
                            </div>
                            <h3 class="info-card-title">Informations Générales</h3>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Numéro de permis</span>
                            <span class="info-value" id="modalPermitNumber">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Description</span>
                            <span class="info-value" id="modalPermitDescription">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Zone de travail</span>
                            <span class="info-value" id="modalPermitZone">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Demandeur</span>
                            <span class="info-value" id="modalPermitRequester">-</span>
                        </div>
                    </div>

                    <div class="info-card">
                        <div class="info-card-header">
                            <div class="info-card-icon">
                                <i class='bx bx-calendar'></i>
                            </div>
                            <h3 class="info-card-title">Planification</h3>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Date de début</span>
                            <span class="info-value" id="modalStartDate">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Date de fin</span>
                            <span class="info-value" id="modalEndDate">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Statut actuel</span>
                            <span class="info-value">
                                <span class="status-display active" id="modalStatus">En cours</span>
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Responsable HSE</span>
                            <span class="info-value">Marie Sécurité</span>
                        </div>
                    </div>

                    <div class="info-card" id="archiveInfoCard" style="display: none;">
                        <div class="info-card-header">
                            <div class="info-card-icon">
                                <i class='bx bx-archive'></i>
                            </div>
                            <h3 class="info-card-title">Informations d'archivage</h3>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Archivé par</span>
                            <span class="info-value" id="modalArchivedBy">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Date d'archivage</span>
                            <span class="info-value" id="modalArchiveDate">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Motif d'archivage</span>
                            <span class="info-value" id="modalArchiveReason">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Durée totale</span>
                            <span class="info-value" id="modalDuration">-</span>
                        </div>
                    </div>

                    <div class="info-card" id="safetyInfoCard" style="display: none;">
                        <div class="info-card-header">
                            <div class="info-card-icon">
                                <i class='bx bx-shield-check'></i>
                            </div>
                            <h3 class="info-card-title">Sécurité et Conformité</h3>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Type de travaux</span>
                            <span class="info-value" id="modalWorkType">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Risques identifiés</span>
                            <span class="info-value" id="modalRisks">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Mesures de sécurité</span>
                            <span class="info-value" id="modalSafetyMeasures">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">EPI requis</span>
                            <span class="info-value" id="modalPPE">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle sidebar on mobile
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.querySelector('.sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');

            menuToggle.addEventListener('click', function() {
                sidebar.classList.toggle('active');
            });

            // Toggle sidebar collapse/expand
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                const icon = sidebarToggle.querySelector('i');
                if (sidebar.classList.contains('collapsed')) {
                    icon.classList.remove('bx-chevron-left');
                    icon.classList.add('bx-chevron-right');
                } else {
                    icon.classList.remove('bx-chevron-right');
                    icon.classList.add('bx-chevron-left');
                }
            });

            // User dropdown toggle
            const userDropdownTrigger = document.getElementById('userDropdownTrigger');
            const userDropdown = document.getElementById('userDropdown');

            userDropdownTrigger.addEventListener('click', function(e) {
                e.stopPropagation();
                userDropdown.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!userDropdown.contains(e.target) && e.target !== userDropdownTrigger && !userDropdownTrigger.contains(e.target)) {
                    userDropdown.classList.remove('active');
                }
            });
            
            // Risk assessment form submission
            const assessmentForm = document.getElementById('assessmentForm');
            const submitBtn = document.getElementById('submitBtn');
            
            assessmentForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                // Validate that at least one checkbox is selected in each section
                if (!validateAssessmentForm()) {
                    return; // Stop submission if validation fails
                }
                
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="bx bx-loader-alt bx-spin btn-icon"></i>Validation en cours...';
                
                try {
                    // Simulate form submission
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    
                    // Get the current permit ID from session storage
                    const currentPermitId = sessionStorage.getItem('currentPermitId');
                    
                    // Mark the permit as evaluated
                    if (currentPermitId) {
                        markPermitAsEvaluated(currentPermitId);
                    }
                    
                    // Show success message
                    alert('Évaluation des risques validée avec succès !');
                    
                    // Return to permits table
                    showSection('permits');
                    
                } catch (error) {
                    // Show error message
                    alert('Une erreur est survenue lors de la validation de l\'évaluation des risques.');
                } finally {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="bx bx-save btn-icon"></i>Valider l\'appréciation des risques';
                }
            });
            
            // Profile form submission
            const profileForm = document.getElementById('profileForm');
            const profileSubmitBtn = document.getElementById('profileSubmitBtn');
            const profileMessage = document.getElementById('profileMessage');
            const userEmail = document.getElementById('userEmail');
            const originalEmail = userEmail.value;

            profileForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                if (userEmail.value === originalEmail) {
                    profileMessage.className = 'message message-error';
                    profileMessage.innerHTML = '<i class="bx bx-info-circle"></i>Aucune modification détectée';
                    profileMessage.classList.remove('hidden');
                    return;
                }
                
                profileSubmitBtn.disabled = true;
                profileSubmitBtn.innerHTML = '<i class="bx bx-loader-alt bx-spin btn-icon"></i>Mise à jour...';
                
                try {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    profileMessage.className = 'message message-success';
                    profileMessage.innerHTML = '<i class="bx bx-check-circle"></i>Email mis à jour avec succès !';
                    profileMessage.classList.remove('hidden');
                    
                    setTimeout(() => {
                        profileMessage.classList.add('hidden');
                    }, 3000);
                    
                } catch (error) {
                    profileMessage.className = 'message message-error';
                    profileMessage.innerHTML = '<i class="bx bx-error-circle"></i>Erreur lors de la mise à jour de l\'email';
                    profileMessage.classList.remove('hidden');
                } finally {
                    profileSubmitBtn.disabled = false;
                    profileSubmitBtn.innerHTML = '<i class="bx bx-save btn-icon"></i>Mettre à jour l\'email';
                }
            });
            
            // Notification settings form submission
            const notificationForm = document.getElementById('notificationForm');
            const saveNotifBtn = document.getElementById('saveNotifBtn');
            
            if (notificationForm) {
                notificationForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    saveNotifBtn.disabled = true;
                    saveNotifBtn.innerHTML = '<i class="bx bx-loader-alt bx-spin btn-icon"></i>Enregistrement...';
                    
                    try {
                        // Simulate API call
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        
                        alert('Préférences de notification enregistrées avec succès!');
                    } catch (error) {
                        alert('Erreur lors de l\'enregistrement des préférences. Veuillez réessayer.');
                    } finally {
                        saveNotifBtn.disabled = false;
                        saveNotifBtn.innerHTML = '<i class="bx bx-save btn-icon"></i>Enregistrer les préférences';
                    }
                });
            }
            
        });

        function validateAssessmentForm() {
            // Check if at least one checkbox is selected in each section
            const sections = [
                {
                    name: 'Dangers',
                    prefix: 'danger-',
                    checkboxes: [
                        'danger-produits', 'danger-pression', 'danger-depression', 'danger-temp-haute',
                        'danger-temp-basse', 'danger-radioactifs', 'danger-electrique', 'danger-hauteur',
                        'danger-confine', 'danger-excavation', 'danger-levage', 'danger-acces',
                        'danger-obscurite', 'danger-bruits', 'danger-machine'
                    ]
                },
                {
                    name: 'Préparation',
                    prefix: 'prep-',
                    checkboxes: [
                        'prep-depressurisation', 'prep-vidange', 'prep-lavage', 'prep-nettoyage',
                        'prep-inertage-vapeur', 'prep-inertage-azote'
                    ]
                },
                {
                    name: 'Zone',
                    prefix: 'zone-',
                    checkboxes: [
                        'zone-feu-gaz', 'zone-esd', 'zone-coordination'
                    ]
                },
                {
                    name: 'Responsabilités',
                    prefix: 'resp-',
                    checkboxes: [
                        'resp-gants-bottes', 'resp-vetements', 'resp-ari', 'resp-masque',
                        'resp-combinaison', 'resp-protection-auditive', 'resp-couverture',
                        'resp-balisage', 'resp-eclairage', 'resp-personnel', 'resp-supervision'
                    ]
                }
            ];

            const missingSections = [];

            // Check each section
            sections.forEach(section => {
                let hasSelection = false;
                section.checkboxes.forEach(checkboxId => {
                    const checkbox = document.getElementById(checkboxId);
                    if (checkbox && checkbox.checked) {
                        hasSelection = true;
                    }
                });

                if (!hasSelection) {
                    missingSections.push(section.name);
                }
            });

            // Show validation message if any sections are missing
            if (missingSections.length > 0) {
                let message = '⚠️ Validation requise\n\nVeuillez sélectionner au moins une option dans ';
                if (missingSections.length === 1) {
                    message += `la section "${missingSections[0]}".`;
                } else if (missingSections.length === 2) {
                    message += `les sections "${missingSections[0]}" et "${missingSections[1]}".`;
                } else {
                    message += `les sections suivantes :\n\n`;
                    missingSections.forEach((section, index) => {
                        message += `• ${section}\n`;
                    });
                }
                
                message += '\n💡 Astuce : Chaque section doit avoir au moins une sélection pour valider l\'évaluation des risques.';
                
                alert(message);
                
                // Scroll to the first missing section for better UX
                if (missingSections.length > 0) {
                    const firstMissingSection = sections.find(s => s.name === missingSections[0]);
                    if (firstMissingSection && firstMissingSection.checkboxes.length > 0) {
                        const firstCheckbox = document.getElementById(firstMissingSection.checkboxes[0]);
                        if (firstCheckbox) {
                            firstCheckbox.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    }
                }
                
                return false;
            }

            return true;
        }

        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll("section").forEach(sec => sec.classList.add('hidden'));
            
            // Show selected section
            document.getElementById(sectionId).classList.remove('hidden');
            
            // Update page title
            const titles = {
                'assessment': 'ÉVALUATION DES RISQUES',
                'profile': 'MON PROFIL',
                'permits': 'PERMIS EN ATTENTE',
                'history': 'SUSPENSION ET ANNULATION',
                'suspension-declaration': 'DÉCLARATION SUSPENSION/ANNULATION',
                'archive': 'ARCHIVE',
                'settings': 'PARAMÈTRES DE COMPTE'
            };
            document.getElementById("page-title").textContent = titles[sectionId];
            
            // Update active link in sidebar
            document.querySelectorAll(".menu-link").forEach(link => {
                link.classList.remove("active");
            });
            event.currentTarget.classList.add("active");
        }

        function evaluatePermit(permitId) {
            // Store the permit ID for evaluation
            sessionStorage.setItem('currentPermitId', permitId);
            
            // Switch to assessment section
            showSection('assessment');
            
            // Update the assessment form title to show which permit is being evaluated
            const assessmentTitle = document.querySelector('#assessment .content-title');
            if (assessmentTitle) {
                assessmentTitle.textContent = `Évaluation des Risques - Permis ${permitId}`;
            }
            
            // Show success message
            alert(`Évaluation des risques pour le permis ${permitId} commencée.`);
        }

        function markPermitAsEvaluated(permitId) {
            // Find the permit row
            const permitRow = document.querySelector(`tr[data-permit-id="${permitId}"]`);
            if (permitRow) {
                // Find the actions cell (last td)
                const actionsCell = permitRow.querySelector('td:last-child');
                if (actionsCell) {
                    // Replace the buttons with approve button
                    actionsCell.innerHTML = `
                        <div class="table-actions">
                            <button class="btn-table btn-view" onclick="approvePermit('${permitId}')" style="background-color: var(--success);">
                                <i class='bx bx-check'></i>
                                Approuver le permis
                            </button>
                        </div>
                    `;
                }
            }
        }

        function approvePermit(permitId) {
            if (confirm(`Êtes-vous sûr de vouloir approuver le permis ${permitId} ?`)) {
                // Find and remove the permit row
                const permitRow = document.querySelector(`tr[data-permit-id="${permitId}"]`);
                if (permitRow) {
                    permitRow.remove();
                    alert(`Permis ${permitId} approuvé avec succès !`);
                }
            }
        }

        function logout() {
            if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                alert('Déconnexion en cours...');
                // Add your logout logic here
                window.location.href = 'login.html'; // Redirect to login page
            }
        }

        // Suspension/Annulation Functions
        function declareSuspensionAnnulation(permitId) {
            // Store the permit ID for suspension/annulation
            sessionStorage.setItem('currentSuspensionPermitId', permitId);
            
            // Get permit details from the table
            const permitRow = document.querySelector(`tr[data-permit-id="${permitId}"]`);
            if (permitRow) {
                const cells = permitRow.querySelectorAll('td');
                const permitData = {
                    number: cells[0].textContent.trim(),
                    description: cells[1].textContent.trim(),
                    zone: cells[2].textContent.trim(),
                    requester: cells[3].textContent.trim()
                };
                
                // Fill the suspension form with permit data
                document.getElementById('permitNumber').value = permitData.number;
                document.getElementById('permitDescription').value = permitData.description;
                document.getElementById('permitZone').value = permitData.zone;
                document.getElementById('permitRequester').value = permitData.requester;
            }
            
            // Switch to suspension declaration section
            showSection('suspension-declaration');
            
            // Update the form title
            const formTitle = document.querySelector('#suspension-declaration .content-title');
            if (formTitle) {
                formTitle.textContent = `Déclaration de Suspension/Annulation - Permis ${permitId}`;
            }
        }

        function viewPermitDetails(permitId) {
            // Get permit details from the table
            const permitRow = document.querySelector(`tr[data-permit-id="${permitId}"]`);
            if (permitRow) {
                const cells = permitRow.querySelectorAll('td');
                let permitData;
                
                // Check if this is from "Permis en attente" table (6 columns) or "Suspension et Annulation" table (7 columns)
                if (cells.length >= 7 && cells[5].textContent.includes('/')) {
                    // "Suspension et Annulation" table format
                    permitData = {
                        number: cells[0].textContent.trim(),
                        description: cells[1].textContent.trim(),
                        zone: cells[2].textContent.trim(),
                        requester: cells[3].textContent.trim(),
                        startDate: cells[4].textContent.trim(),
                        endDate: cells[5].textContent.trim(),
                        status: cells[6].textContent.trim()
                    };
                } else {
                    // "Permis en attente" table format
                    permitData = {
                        number: cells[0].textContent.trim(),
                        description: cells[1].textContent.trim(),
                        zone: cells[2].textContent.trim(),
                        requester: cells[3].textContent.trim(),
                        startDate: cells[4].textContent.trim(),
                        endDate: 'En attente d\'approbation',
                        status: cells[5].textContent.trim()
                    };
                }
                
                // Fill modal with permit data
                document.getElementById('modalPermitTitle').textContent = `Détails du Permis ${permitData.number}`;
                document.getElementById('modalPermitNumber').textContent = permitData.number;
                document.getElementById('modalPermitDescription').textContent = permitData.description;
                document.getElementById('modalPermitZone').textContent = permitData.zone;
                document.getElementById('modalPermitRequester').textContent = permitData.requester;
                document.getElementById('modalStartDate').textContent = permitData.startDate;
                document.getElementById('modalEndDate').textContent = permitData.endDate;
                document.getElementById('modalStatus').textContent = permitData.status;
                
                // Show modal
                document.getElementById('permitModal').classList.add('active');
            } else {
                alert(`❌ Erreur: Impossible de trouver les détails du permis ${permitId}`);
            }
        }

        function closePermitModal() {
            document.getElementById('permitModal').classList.remove('active');
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('permitModal');
            if (e.target === modal) {
                closePermitModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePermitModal();
            }
        });

        // Cancel assessment function
        function cancelAssessment() {
            if (confirm('Êtes-vous sûr de vouloir annuler l\'évaluation des risques ? Toutes les données saisies seront perdues.')) {
                // Clear the form
                document.getElementById('assessmentForm').reset();
                
                // Clear the stored permit ID
                sessionStorage.removeItem('currentPermitId');
                
                // Return to permits section
                showSection('permits');
                
                // Reset the assessment form title
                const assessmentTitle = document.querySelector('#assessment .content-title');
                if (assessmentTitle) {
                    assessmentTitle.textContent = 'Évaluation des Risques';
                }
            }
        }

        // Initialize suspension form functionality
        document.addEventListener('DOMContentLoaded', function() {
            
            // Suspension form submission
            const suspensionForm = document.getElementById('suspensionForm');
            const suspensionSubmitBtn = document.getElementById('suspensionSubmitBtn');
            
            if (suspensionForm) {
                suspensionForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    
                    // Validate form
                    const reason = document.getElementById('reason').value;
                    const urgency = document.getElementById('urgency').value;
                    const comments = document.getElementById('comments').value;
                    
                    if (!reason || !urgency || !comments.trim()) {
                        alert('Veuillez remplir tous les champs obligatoires.');
                        return;
                    }
                    
                    // Disable submit button and show loading
                    suspensionSubmitBtn.disabled = true;
                    suspensionSubmitBtn.innerHTML = '<i class="bx bx-loader-alt bx-spin btn-icon"></i>Envoi en cours...';
                    
                    try {
                        // Simulate form submission
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        const permitId = sessionStorage.getItem('currentSuspensionPermitId');
                        
                        // Show success message
                        let successMessage = `✅ Déclaration de suspension et annulation envoyée avec succès !\n\n`;
                        successMessage += `Permis: ${permitId}\n`;
                        successMessage += `Action: Suspension et Annulation\n`;
                        successMessage += `Motif: ${document.getElementById('reason').options[document.getElementById('reason').selectedIndex].text}\n`;
                        successMessage += `\n📧 Notification automatiquement envoyée au coordinateur HSE.`;
                        
                        alert(successMessage);
                        
                        // Update permit status in the table
                        updatePermitStatus(permitId, 'suspension-annulation');
                        
                        // Reset form and return to history section
                        suspensionForm.reset();
                        showSection('history');
                        
                    } catch (error) {
                        alert('❌ Erreur lors de l\'envoi de la déclaration. Veuillez réessayer.');
                    } finally {
                        suspensionSubmitBtn.disabled = false;
                        suspensionSubmitBtn.innerHTML = '<i class="bx bx-send btn-icon"></i>Envoyer la déclaration';
                    }
                });
            }
            
            // Search functionality for active permits
            const searchActivePermits = document.getElementById('searchActivePermits');
            if (searchActivePermits) {
                searchActivePermits.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const tableRows = document.querySelectorAll('#activePermitsTableBody tr');
                    
                    tableRows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        if (text.includes(searchTerm)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            }
            
            // Status filter functionality
            const statusFilter = document.getElementById('statusFilter');
            if (statusFilter) {
                statusFilter.addEventListener('change', function() {
                    const filterValue = this.value;
                    const tableRows = document.querySelectorAll('#activePermitsTableBody tr');
                    
                    tableRows.forEach(row => {
                        if (!filterValue) {
                            row.style.display = '';
                        } else {
                            const statusBadge = row.querySelector('.priority-badge');
                            if (statusBadge) {
                                const statusText = statusBadge.textContent.toLowerCase();
                                let shouldShow = false;
                                switch(filterValue) {
                                    case 'en-cours':
                                        shouldShow = statusText.includes('en cours');
                                        break;
                                    case 'suspendu':
                                        shouldShow = statusText.includes('suspendu');
                                        break;
                                    case 'annule':
                                        shouldShow = statusText.includes('suspendu/annulé');
                                        break;
                                }
                                row.style.display = shouldShow ? '' : 'none';
                            }
                        }
                    });
                });
            }
        });

        function updatePermitStatus(permitId, actionType) {
            const permitRow = document.querySelector(`tr[data-permit-id="${permitId}"]`);
            if (permitRow) {
                const statusCell = permitRow.querySelector('.priority-badge');
                if (statusCell) {
                    if (actionType === 'suspension-annulation') {
                        statusCell.className = 'priority-badge high';
                        statusCell.textContent = 'Suspendu/Annulé';
                    }
                }
                
                // Update actions buttons
                const actionsCell = permitRow.querySelector('.table-actions');
                if (actionsCell) {
                    actionsCell.innerHTML = `
                        <button class="btn-table btn-view" onclick="viewPermitDetails('${permitId}')">
                            <i class='bx bx-show'></i>
                            Voir
                        </button>
                        <span class="text-muted" style="font-size: 12px; color: var(--gray-500);">
                            Suspendu/Annulé
                        </span>
                    `;
                }
            }
        }

        // Search functionality for permits table
        function searchPermitsTable() {
            const input = document.getElementById('searchPermitsInput');
            const filter = input.value.toUpperCase();
            const table = document.querySelector('#permits .table-container table');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length; j++) {
                    const cell = cells[j];
                    if (cell) {
                        const textValue = cell.textContent || cell.innerText;
                        if (textValue.toUpperCase().indexOf(filter) > -1) {
                            found = true;
                            break;
                        }
                    }
                }

                if (found) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        // Search functionality for history table
        function searchHistoryTable() {
            const input = document.getElementById('searchHistoryInput');
            const filter = input.value.toUpperCase();
            const table = document.querySelector('#history .table-container table');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length; j++) {
                    const cell = cells[j];
                    if (cell) {
                        const textValue = cell.textContent || cell.innerText;
                        if (textValue.toUpperCase().indexOf(filter) > -1) {
                            found = true;
                            break;
                        }
                    }
                }

                if (found) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        // Archive Section Functions
        function searchArchiveTable() {
            const input = document.getElementById('searchArchiveInput');
            const filter = input.value.toUpperCase();
            const table = document.querySelector('#archive .table-container table');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length; j++) {
                    const cell = cells[j];
                    if (cell) {
                        const textValue = cell.textContent || cell.innerText;
                        if (textValue.toUpperCase().indexOf(filter) > -1) {
                            found = true;
                            break;
                        }
                    }
                }

                if (found) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        function filterArchiveTable() {
            const typeFilter = document.getElementById('archiveTypeFilter').value.toLowerCase();
            const statusFilter = document.getElementById('archiveStatusFilter').value.toLowerCase();
            const zoneFilter = document.getElementById('archiveZoneFilter').value.toLowerCase();
            const dateStart = document.getElementById('archiveDateStart') ? document.getElementById('archiveDateStart').value : '';
            const dateEnd = document.getElementById('archiveDateEnd') ? document.getElementById('archiveDateEnd').value : '';
            const archivedByFilter = document.getElementById('archivedByFilter') ? document.getElementById('archivedByFilter').value.toLowerCase() : '';
            
            const table = document.querySelector('#archive .table-container table');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let shouldShow = true;

                // Type filter (column 1)
                if (typeFilter && cells[1]) {
                    const typeText = cells[1].textContent.toLowerCase();
                    if (!typeText.includes(typeFilter)) {
                        shouldShow = false;
                    }
                }

                // Status filter (column 6)
                if (statusFilter && cells[6]) {
                    const statusText = cells[6].textContent.toLowerCase();
                    if ((statusFilter === 'cloture' && !statusText.includes('clôturé')) ||
                        (statusFilter === 'annule' && !statusText.includes('annulé'))) {
                        shouldShow = false;
                    }
                }

                // Zone filter (column 2)
                if (zoneFilter && cells[2]) {
                    const zoneText = cells[2].textContent.toLowerCase();
                    if (!zoneText.includes(zoneFilter.toLowerCase())) {
                        shouldShow = false;
                    }
                }

                // Archived by filter (column 7)
                if (archivedByFilter && cells[7]) {
                    const archivedByText = cells[7].textContent.toLowerCase();
                    if (!archivedByText.includes(archivedByFilter)) {
                        shouldShow = false;
                    }
                }

                // Date filters (columns 4 and 5)
                if (dateStart && cells[4]) {
                    const startDateText = cells[4].textContent;
                    const startDate = parseDate(startDateText);
                    const filterStartDate = new Date(dateStart);
                    if (startDate < filterStartDate) {
                        shouldShow = false;
                    }
                }

                if (dateEnd && cells[5]) {
                    const endDateText = cells[5].textContent;
                    const endDate = parseDate(endDateText);
                    const filterEndDate = new Date(dateEnd);
                    if (endDate > filterEndDate) {
                        shouldShow = false;
                    }
                }

                row.style.display = shouldShow ? '' : 'none';
            }
        }

        function parseDate(dateString) {
            // Parse date in format DD/MM/YYYY
            const parts = dateString.split('/');
            if (parts.length === 3) {
                return new Date(parts[2], parts[1] - 1, parts[0]);
            }
            return new Date();
        }

        function resetArchiveFilters() {
            document.getElementById('searchArchiveInput').value = '';
            document.getElementById('archiveTypeFilter').value = '';
            document.getElementById('archiveStatusFilter').value = '';
            document.getElementById('archiveZoneFilter').value = '';
            
            if (document.getElementById('archiveDateStart')) {
                document.getElementById('archiveDateStart').value = '';
            }
            if (document.getElementById('archiveDateEnd')) {
                document.getElementById('archiveDateEnd').value = '';
            }
            if (document.getElementById('archivedByFilter')) {
                document.getElementById('archivedByFilter').value = '';
            }
            
            // Show all rows
            const table = document.querySelector('#archive .table-container table');
            const rows = table.getElementsByTagName('tr');
            for (let i = 1; i < rows.length; i++) {
                rows[i].style.display = '';
            }
        }

        function showArchiveFilters() {
            const filtersPanel = document.getElementById('advancedFilters');
            if (filtersPanel.style.display === 'none' || filtersPanel.style.display === '') {
                filtersPanel.style.display = 'block';
            } else {
                filtersPanel.style.display = 'none';
            }
        }

        function viewArchivedPermitDetails(permitId) {
            // Find the permit row in archive table
            const permitRow = document.querySelector(`#archive tr[data-permit-id="${permitId}"]`);

            if (permitRow) {
                const cells = permitRow.querySelectorAll('td');
                const permitData = {
                    number: cells[0].textContent.trim(),
                    description: cells[1].textContent.trim(),
                    zone: cells[2].textContent.trim(),
                    requester: cells[3].textContent.trim(),
                    startDate: cells[4].textContent.trim(),
                    endDate: cells[5].textContent.trim(),
                    status: cells[6].textContent.trim(),
                    archivedBy: cells[7].textContent.trim(),
                    archiveDate: cells[8].textContent.trim()
                };

                // Enhanced data for archived permits
                const enhancedData = getEnhancedPermitData(permitId, permitData);

                // Fill modal with permit data
                document.getElementById('modalPermitTitle').textContent = `Détails du Permis Archivé ${permitData.number}`;
                document.getElementById('modalPermitNumber').textContent = permitData.number;
                document.getElementById('modalPermitDescription').textContent = permitData.description;
                document.getElementById('modalPermitZone').textContent = permitData.zone;
                document.getElementById('modalPermitRequester').textContent = permitData.requester;
                document.getElementById('modalStartDate').textContent = permitData.startDate;
                document.getElementById('modalEndDate').textContent = permitData.endDate;
                document.getElementById('modalStatus').textContent = permitData.status;

                // Show archive-specific information
                document.getElementById('archiveInfoCard').style.display = 'block';
                document.getElementById('modalArchivedBy').textContent = permitData.archivedBy;
                document.getElementById('modalArchiveDate').textContent = permitData.archiveDate;
                document.getElementById('modalArchiveReason').textContent = enhancedData.archiveReason;
                document.getElementById('modalDuration').textContent = enhancedData.duration;

                // Show safety information
                document.getElementById('safetyInfoCard').style.display = 'block';
                document.getElementById('modalWorkType').textContent = enhancedData.workType;
                document.getElementById('modalRisks').textContent = enhancedData.risks;
                document.getElementById('modalSafetyMeasures').textContent = enhancedData.safetyMeasures;
                document.getElementById('modalPPE').textContent = enhancedData.ppe;

                // Show modal
                document.getElementById('permitModal').classList.add('active');
            } else {
                alert(`❌ Erreur: Impossible de trouver les détails du permis archivé ${permitId}`);
            }
        }

        function getEnhancedPermitData(permitId, basicData) {
            // Enhanced data based on permit ID and type
            const enhancedDataMap = {
                'PT-2024-001': {
                    workType: 'Travaux de maintenance préventive',
                    risks: 'Risques mécaniques, électriques',
                    safetyMeasures: 'Consignation électrique, EPI complets, surveillance continue',
                    ppe: 'Casque, gants isolants, chaussures de sécurité, lunettes',
                    archiveReason: 'Travaux terminés avec succès',
                    duration: '4 jours'
                },
                'PT-2024-002': {
                    workType: 'Réparation d\'urgence',
                    risks: 'Pression résiduelle, produits chimiques',
                    safetyMeasures: 'Dépressurisation, détection gaz, ventilation forcée',
                    ppe: 'Combinaison chimique, masque respiratoire, gants nitrile',
                    archiveReason: 'Annulé - Conditions météorologiques défavorables',
                    duration: '2 jours (interrompu)'
                },
                'PT-2024-003': {
                    workType: 'Installation d\'équipements',
                    risks: 'Travail en hauteur, manutention',
                    safetyMeasures: 'Harnais de sécurité, plan de levage, signalisation',
                    ppe: 'Harnais, casque, gants anti-coupure, chaussures antidérapantes',
                    archiveReason: 'Installation réussie et testée',
                    duration: '4 jours'
                },
                'PT-2024-004': {
                    workType: 'Nettoyage industriel',
                    risks: 'Espaces confinés, produits chimiques',
                    safetyMeasures: 'Surveillance atmosphérique, ventilation, équipe de secours',
                    ppe: 'Appareil respiratoire autonome, combinaison étanche',
                    archiveReason: 'Nettoyage terminé selon procédures',
                    duration: '5 jours'
                },
                'PT-2024-005': {
                    workType: 'Travaux à froid - Maintenance électrique',
                    risks: 'Électrocution, court-circuit',
                    safetyMeasures: 'Consignation LOTO, vérification absence tension, isolation',
                    ppe: 'Gants isolants classe 2, écran facial, vêtements ignifugés',
                    archiveReason: 'Maintenance préventive réalisée',
                    duration: '5 jours'
                },
                'PT-2024-006': {
                    workType: 'Travaux à froid - Inspection tuyauterie',
                    risks: 'Chute, produits résiduels',
                    safetyMeasures: 'Échafaudage sécurisé, purge complète, test étanchéité',
                    ppe: 'Harnais, détecteur de gaz portable, gants résistants',
                    archiveReason: 'Inspection terminée - Conformité validée',
                    duration: '5 jours'
                },
                'PT-2024-007': {
                    workType: 'Travaux à froid - Calibrage instruments',
                    risks: 'Erreur de mesure, dysfonctionnement',
                    safetyMeasures: 'Procédure de calibrage certifiée, double vérification',
                    ppe: 'Gants de précision, lunettes de protection',
                    archiveReason: 'Annulé - Équipement de calibrage défaillant',
                    duration: '5 jours (interrompu)'
                },
                'PT-2024-008': {
                    workType: 'Travaux à froid - Maintenance mécanique',
                    risks: 'Pièces en mouvement, huiles sous pression',
                    safetyMeasures: 'Arrêt machine, purge hydraulique, blocage mécanique',
                    ppe: 'Gants mécaniques, lunettes, vêtements ajustés',
                    archiveReason: 'Maintenance réussie - Tests de fonctionnement OK',
                    duration: '5 jours'
                }
            };

            return enhancedDataMap[permitId] || {
                workType: 'Type de travaux non spécifié',
                risks: 'Risques standards selon procédure',
                safetyMeasures: 'Mesures de sécurité standard',
                ppe: 'EPI standard requis',
                archiveReason: basicData.status.includes('Clôturé') ? 'Travaux terminés' : 'Travaux annulés',
                duration: 'Non spécifié'
            };
        }

        function downloadPermitPDF(permitId) {
            // Get permit info for PDF generation
            const permitRow = document.querySelector(`#archive tr[data-permit-id="${permitId}"]`);
            if (!permitRow) {
                alert('❌ Erreur: Permis introuvable');
                return;
            }

            const cells = permitRow.querySelectorAll('td');
            const permitInfo = {
                number: cells[0].textContent.trim(),
                description: cells[1].textContent.trim(),
                zone: cells[2].textContent.trim(),
                requester: cells[3].textContent.trim(),
                status: cells[6].textContent.trim()
            };

            // Simulate PDF generation process
            alert(`📄 Génération du PDF en cours...\n\n📋 Permis: ${permitInfo.number}\n🔧 Travaux: ${permitInfo.description}\n📍 Zone: ${permitInfo.zone}\n👤 Demandeur: ${permitInfo.requester}\n📊 Statut: ${permitInfo.status}\n\n⏳ Veuillez patienter...`);

            // Simulate download progress
            setTimeout(() => {
                const filename = `Permis_${permitId}_Archive.pdf`;
                alert(`✅ PDF généré avec succès!\n\n📁 Fichier: ${filename}\n📊 Taille: 1.8 MB\n📋 Contenu:\n• Informations complètes du permis\n• Historique des modifications\n• Signatures électroniques\n• Mesures de sécurité appliquées\n• Commentaires d'archivage\n\n💾 Le fichier sera téléchargé dans votre dossier de téléchargements.`);

                // In a real application, this would trigger an actual PDF download
                // window.open(`/api/permits/${permitId}/pdf`, '_blank');
            }, 1500);
        }

        function addArchiveComment(permitId) {
            // Get permit info for context
            const permitRow = document.querySelector(`#archive tr[data-permit-id="${permitId}"]`);
            const permitDescription = permitRow ? permitRow.querySelectorAll('td')[1].textContent.trim() : 'Permis inconnu';

            const comment = prompt(`💬 Ajouter un commentaire post-archivage\n\n📋 Permis: ${permitId}\n🔧 Travaux: ${permitDescription}\n\n📝 Votre commentaire (max 500 caractères):`);

            if (comment && comment.trim()) {
                if (comment.trim().length > 500) {
                    alert('⚠️ Le commentaire ne peut pas dépasser 500 caractères.\n\n📏 Longueur actuelle: ' + comment.trim().length + ' caractères');
                    return;
                }

                // Simulate saving comment
                const timestamp = new Date().toLocaleString('fr-FR');
                const commentId = 'COM-' + Date.now();

                alert(`✅ Commentaire ajouté avec succès!\n\n📋 Permis: ${permitId}\n🆔 ID Commentaire: ${commentId}\n📝 Contenu: "${comment.trim()}"\n📅 Date: ${timestamp}\n👤 Auteur: Marie Sécurité (Responsable HSE)\n\n💾 Le commentaire a été enregistré dans l'historique du permis et sera visible dans tous les rapports futurs.`);

                // In a real application, this would save the comment to the database
                // saveArchiveComment(permitId, {
                //     id: commentId,
                //     content: comment.trim(),
                //     author: 'Marie Sécurité',
                //     role: 'Responsable HSE',
                //     timestamp: new Date().toISOString()
                // });
            } else if (comment !== null) {
                alert('⚠️ Le commentaire ne peut pas être vide.\n\n💡 Conseil: Ajoutez des informations utiles comme:\n• Observations post-travaux\n• Recommandations pour l\'avenir\n• Points d\'amélioration identifiés\n• Retour d\'expérience');
            }
        }

        function closePermitModal() {
            document.getElementById('permitModal').classList.remove('active');
            // Hide archive-specific cards when closing
            document.getElementById('archiveInfoCard').style.display = 'none';
            document.getElementById('safetyInfoCard').style.display = 'none';
        }

        function exportArchiveToExcel() {
            // Get current filter values for export info
            const typeFilter = document.getElementById('archiveTypeFilter').value;
            const statusFilter = document.getElementById('archiveStatusFilter').value;
            const zoneFilter = document.getElementById('archiveZoneFilter').value;

            let filterInfo = '';
            if (typeFilter || statusFilter || zoneFilter) {
                filterInfo = '\n\n🔍 Filtres appliqués:';
                if (typeFilter) filterInfo += `\n• Type: ${typeFilter}`;
                if (statusFilter) filterInfo += `\n• Statut: ${statusFilter}`;
                if (zoneFilter) filterInfo += `\n• Zone: ${zoneFilter}`;
            }

            alert(`📊 Export Excel en cours...\n\n✅ Le fichier "Archive_Permis_${new Date().toISOString().split('T')[0]}.xlsx" sera téléchargé dans quelques secondes.\n\n📋 Contenu:\n• ${document.querySelectorAll('#archive tbody tr:not([style*="display: none"])').length} permis archivés\n• Informations complètes de sécurité\n• Statistiques d'archivage\n• Historique des commentaires${filterInfo}`);

            // Simulate download progress
            setTimeout(() => {
                alert(`✅ Export terminé!\n\n📁 Fichier téléchargé: Archive_Permis_${new Date().toISOString().split('T')[0]}.xlsx\n📊 Taille: 2.3 MB\n📈 ${document.querySelectorAll('#archive tbody tr').length} permis exportés avec succès`);
            }, 2000);
        }
    </script>
</body>
</html>