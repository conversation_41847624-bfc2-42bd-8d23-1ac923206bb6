<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Système de Gestion des Permis de Travail - Responsable d'Exécution</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"/>
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet"/>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #3b82f6;
            --primary-dark: #2563eb;
            --primary-light: #93c5fd;
            --secondary: #4f46e5;
            --accent: #3b82f6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --sidebar-width: 280px;
            --topbar-height: 70px;
            --sidebar-collapsed-width: 80px;
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;
            --transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--gray-50);
            color: var(--gray-900);
            display: flex;
            min-height: 100vh;
            line-height: 1.5;
            font-size: 15px;
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--gray-900);
            color: var(--gray-100);
            position: fixed;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
            transition: var(--transition-slow);
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            border-right: 1px solid rgba(255, 255, 255, 0.05);
            overflow: hidden;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar.collapsed .logo-text,
        .sidebar.collapsed .menu-title,
        .sidebar.collapsed .menu-text,
        .sidebar.collapsed .menu-badge,
        .sidebar.collapsed .user-info,
        .sidebar.collapsed .user-actions {
            opacity: 0;
            width: 0;
            height: 0;
            margin: 0;
            padding: 0;
            overflow: hidden;
            position: absolute;
        }

        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: var(--topbar-height);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 700;
            font-size: 18px;
            color: var(--gray-100);
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            box-shadow: var(--shadow-md);
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--gray-400);
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: var(--radius-md);
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar-toggle:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--gray-100);
        }

        .sidebar-menu {
            flex: 1;
            padding: 20px 0;
            overflow-y: auto;
        }

        .menu-group {
            margin-bottom: 32px;
        }

        .menu-title {
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: var(--gray-400);
            margin-bottom: 12px;
            padding: 0 20px;
        }

        .menu-items {
            list-style: none;
        }

        .menu-item {
            margin-bottom: 4px;
        }

        .menu-link {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            color: var(--gray-300);
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
            border-radius: 0;
            position: relative;
        }

        .menu-link:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--gray-100);
        }

        .menu-link.active {
            background-color: var(--primary);
            color: white;
            font-weight: 500;
        }

        .menu-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background-color: var(--primary-light);
        }

        .menu-icon {
            font-size: 20px;
            min-width: 20px;
        }

        .menu-text {
            font-size: 14px;
            font-weight: 500;
        }

        .menu-badge {
            background-color: var(--danger);
            color: white;
            font-size: 11px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: var(--radius-full);
            min-width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.05);
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: var(--radius-lg);
            background-color: rgba(255, 255, 255, 0.05);
            transition: var(--transition);
        }

        .user-profile:hover {
            background-color: rgba(255, 255, 255, 0.08);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            color: white;
            position: relative;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background-color: var(--danger);
            color: white;
            font-size: 10px;
            font-weight: 600;
            padding: 2px 5px;
            border-radius: var(--radius-full);
            min-width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid var(--gray-900);
        }

        .user-info {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            font-weight: 600;
            font-size: 14px;
            color: var(--gray-100);
            margin-bottom: 2px;
        }

        .user-role {
            font-size: 12px;
            color: var(--gray-400);
        }

        .user-actions {
            display: flex;
            gap: 4px;
        }

        .action-btn {
            background: none;
            border: none;
            color: var(--gray-400);
            font-size: 16px;
            cursor: pointer;
            padding: 6px;
            border-radius: var(--radius-md);
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--gray-100);
        }

        /* Main Content Styles */
        .main-content {
            margin-left: var(--sidebar-width);
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            transition: var(--transition-slow);
        }

        .sidebar.collapsed + .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        .topbar {
            height: var(--topbar-height);
            background-color: white;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 32px;
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--gray-900);
        }

        .topbar-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .search-bar {
            position: relative;
            width: 320px;
        }

        .search-bar input {
            width: 100%;
            padding: 10px 16px 10px 40px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-lg);
            font-size: 14px;
            background-color: var(--gray-50);
            transition: var(--transition);
        }

        .search-bar input:focus {
            outline: none;
            border-color: var(--primary);
            background-color: white;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-bar .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: 16px;
        }

        .content-area {
            flex: 1;
            padding: 32px;
            overflow-y: auto;
        }

        .content-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 32px;
        }

        .content-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 8px;
        }

        .content-subtitle {
            font-size: 16px;
            color: var(--gray-600);
        }

        .content-actions {
            display: flex;
            gap: 12px;
        }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border: none;
            border-radius: var(--radius-md);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .btn-outline {
            background-color: white;
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
        }

        .btn-outline:hover {
            background-color: var(--gray-50);
            border-color: var(--gray-400);
        }

        .btn-icon {
            font-size: 16px;
        }

        /* Table Styles */
        .table-container {
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            border: 1px solid var(--gray-200);
        }

        .compact-table {
            width: 100%;
            border-collapse: collapse;
        }

        .compact-table th {
            background-color: var(--gray-50);
            padding: 16px;
            text-align: left;
            font-weight: 600;
            font-size: 13px;
            color: var(--gray-700);
            border-bottom: 1px solid var(--gray-200);
            white-space: nowrap;
        }

        .compact-table td {
            padding: 16px;
            border-bottom: 1px solid var(--gray-100);
            font-size: 14px;
            color: var(--gray-900);
        }

        .compact-table tr:last-child td {
            border-bottom: none;
        }

        .compact-table tr:hover {
            background-color: var(--gray-50);
        }

        .table-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .btn-table {
            padding: 6px 12px;
            border: none;
            border-radius: var(--radius-md);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 4px;
            white-space: nowrap;
        }

        .btn-view {
            background-color: var(--info);
            color: white;
        }

        .btn-view:hover {
            background-color: #0891b2;
            transform: translateY(-1px);
        }

        .btn-edit {
            background-color: var(--warning);
            color: white;
        }

        .btn-edit:hover {
            background-color: #d97706;
            transform: translateY(-1px);
        }

        .btn-delete {
            background-color: var(--danger);
            color: white;
        }

        .btn-delete:hover {
            background-color: #dc2626;
            transform: translateY(-1px);
        }

        .btn-success {
            background-color: var(--success);
            color: white;
        }

        .btn-success:hover {
            background-color: #059669;
            transform: translateY(-1px);
        }

        /* Badge Styles */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: var(--radius-full);
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-success {
            background-color: #dcfce7;
            color: #166534;
        }

        .badge-warning {
            background-color: #fef3c7;
            color: #92400e;
        }

        .badge-danger {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .badge-info {
            background-color: #e0f2fe;
            color: #0c4a6e;
        }

        .badge-secondary {
            background-color: var(--gray-100);
            color: var(--gray-600);
        }

        /* Form Styles */
        .form-section {
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            margin-bottom: 24px;
        }

        .section-header {
            padding: 24px 24px 0;
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
        }

        .section-icon {
            font-size: 24px;
            color: var(--primary);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--gray-900);
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- Sidebar Navigation -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <div class="logo-icon">
                    <img src="sonatrach-logo.png" alt="Logo Sonatrach" style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;"/>
                </div>
                <span class="logo-text">PT Pro</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class='bx bx-chevron-left'></i>
            </button>
        </div>
        <nav class="sidebar-menu">
            <div class="menu-group">
                <h3 class="menu-title">Navigation</h3>
                <ul class="menu-items">
                    <li class="menu-item">
                        <div class="menu-link active" onclick="showSection('dashboard')">
                            <i class='bx bxs-dashboard menu-icon'></i>
                            <span class="menu-text">Tableau de bord</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('mes-permis')">
                            <i class='bx bx-file menu-icon'></i>
                            <span class="menu-text">Mes permis</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('declaration-fin')">
                            <i class='bx bx-check-circle menu-icon'></i>
                            <span class="menu-text">Déclaration fin travaux</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('demande-prolongation')">
                            <i class='bx bx-time-five menu-icon'></i>
                            <span class="menu-text">Demande prolongation</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('incidents')">
                            <i class='bx bx-error-circle menu-icon'></i>
                            <span class="menu-text">Signaler incident</span>
                        </div>
                    </li>
                </ul>
            </div>

            <div class="menu-group">
                <h3 class="menu-title">Compte</h3>
                <ul class="menu-items">
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('profile')">
                            <i class='bx bxs-user menu-icon'></i>
                            <span class="menu-text">Mon Profil</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('settings')">
                            <i class='bx bx-cog menu-icon'></i>
                            <span class="menu-text">Paramètres</span>
                        </div>
                    </li>
                </ul>
            </div>
        </nav>
        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">
                    RE
                    <span class="notification-badge">2</span>
                </div>
                <div class="user-info">
                    <div class="user-name">Resp. Exécution</div>
                    <div class="user-role">Responsable d'Exécution</div>
                </div>
                <div class="user-actions">
                    <button class="action-btn" onclick="logout()">
                        <i class='bx bx-log-out'></i>
                    </button>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <div class="topbar">
            <h1 class="page-title" id="page-title">TABLEAU DE BORD</h1>
            <div class="topbar-actions">
                <div class="search-bar">
                    <i class='bx bx-search search-icon'></i>
                    <input type="text" placeholder="Rechercher un permis...">
                </div>
            </div>
        </div>

        <div class="content-area">
            <!-- Dashboard Section -->
            <section id="dashboard">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Tableau de bord</h1>
                        <p class="content-subtitle">Vue d'ensemble de vos permis de travail et activités</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-outline" onclick="refreshDashboard()">
                            <i class='bx bx-refresh btn-icon'></i>
                            Actualiser
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 24px; margin-bottom: 32px;">
                    <div class="form-section" style="padding: 24px;">
                        <div style="display: flex; align-items: center; gap: 16px;">
                            <div style="width: 48px; height: 48px; background: linear-gradient(135deg, var(--primary), var(--secondary)); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">
                                <i class='bx bx-file'></i>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: 700; color: var(--gray-900); margin-bottom: 4px;">5</div>
                                <div style="font-size: 14px; color: var(--gray-600);">Permis actifs</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section" style="padding: 24px;">
                        <div style="display: flex; align-items: center; gap: 16px;">
                            <div style="width: 48px; height: 48px; background: linear-gradient(135deg, var(--warning), #f97316); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">
                                <i class='bx bx-time-five'></i>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: 700; color: var(--gray-900); margin-bottom: 4px;">2</div>
                                <div style="font-size: 14px; color: var(--gray-600);">En cours d'exécution</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section" style="padding: 24px;">
                        <div style="display: flex; align-items: center; gap: 16px;">
                            <div style="width: 48px; height: 48px; background: linear-gradient(135deg, var(--success), #059669); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">
                                <i class='bx bx-check-circle'></i>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: 700; color: var(--gray-900); margin-bottom: 4px;">12</div>
                                <div style="font-size: 14px; color: var(--gray-600);">Travaux terminés</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section" style="padding: 24px;">
                        <div style="display: flex; align-items: center; gap: 16px;">
                            <div style="width: 48px; height: 48px; background: linear-gradient(135deg, var(--danger), #dc2626); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">
                                <i class='bx bx-error-circle'></i>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: 700; color: var(--gray-900); margin-bottom: 4px;">1</div>
                                <div style="font-size: 14px; color: var(--gray-600);">Incident signalé</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Permits Table -->
                <div class="form-section">
                    <div class="section-header">
                        <i class='bx bx-file section-icon'></i>
                        <h2 class="section-title">Permis récents</h2>
                    </div>
                    <div style="padding: 0 24px 24px;">
                        <div class="table-container">
                            <table class="compact-table">
                                <thead>
                                    <tr>
                                        <th>ID Permis</th>
                                        <th>Type de travail</th>
                                        <th>Zone</th>
                                        <th>Date début</th>
                                        <th>Date fin</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>PT-2024-001</td>
                                        <td>Travaux de maintenance</td>
                                        <td>Zone A - Production</td>
                                        <td>20/01/2024</td>
                                        <td>22/01/2024</td>
                                        <td><span class="badge badge-warning">En cours</span></td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="btn-table btn-view" onclick="voirPermis('PT-2024-001')">
                                                    <i class='bx bx-show'></i>
                                                    Voir
                                                </button>
                                                <button class="btn-table btn-success" onclick="declarerFin('PT-2024-001')">
                                                    <i class='bx bx-check'></i>
                                                    Déclarer fin
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>PT-2024-003</td>
                                        <td>Travaux d'urgence</td>
                                        <td>Zone C - Laboratoire</td>
                                        <td>18/01/2024</td>
                                        <td>21/01/2024</td>
                                        <td><span class="badge badge-warning">En cours</span></td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="btn-table btn-view" onclick="voirPermis('PT-2024-003')">
                                                    <i class='bx bx-show'></i>
                                                    Voir
                                                </button>
                                                <button class="btn-table btn-edit" onclick="demanderProlongation('PT-2024-003')">
                                                    <i class='bx bx-time'></i>
                                                    Prolonger
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>PT-2024-005</td>
                                        <td>Travaux routiniers</td>
                                        <td>Zone B - Stockage</td>
                                        <td>19/01/2024</td>
                                        <td>20/01/2024</td>
                                        <td><span class="badge badge-success">Terminé</span></td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="btn-table btn-view" onclick="voirPermis('PT-2024-005')">
                                                    <i class='bx bx-show'></i>
                                                    Voir
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Mes Permis Section -->
            <section id="mes-permis" class="hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Mes permis de travail</h1>
                        <p class="content-subtitle">Consultez tous vos permis de travail assignés</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-outline" onclick="refreshPermis()">
                            <i class='bx bx-refresh btn-icon'></i>
                            Actualiser
                        </button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div style="margin-bottom: 24px;">
                    <div class="search-bar">
                        <i class='bx bx-search search-icon'></i>
                        <input type="text" id="searchMesPermis" placeholder="Rechercher un permis..." onkeyup="searchMesPermis()">
                    </div>
                </div>

                <div class="table-container">
                    <table class="compact-table">
                        <thead>
                            <tr>
                                <th>ID Permis</th>
                                <th>Type de travail</th>
                                <th>Zone</th>
                                <th>Date début</th>
                                <th>Date fin</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="mesPermisTableBody">
                            <tr data-id="PT-2024-001">
                                <td>PT-2024-001</td>
                                <td>Travaux de maintenance électrique</td>
                                <td>Zone A - Production</td>
                                <td>20/01/2024</td>
                                <td>22/01/2024</td>
                                <td><span class="badge badge-warning">En cours</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="voirPermis('PT-2024-001')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-success" onclick="declarerFin('PT-2024-001')">
                                            <i class='bx bx-check'></i>
                                            Déclarer fin
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-id="PT-2024-003">
                                <td>PT-2024-003</td>
                                <td>Travaux d'urgence - Réparation fuite</td>
                                <td>Zone C - Laboratoire</td>
                                <td>18/01/2024</td>
                                <td>21/01/2024</td>
                                <td><span class="badge badge-warning">En cours</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="voirPermis('PT-2024-003')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-edit" onclick="demanderProlongation('PT-2024-003')">
                                            <i class='bx bx-time'></i>
                                            Prolonger
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-id="PT-2024-005">
                                <td>PT-2024-005</td>
                                <td>Travaux routiniers - Nettoyage</td>
                                <td>Zone B - Stockage</td>
                                <td>19/01/2024</td>
                                <td>20/01/2024</td>
                                <td><span class="badge badge-success">Terminé</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="voirPermis('PT-2024-005')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-id="PT-2024-007">
                                <td>PT-2024-007</td>
                                <td>Travaux de peinture</td>
                                <td>Zone D - Bureaux</td>
                                <td>22/01/2024</td>
                                <td>24/01/2024</td>
                                <td><span class="badge badge-info">Programmé</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="voirPermis('PT-2024-007')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Déclaration fin travaux Section -->
            <section id="declaration-fin" class="hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Déclaration fin de travaux</h1>
                        <p class="content-subtitle">Déclarez la fin de vos travaux et clôturez les permis</p>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-header">
                        <i class='bx bx-check-circle section-icon'></i>
                        <h2 class="section-title">Déclaration de fin de travaux</h2>
                    </div>
                    <div style="padding: 0 24px 24px;">
                        <form id="declarationFinForm">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
                                <div>
                                    <label style="display: block; font-weight: 500; margin-bottom: 8px; color: var(--gray-700);">
                                        <i class='bx bx-file' style="margin-right: 8px;"></i>
                                        ID Permis <span style="color: var(--danger);">*</span>
                                    </label>
                                    <select id="permisSelect" required style="width: 100%; padding: 12px; border: 1px solid var(--gray-300); border-radius: var(--radius-md); font-size: 14px;">
                                        <option value="">Sélectionnez un permis</option>
                                        <option value="PT-2024-001">PT-2024-001 - Travaux de maintenance électrique</option>
                                        <option value="PT-2024-003">PT-2024-003 - Travaux d'urgence - Réparation fuite</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; font-weight: 500; margin-bottom: 8px; color: var(--gray-700);">
                                        <i class='bx bx-calendar' style="margin-right: 8px;"></i>
                                        Date et heure de fin <span style="color: var(--danger);">*</span>
                                    </label>
                                    <input type="datetime-local" id="dateFinTravaux" required style="width: 100%; padding: 12px; border: 1px solid var(--gray-300); border-radius: var(--radius-md); font-size: 14px;">
                                </div>
                            </div>

                            <div style="margin-bottom: 24px;">
                                <label style="display: block; font-weight: 500; margin-bottom: 8px; color: var(--gray-700);">
                                    <i class='bx bx-message-detail' style="margin-right: 8px;"></i>
                                    Remarques et observations <span style="color: var(--danger);">*</span>
                                </label>
                                <textarea id="remarquesFin" required rows="4" placeholder="Décrivez l'état des travaux, les conditions de sécurité, et toute observation importante..." style="width: 100%; padding: 12px; border: 1px solid var(--gray-300); border-radius: var(--radius-md); font-size: 14px; resize: vertical;"></textarea>
                            </div>

                            <div style="margin-bottom: 24px;">
                                <label style="display: block; font-weight: 500; margin-bottom: 12px; color: var(--gray-700);">
                                    <i class='bx bx-check-shield' style="margin-right: 8px;"></i>
                                    Vérifications de sécurité
                                </label>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                    <label style="display: flex; align-items: center; gap: 8px; padding: 12px; border: 1px solid var(--gray-200); border-radius: var(--radius-md); cursor: pointer;">
                                        <input type="checkbox" id="zoneNettoyee" style="margin: 0;">
                                        <span style="font-size: 14px;">Zone de travail nettoyée</span>
                                    </label>
                                    <label style="display: flex; align-items: center; gap: 8px; padding: 12px; border: 1px solid var(--gray-200); border-radius: var(--radius-md); cursor: pointer;">
                                        <input type="checkbox" id="equipementsRetires" style="margin: 0;">
                                        <span style="font-size: 14px;">Équipements retirés</span>
                                    </label>
                                    <label style="display: flex; align-items: center; gap: 8px; padding: 12px; border: 1px solid var(--gray-200); border-radius: var(--radius-md); cursor: pointer;">
                                        <input type="checkbox" id="securiteRespecte" style="margin: 0;">
                                        <span style="font-size: 14px;">Consignes de sécurité respectées</span>
                                    </label>
                                    <label style="display: flex; align-items: center; gap: 8px; padding: 12px; border: 1px solid var(--gray-200); border-radius: var(--radius-md); cursor: pointer;">
                                        <input type="checkbox" id="aucunIncident" style="margin: 0;">
                                        <span style="font-size: 14px;">Aucun incident signalé</span>
                                    </label>
                                </div>
                            </div>

                            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                                <button type="button" class="btn btn-outline" onclick="resetDeclarationForm()">
                                    <i class='bx bx-refresh btn-icon'></i>
                                    Réinitialiser
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class='bx bx-check btn-icon'></i>
                                    Déclarer fin de travaux
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Historique des déclarations -->
                <div class="form-section">
                    <div class="section-header">
                        <i class='bx bx-history section-icon'></i>
                        <h2 class="section-title">Historique des déclarations</h2>
                    </div>
                    <div style="padding: 0 24px 24px;">
                        <div class="table-container">
                            <table class="compact-table">
                                <thead>
                                    <tr>
                                        <th>ID Permis</th>
                                        <th>Type de travail</th>
                                        <th>Date déclaration</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>PT-2024-005</td>
                                        <td>Travaux routiniers - Nettoyage</td>
                                        <td>20/01/2024 16:30</td>
                                        <td><span class="badge badge-success">Confirmé par AZ</span></td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="btn-table btn-view" onclick="voirDeclaration('PT-2024-005')">
                                                    <i class='bx bx-show'></i>
                                                    Voir
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>PT-2024-002</td>
                                        <td>Travaux de maintenance</td>
                                        <td>18/01/2024 14:15</td>
                                        <td><span class="badge badge-success">Confirmé par AZ</span></td>
                                        <td>
                                            <div class="table-actions">
                                                <button class="btn-table btn-view" onclick="voirDeclaration('PT-2024-002')">
                                                    <i class='bx bx-show'></i>
                                                    Voir
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Demande prolongation Section -->
            <section id="demande-prolongation" class="hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Demande de prolongation</h1>
                        <p class="content-subtitle">Demandez une prolongation pour vos permis de travail</p>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-header">
                        <i class='bx bx-time-five section-icon'></i>
                        <h2 class="section-title">Nouvelle demande de prolongation</h2>
                    </div>
                    <div style="padding: 0 24px 24px;">
                        <form id="demandeProlongationForm">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
                                <div>
                                    <label style="display: block; font-weight: 500; margin-bottom: 8px; color: var(--gray-700);">
                                        <i class='bx bx-file' style="margin-right: 8px;"></i>
                                        ID Permis <span style="color: var(--danger);">*</span>
                                    </label>
                                    <select id="permisSelectProlongation" required style="width: 100%; padding: 12px; border: 1px solid var(--gray-300); border-radius: var(--radius-md); font-size: 14px;">
                                        <option value="">Sélectionnez un permis</option>
                                        <option value="PT-2024-001">PT-2024-001 - Travaux de maintenance électrique</option>
                                        <option value="PT-2024-003">PT-2024-003 - Travaux d'urgence - Réparation fuite</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; font-weight: 500; margin-bottom: 8px; color: var(--gray-700);">
                                        <i class='bx bx-calendar' style="margin-right: 8px;"></i>
                                        Nouvelle date de fin <span style="color: var(--danger);">*</span>
                                    </label>
                                    <input type="datetime-local" id="nouvelleDateFin" required style="width: 100%; padding: 12px; border: 1px solid var(--gray-300); border-radius: var(--radius-md); font-size: 14px;">
                                </div>
                            </div>

                            <div style="margin-bottom: 24px;">
                                <label style="display: block; font-weight: 500; margin-bottom: 8px; color: var(--gray-700);">
                                    <i class='bx bx-message-detail' style="margin-right: 8px;"></i>
                                    Justification de la prolongation <span style="color: var(--danger);">*</span>
                                </label>
                                <textarea id="justificationProlongation" required rows="4" placeholder="Expliquez les raisons de la demande de prolongation..." style="width: 100%; padding: 12px; border: 1px solid var(--gray-300); border-radius: var(--radius-md); font-size: 14px; resize: vertical;"></textarea>
                            </div>

                            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                                <button type="button" class="btn btn-outline" onclick="resetProlongationForm()">
                                    <i class='bx bx-refresh btn-icon'></i>
                                    Réinitialiser
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class='bx bx-send btn-icon'></i>
                                    Envoyer la demande
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </section>

            <!-- Signaler incident Section -->
            <section id="incidents" class="hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Signaler un incident</h1>
                        <p class="content-subtitle">Signalez tout incident ou problème de sécurité</p>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-header">
                        <i class='bx bx-error-circle section-icon'></i>
                        <h2 class="section-title">Déclaration d'incident</h2>
                    </div>
                    <div style="padding: 0 24px 24px;">
                        <form id="incidentForm">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-bottom: 24px;">
                                <div>
                                    <label style="display: block; font-weight: 500; margin-bottom: 8px; color: var(--gray-700);">
                                        <i class='bx bx-file' style="margin-right: 8px;"></i>
                                        ID Permis concerné
                                    </label>
                                    <select id="permisSelectIncident" style="width: 100%; padding: 12px; border: 1px solid var(--gray-300); border-radius: var(--radius-md); font-size: 14px;">
                                        <option value="">Sélectionnez un permis (optionnel)</option>
                                        <option value="PT-2024-001">PT-2024-001 - Travaux de maintenance électrique</option>
                                        <option value="PT-2024-003">PT-2024-003 - Travaux d'urgence - Réparation fuite</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; font-weight: 500; margin-bottom: 8px; color: var(--gray-700);">
                                        <i class='bx bx-error' style="margin-right: 8px;"></i>
                                        Type d'incident <span style="color: var(--danger);">*</span>
                                    </label>
                                    <select id="typeIncident" required style="width: 100%; padding: 12px; border: 1px solid var(--gray-300); border-radius: var(--radius-md); font-size: 14px;">
                                        <option value="">Sélectionnez le type</option>
                                        <option value="accident">Accident de travail</option>
                                        <option value="presqueaccident">Presque accident</option>
                                        <option value="equipement">Défaillance équipement</option>
                                        <option value="environnement">Incident environnemental</option>
                                        <option value="securite">Non-respect sécurité</option>
                                        <option value="autre">Autre</option>
                                    </select>
                                </div>
                            </div>

                            <div style="margin-bottom: 24px;">
                                <label style="display: block; font-weight: 500; margin-bottom: 8px; color: var(--gray-700);">
                                    <i class='bx bx-message-detail' style="margin-right: 8px;"></i>
                                    Description de l'incident <span style="color: var(--danger);">*</span>
                                </label>
                                <textarea id="descriptionIncident" required rows="5" placeholder="Décrivez l'incident en détail : que s'est-il passé, où, quand, qui était impliqué..." style="width: 100%; padding: 12px; border: 1px solid var(--gray-300); border-radius: var(--radius-md); font-size: 14px; resize: vertical;"></textarea>
                            </div>

                            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                                <button type="button" class="btn btn-outline" onclick="resetIncidentForm()">
                                    <i class='bx bx-refresh btn-icon'></i>
                                    Réinitialiser
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class='bx bx-send btn-icon'></i>
                                    Signaler l'incident
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </section>

            <!-- Profile Section -->
            <section id="profile" class="hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Mon Profil</h1>
                        <p class="content-subtitle">Gérez vos informations personnelles</p>
                    </div>
                </div>

                <div class="form-section">
                    <div style="padding: 24px;">
                        <div style="display: flex; align-items: center; gap: 24px; margin-bottom: 32px;">
                            <div style="width: 80px; height: 80px; border-radius: var(--radius-full); background: linear-gradient(135deg, var(--primary), var(--secondary)); display: flex; align-items: center; justify-content: center; font-size: 32px; font-weight: 700; color: white;">
                                RE
                            </div>
                            <div>
                                <h2 style="font-size: 24px; font-weight: 700; color: var(--gray-900); margin-bottom: 4px;">Responsable Exécution</h2>
                                <p style="color: var(--gray-600); margin-bottom: 8px;">@respexecution</p>
                                <span style="display: inline-flex; align-items: center; gap: 8px; padding: 4px 12px; background-color: var(--primary); color: white; border-radius: var(--radius-full); font-size: 12px; font-weight: 600;">
                                    <i class='bx bx-hard-hat'></i>
                                    Responsable d'Exécution
                                </span>
                            </div>
                        </div>

                        <form>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                                <div>
                                    <label style="display: block; font-weight: 500; margin-bottom: 8px; color: var(--gray-700);">Nom complet</label>
                                    <input type="text" value="Ahmed Benali" style="width: 100%; padding: 12px; border: 1px solid var(--gray-300); border-radius: var(--radius-md); font-size: 14px;">
                                </div>
                                <div>
                                    <label style="display: block; font-weight: 500; margin-bottom: 8px; color: var(--gray-700);">Email</label>
                                    <input type="email" value="<EMAIL>" style="width: 100%; padding: 12px; border: 1px solid var(--gray-300); border-radius: var(--radius-md); font-size: 14px;">
                                </div>
                                <div>
                                    <label style="display: block; font-weight: 500; margin-bottom: 8px; color: var(--gray-700);">Téléphone</label>
                                    <input type="tel" value="+213 555 123 456" style="width: 100%; padding: 12px; border: 1px solid var(--gray-300); border-radius: var(--radius-md); font-size: 14px;">
                                </div>
                                <div>
                                    <label style="display: block; font-weight: 500; margin-bottom: 8px; color: var(--gray-700);">Département</label>
                                    <input type="text" value="Maintenance Industrielle" style="width: 100%; padding: 12px; border: 1px solid var(--gray-300); border-radius: var(--radius-md); font-size: 14px;">
                                </div>
                            </div>
                            <div style="margin-top: 24px; display: flex; gap: 12px; justify-content: flex-end;">
                                <button type="button" class="btn btn-outline">Annuler</button>
                                <button type="submit" class="btn btn-primary">Sauvegarder</button>
                            </div>
                        </form>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings" class="hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Paramètres de compte</h1>
                        <p class="content-subtitle">Gérez vos préférences de notification et les paramètres de compte</p>
                    </div>
                </div>

                <div class="form-section">
                    <div style="padding: 24px;">
                        <h3 style="font-size: 18px; font-weight: 600; color: var(--gray-900); margin-bottom: 16px;">Préférences de notification</h3>
                        <div style="display: flex; flex-direction: column; gap: 16px;">
                            <label style="display: flex; align-items: center; gap: 12px; cursor: pointer;">
                                <input type="checkbox" checked style="margin: 0;">
                                <span style="font-size: 14px; color: var(--gray-700);">Notifications par email</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 12px; cursor: pointer;">
                                <input type="checkbox" checked style="margin: 0;">
                                <span style="font-size: 14px; color: var(--gray-700);">Notifications de nouveaux permis</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 12px; cursor: pointer;">
                                <input type="checkbox" style="margin: 0;">
                                <span style="font-size: 14px; color: var(--gray-700);">Rappels d'échéance</span>
                            </label>
                        </div>
                        <div style="margin-top: 24px; display: flex; gap: 12px; justify-content: flex-end;">
                            <button type="button" class="btn btn-primary">Sauvegarder les préférences</button>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <script>
        // Sidebar toggle functionality
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            document.querySelector('.sidebar').classList.toggle('collapsed');
        });

        // Section navigation
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll("section").forEach(sec => sec.classList.add('hidden'));
            // Show selected section
            document.getElementById(sectionId).classList.remove('hidden');
            // Update page title
            const titles = {
                'dashboard': 'TABLEAU DE BORD',
                'mes-permis': 'MES PERMIS DE TRAVAIL',
                'declaration-fin': 'DÉCLARATION FIN DE TRAVAUX',
                'demande-prolongation': 'DEMANDE DE PROLONGATION',
                'incidents': 'SIGNALER UN INCIDENT',
                'profile': 'MON PROFIL',
                'settings': 'PARAMÈTRES DE COMPTE'
            };
            document.getElementById("page-title").textContent = titles[sectionId];

            // Update active link in sidebar
            document.querySelectorAll(".menu-link").forEach(link => {
                link.classList.remove("active");
            });
            event.currentTarget.classList.add("active");
        }

        // Form submissions
        document.getElementById('declarationFinForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const permisId = document.getElementById('permisSelect').value;
            const dateFin = document.getElementById('dateFinTravaux').value;
            const remarques = document.getElementById('remarquesFin').value;

            // Validation
            if (!permisId || !dateFin || !remarques) {
                alert('Veuillez remplir tous les champs obligatoires.');
                return;
            }

            // Check security verifications
            const verifications = ['zoneNettoyee', 'equipementsRetires', 'securiteRespecte', 'aucunIncident'];
            const checkedVerifications = verifications.filter(id => document.getElementById(id).checked);

            if (checkedVerifications.length < 3) {
                if (!confirm('Certaines vérifications de sécurité ne sont pas cochées. Voulez-vous continuer ?')) {
                    return;
                }
            }

            alert(`Déclaration de fin de travaux pour le permis ${permisId} envoyée avec succès !\n\nElle sera transmise au Représentant de l'Autorité de Zone pour confirmation.`);
            resetDeclarationForm();
        });

        document.getElementById('demandeProlongationForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const permisId = document.getElementById('permisSelectProlongation').value;
            const nouvelleDateFin = document.getElementById('nouvelleDateFin').value;
            const justification = document.getElementById('justificationProlongation').value;

            if (!permisId || !nouvelleDateFin || !justification) {
                alert('Veuillez remplir tous les champs obligatoires.');
                return;
            }

            alert(`Demande de prolongation pour le permis ${permisId} envoyée avec succès !\n\nElle sera transmise au Représentant de l'Autorité de Zone pour approbation.`);
            resetProlongationForm();
        });

        document.getElementById('incidentForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const typeIncident = document.getElementById('typeIncident').value;
            const description = document.getElementById('descriptionIncident').value;

            if (!typeIncident || !description) {
                alert('Veuillez remplir tous les champs obligatoires.');
                return;
            }

            alert('Incident signalé avec succès !\n\nIl sera transmis immédiatement au Responsable HSE et aux autorités compétentes.');
            resetIncidentForm();
        });

        // Utility functions
        function resetDeclarationForm() {
            document.getElementById('declarationFinForm').reset();
        }

        function resetProlongationForm() {
            document.getElementById('demandeProlongationForm').reset();
        }

        function resetIncidentForm() {
            document.getElementById('incidentForm').reset();
        }

        function refreshDashboard() {
            alert('Tableau de bord actualisé !');
        }

        function refreshPermis() {
            alert('Liste des permis actualisée !');
        }

        function voirPermis(id) {
            alert(`Affichage des détails du permis ${id}`);
        }

        function declarerFin(id) {
            // Pre-fill the declaration form and switch to that section
            document.getElementById('permisSelect').value = id;
            showSection('declaration-fin');

            // Simulate clicking the menu link
            document.querySelectorAll(".menu-link").forEach(link => {
                link.classList.remove("active");
            });
            document.querySelector('[onclick="showSection(\'declaration-fin\')"]').classList.add("active");
        }

        function demanderProlongation(id) {
            // Pre-fill the prolongation form and switch to that section
            document.getElementById('permisSelectProlongation').value = id;
            showSection('demande-prolongation');

            // Simulate clicking the menu link
            document.querySelectorAll(".menu-link").forEach(link => {
                link.classList.remove("active");
            });
            document.querySelector('[onclick="showSection(\'demande-prolongation\')"]').classList.add("active");
        }

        function voirDeclaration(id) {
            alert(`Affichage des détails de la déclaration pour le permis ${id}`);
        }

        function searchMesPermis() {
            const searchTerm = document.getElementById('searchMesPermis').value.toLowerCase();
            const rows = document.querySelectorAll('#mesPermisTableBody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function logout() {
            if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                window.location.href = 'login.html';
            }
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Set current date/time for forms
            const now = new Date();
            const currentDateTime = now.toISOString().slice(0, 16);
            document.getElementById('dateFinTravaux').value = currentDateTime;

            // Set minimum date for prolongation to tomorrow
            const tomorrow = new Date(now);
            tomorrow.setDate(tomorrow.getDate() + 1);
            document.getElementById('nouvelleDateFin').min = tomorrow.toISOString().slice(0, 16);
        });
    </script>
</body>
</html>