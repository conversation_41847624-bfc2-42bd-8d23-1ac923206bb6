<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Système de Gestion des Permis de Travail - Responsable d'Exécution</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"/>
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet"/>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #3b82f6;
            --primary-dark: #2563eb;
            --primary-light: #93c5fd;
            --secondary: #4f46e5;
            --accent: #3b82f6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --sidebar-width: 280px;
            --topbar-height: 70px;
            --sidebar-collapsed-width: 80px;
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;
            --transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--gray-50);
            color: var(--gray-900);
            display: flex;
            min-height: 100vh;
            line-height: 1.5;
            font-size: 15px;
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--gray-900);
            color: var(--gray-100);
            position: fixed;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
            transition: var(--transition-slow);
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            border-right: 1px solid rgba(255, 255, 255, 0.05);
            overflow: hidden;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar.collapsed .logo-text,
        .sidebar.collapsed .menu-title,
        .sidebar.collapsed .menu-text,
        .sidebar.collapsed .menu-badge,
        .sidebar.collapsed .user-info,
        .sidebar.collapsed .user-actions {
            opacity: 0;
            width: 0;
            height: 0;
            margin: 0;
            padding: 0;
            overflow: hidden;
            position: absolute;
        }

        .sidebar.collapsed .menu-link {
            justify-content: center;
            padding: 12px 0;
        }

        .sidebar.collapsed .menu-icon {
            margin-right: 0;
            font-size: 24px;
        }

        .sidebar-header {
            padding: 0 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: var(--topbar-height);
            min-height: var(--topbar-height);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 18px;
            font-weight: 700;
            color: white;
            white-space: nowrap;
        }

        .logo-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            flex-shrink: 0;
        }

        .logo-text {
            transition: var(--transition-slow);
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--gray-400);
            font-size: 20px;
            cursor: pointer;
            transition: var(--transition);
            padding: 8px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar-toggle:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-menu {
            flex: 1;
            padding: 16px 0;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--gray-700) var(--gray-800);
        }

        .sidebar-menu::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar-menu::-webkit-scrollbar-track {
            background: var(--gray-800);
        }

        .sidebar-menu::-webkit-scrollbar-thumb {
            background-color: var(--gray-700);
            border-radius: var(--radius-full);
        }

        .menu-group {
            margin-bottom: 24px;
        }

        .menu-title {
            padding: 0 24px 12px;
            font-size: 12px;
            font-weight: 600;
            color: var(--gray-400);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: var(--transition-slow);
        }

        .menu-items {
            list-style: none;
        }

        .menu-item {
            margin: 2px 0;
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: var(--gray-300);
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: var(--transition);
            font-size: 14px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .menu-link:hover {
            background-color: var(--gray-800);
            color: white;
        }

        .menu-link.active {
            background-color: rgba(37, 99, 235, 0.1);
            border-left-color: var(--primary);
            color: white;
        }

        .menu-link.active .menu-icon {
            color: var(--primary-light);
        }

        .menu-icon {
            margin-right: 12px;
            font-size: 20px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-400);
            transition: var(--transition);
            flex-shrink: 0;
        }

        .menu-text {
            transition: var(--transition-slow);
            white-space: nowrap;
        }

        .sidebar-footer {
            padding: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: auto;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px;
            border-radius: var(--radius-md);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .user-profile:hover {
            background-color: var(--gray-800);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            flex-shrink: 0;
            position: relative;
        }

        .user-info {
            flex: 1;
            min-width: 0;
            transition: var(--transition-slow);
        }

        .user-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-role {
            font-size: 12px;
            color: var(--gray-400);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-actions {
            display: flex;
            gap: 8px;
            transition: var(--transition-slow);
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--gray-400);
            cursor: pointer;
            transition: var(--transition);
            border: none;
            position: relative;
        }

        .action-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 18px;
            height: 18px;
            background-color: var(--danger);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 700;
            color: white;
            border: 2px solid var(--gray-900);
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: var(--transition-slow);
            display: flex;
            flex-direction: column;
        }

        .sidebar.collapsed ~ .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        /* Top Navigation */
        .topbar {
            height: var(--topbar-height);
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            box-shadow: var(--shadow-sm);
            border-bottom: 1px solid var(--gray-200);
            position: sticky;
            top: 0;
            z-index: 900;
        }

        .topbar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .menu-toggle {
            background: none;
            border: none;
            color: var(--gray-500);
            font-size: 24px;
            cursor: pointer;
            transition: var(--transition);
            display: none;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
        }

        .menu-toggle:hover {
            background-color: var(--gray-100);
            color: var(--gray-700);
        }

        .breadcrumbs {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .breadcrumb-item {
            color: var(--gray-500);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .breadcrumb-item:not(:last-child)::after {
            content: '/';
            color: var(--gray-300);
            margin-left: 8px;
        }

        .breadcrumb-item.active {
            color: var(--gray-700);
            font-weight: 500;
        }

        .topbar-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .topbar-action {
            position: relative;
            cursor: pointer;
        }

        .action-btn-lg {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            color: var(--gray-500);
            cursor: pointer;
            transition: var(--transition);
            border: none;
            position: relative;
        }

        .action-btn-lg:hover {
            background-color: var(--gray-100);
            color: var(--gray-700);
        }

        .action-btn-lg .notification-badge {
            border-color: white;
        }

        .user-dropdown {
            position: absolute;
            top: calc(100% + 10px);
            right: 0;
            width: 280px;
            background-color: white;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-xl);
            padding: 8px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: var(--transition);
        }

        .user-dropdown.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-header {
            padding: 12px 16px;
            border-bottom: 1px solid var(--gray-100);
            margin-bottom: 8px;
        }

        .dropdown-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--gray-900);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            border-radius: var(--radius-sm);
            color: var(--gray-700);
            text-decoration: none;
            font-size: 14px;
            transition: var(--transition);
            cursor: pointer;
        }

        .dropdown-item:hover {
            background-color: var(--gray-50);
            color: var(--primary);
        }

        .dropdown-item i {
            margin-right: 12px;
            font-size: 18px;
            width: 20px;
            color: var(--gray-500);
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--gray-100);
            margin: 8px 0;
        }


        /* Settings Styles */
        .checkbox-label {
            display: flex;
            align-items: center;
            font-weight: 600;
            color: var(--gray-800);
            cursor: pointer;
            margin-bottom: 4px;
        }

        .checkbox-label input[type="checkbox"] {
            margin-right: 10px;
            width: 18px;
            height: 18px;
            border: 2px solid var(--primary);
            border-radius: 4px;
            cursor: pointer;
        }

        .form-text {
            font-size: 13px;
            color: var(--gray-500);
            margin-top: 4px;
            margin-left: 28px;
            margin-bottom: 16px;
        }

        .profile-section {
            background: white;
            border-radius: var(--radius-lg);
            margin-bottom: 24px;
            border: 1px solid var(--gray-200);
            overflow: hidden;
        }

        .profile-section-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--gray-200);
            background-color: var(--gray-50);
        }

        .profile-section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
        }

        .profile-section-body {
            padding: 24px;
        }

        .profile-actions {
            display: flex;
            justify-content: flex-end;
            padding-top: 16px;
            margin-top: 24px;
            border-top: 1px solid var(--gray-200);
        }

        /* Content Area */
        .content-wrapper {
            flex: 1;
            padding: 24px;
            background-color: var(--gray-50);
        }

        .content-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
        }

        .content-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
        }

        .content-subtitle {
            font-size: 16px;
            color: var(--gray-600);
            margin-top: 8px;
        }

        .content-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 20px;
            border-radius: var(--radius-md);
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: var(--transition);
            border: 1px solid transparent;
            text-decoration: none;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .btn-outline {
            background-color: transparent;
            border-color: var(--gray-300);
            color: var(--gray-700);
        }

        .btn-outline:hover {
            background-color: var(--gray-50);
            border-color: var(--gray-400);
        }

        .btn-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: var(--radius-sm);
        }

        .btn-danger {
            background-color: var(--danger);
            color: white;
            border: 1px solid var(--danger);
        }

        .btn-danger:hover {
            background-color: #dc2626;
            border-color: #dc2626;
        }

        /* Table Styles */
        .table-container {
            overflow-x: auto;
            border-radius: var(--radius-lg);
            border: 1px solid var(--gray-200);
            margin-top: 24px;
        }

        .compact-table {
            font-size: 12px;
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .compact-table th {
            background: var(--gray-50);
            padding: 8px 12px;
            text-align: left;
            font-weight: 600;
            color: var(--gray-700);
            border-bottom: 2px solid var(--gray-200);
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .compact-table td {
            padding: 8px 12px;
            border-bottom: 1px solid var(--gray-100);
            color: var(--gray-600);
            vertical-align: middle;
        }

        .compact-table tr:hover {
            background-color: var(--gray-50);
        }

        .compact-table .table-actions {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
        }

        .compact-table .btn-table {
            padding: 4px 8px;
            font-size: 10px;
            border-radius: var(--radius-sm);
            min-width: auto;
            white-space: nowrap;
        }

        .compact-table .btn-table i {
            font-size: 12px;
            margin-right: 4px;
        }

        /* Enhanced Button Styles */
        .btn-table {
            padding: 6px 10px;
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .btn-table.btn-view {
            background-color: var(--primary);
            color: white;
        }

        .btn-table.btn-view:hover {
            background-color: var(--primary-dark);
        }

        .btn-table.btn-edit {
            background-color: var(--warning);
            color: var(--gray-900);
        }

        .btn-table.btn-edit:hover {
            background-color: var(--warning);
            opacity: 0.8;
        }

        .btn-table.btn-delete {
            background-color: var(--danger);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .btn-table.btn-delete:hover {
            background-color: var(--danger);
            opacity: 0.9;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
        }

        .btn-table.btn-delete:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
        }

        .btn-table.btn-warning {
            background-color: var(--warning);
            color: var(--gray-900);
        }

        .btn-table.btn-warning:hover {
            background-color: var(--warning);
            opacity: 0.8;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
        }

        .btn-table.btn-warning:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
        }

        /* Status Badge Styles - matching priority badge styles */
        .status-badge {
            padding: 4px 8px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.active {
            background-color: #f0fdf4;
            color: var(--success);
        }

        .status-badge.expiring {
            background-color: #fef3c7;
            color: var(--warning);
        }

        .status-badge.suspended {
            background-color: #fef2f2;
            color: var(--danger);
        }

        .status-badge.cancelled {
            background-color: #f3f4f6;
            color: var(--gray-600);
        }

        .table-actions {
            display: flex;
            gap: 6px;
            justify-content: center;
        }

        /* Search Bar Styles */
        .search-bar {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .search-bar input[type="text"] {
            padding: 10px 14px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 13px;
            width: 250px;
            transition: var(--transition);
        }

        .search-bar input[type="text"]:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-bar button {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 10px 18px;
            border-radius: var(--radius-md);
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
        }

        .search-bar button:hover {
            background-color: var(--primary-dark);
        }

        .permit-number {
            font-weight: 600;
            color: var(--gray-600);
        }

        .priority-badge {
            padding: 4px 8px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .priority-badge.high {
            background-color: #fef2f2;
            color: var(--danger);
        }

        .priority-badge.medium {
            background-color: #fef3c7;
            color: var(--warning);
        }

        .priority-badge.low {
            background-color: #f0fdf4;
            color: var(--success);
        }

        .actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        /* Form Styles */
        .form-section {
            background-color: white;
            padding: 32px;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            margin-bottom: 24px;
            border: 1px solid var(--gray-200);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-row-4 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-card {
            background-color: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-sm);
        }

        .form-card:last-child {
            margin-bottom: 0;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid var(--gray-200);
        }

        .text-muted {
            color: var(--gray-500);
            font-size: 12px;
        }

        label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--gray-700);
        }

        .label-icon {
            margin-right: 6px;
            font-size: 16px;
            color: var(--primary);
        }

        input[type="text"],
        input[type="email"],
        input[type="password"],
        input[type="date"],
        input[type="time"],
        textarea,
        select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 14px;
            background-color: white;
            transition: var(--transition);
            font-family: inherit;
        }

        input:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 120px;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 12px;
            margin-top: 12px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            transition: var(--transition);
            cursor: pointer;
        }

        .checkbox-item:hover {
            background-color: var(--gray-50);
            border-color: var(--primary);
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary);
            margin: 0;
        }

        .checkbox-item label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            font-size: 14px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        th,
        td {
            border: 1px solid var(--gray-200);
            padding: 16px;
            text-align: left;
        }

        th {
            background-color: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
            font-size: 14px;
        }

        td input {
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-sm);
            padding: 10px 12px;
            font-size: 14px;
            width: 100%;
        }

        /* Profile Styles */
        .profile-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            padding: 32px;
            border-radius: var(--radius-xl);
            margin-bottom: 32px;
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .profile-info {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: var(--radius-full);
            background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 28px;
            flex-shrink: 0;
            box-shadow: var(--shadow-md);
            border: 3px solid rgba(255,255,255,0.2);
        }

        .profile-details {
            flex: 1;
        }

        .profile-name {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .profile-username {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 12px;
        }

        .profile-role {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border-radius: var(--radius-full);
            font-size: 14px;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .profile-form {
            background-color: white;
            padding: 32px;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
        }

        .form-section-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* Readonly fields */
        input[readonly] {
            background-color: var(--gray-50);
            color: var(--gray-600);
            cursor: not-allowed;
        }

        /* Success/Error Messages */
        .message {
            padding: 16px 20px;
            border-radius: var(--radius-md);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
        }

        .message-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .message-error {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Section Headers */
        .section-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--gray-200);
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--gray-900);
        }

        .section-icon {
            font-size: 24px;
            color: var(--primary);
        }

        /* Required field indicator */
        .required {
            color: var(--danger);
            margin-left: 4px;
        }

        /* Status Badges */
        .status-badge {
            padding: 4px 12px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .status-badge.completed {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-badge.cancelled {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Statistics Cards */
        .stat-card {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 20px;
            border-radius: var(--radius-lg);
            text-align: center;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            line-height: 1;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
        }

        /* Archive specific styles */
        .search-bar select {
            padding: 8px 12px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            background-color: white;
            color: var(--gray-700);
            font-size: 14px;
            transition: var(--transition);
        }

        .search-bar select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            :root {
                --sidebar-width: 240px;
            }
        }

        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 1100;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .menu-toggle {
                display: flex;
            }

            .sidebar-toggle {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .topbar {
                padding: 0 16px;
            }

            .content-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .content-actions {
                width: 100%;
                justify-content: flex-end;
            }

            .form-row,
            .form-row-3,
            .form-row-4 {
                grid-template-columns: 1fr;
            }

            .profile-info {
                flex-direction: column;
                text-align: center;
            }

            .checkbox-group {
                grid-template-columns: 1fr;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 576px) {
            .topbar-actions {
                gap: 8px;
            }

            .action-btn-lg {
                width: 36px;
                height: 36px;
            }

            .content-wrapper {
                padding: 16px;
            }

            .form-section,
            .profile-form {
                padding: 20px;
            }

            .profile-header {
                padding: 24px;
            }
        }

        /* Animation */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-section,
        .profile-header,
        .profile-form {
            animation: fadeIn 0.5s ease-out;
        }

        /* Hidden sections */
        .hidden {
            display: none;
        }

        /* Loading states */
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn:disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        /* Badge styles for status */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: var(--radius-full);
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-success {
            background-color: #dcfce7;
            color: #166534;
        }

        .badge-warning {
            background-color: #fef3c7;
            color: #92400e;
        }

        .badge-danger {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .badge-info {
            background-color: #e0f2fe;
            color: #0c4a6e;
        }

        .badge-secondary {
            background-color: var(--gray-100);
            color: var(--gray-600);
        }

        /* Modal Styles for Permit Details */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(4px);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-overlay.active {
            display: flex;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .permit-modal {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            animation: slideIn 0.3s ease-out;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 24px;
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
            position: relative;
            overflow: hidden;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .modal-title {
            font-size: 24px;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
            z-index: 1;
        }

        .modal-title i {
            font-size: 28px;
            color: var(--primary-light);
        }

        .modal-close {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            transition: var(--transition);
            z-index: 2;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .modal-body {
            padding: 32px;
        }

        .permit-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .info-card {
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            padding: 20px;
            border-left: 4px solid var(--primary);
            transition: var(--transition);
        }

        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .info-card-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .info-card-icon {
            width: 40px;
            height: 40px;
            background: var(--primary);
            color: white;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .info-card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--gray-200);
        }

        .info-item:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }

        .info-label {
            font-weight: 500;
            color: var(--gray-600);
            font-size: 14px;
            flex: 1;
        }

        .info-value {
            font-weight: 600;
            color: var(--gray-900);
            font-size: 14px;
            text-align: right;
            flex: 1;
        }

        .status-display {
            padding: 4px 8px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-display.active {
            background-color: #dcfce7;
            color: #166534;
        }
    </style>
</head>
<body>
    <!-- Sidebar Navigation -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <div class="logo-icon">
                    <img src="sonatrach-logo.png" alt="Logo Sonatrach" style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;"/>
                </div>
                <span class="logo-text">PT Pro</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class='bx bx-chevron-left'></i>
            </button>
        </div>
        <nav class="sidebar-menu">
            <div class="menu-group">
                <h3 class="menu-title">Navigation</h3>
                <ul class="menu-items">
                    <li class="menu-item">
                        <div class="menu-link active" onclick="showSection('permis-emis')">
                            <i class='bx bxs-file-plus menu-icon'></i>
                            <span class="menu-text">Permis émis</span>
                        </div>
                    </li>

                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('declaration-fin')">
                            <i class='bx bx-check-circle menu-icon'></i>
                            <span class="menu-text">Déclaration fin de travail</span>
                        </div>
                    </li>

                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('demande-prolongation')">
                            <i class='bx bx-time-five menu-icon'></i>
                            <span class="menu-text">Demande prolongation</span>
                        </div>
                    </li>

                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('autorisation-continuation')">
                            <i class='bx bx-play-circle menu-icon'></i>
                            <span class="menu-text">Autorisation continuation/reprise travail</span>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="menu-group">
                <h3 class="menu-title">Compte</h3>
                <ul class="menu-items">
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('profile')">
                            <i class='bx bxs-user menu-icon'></i>
                            <span class="menu-text">Mon Profil</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('settings')">
                            <i class='bx bx-cog menu-icon'></i>
                            <span class="menu-text">Paramètres</span>
                        </div>
                    </li>
                </ul>
            </div>
        </nav>
        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">
                    RE
                    <span class="notification-badge">2</span>
                </div>
                <div class="user-info">
                    <div class="user-name">Resp. Exécution</div>
                    <div class="user-role">Responsable d'Exécution</div>
                </div>
                <div class="user-actions">
                    <button class="action-btn" onclick="logout()">
                        <i class='bx bx-log-out'></i>
                    </button>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Top Navigation -->
        <div class="topbar">
            <div class="topbar-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class='bx bx-menu'></i>
                </button>
                <div class="breadcrumbs">
                    <span class="breadcrumb-item">ACCUEIL</span>
                    <span class="breadcrumb-item active" id="page-title">MES PERMIS</span>
                </div>
            </div>
            <div class="topbar-actions">
                <div class="topbar-action">
                    <button class="action-btn-lg">
                        <i class='bx bx-bell'></i>
                        <span class="notification-badge">2</span>
                    </button>
                </div>
                <div class="topbar-action" id="userDropdownTrigger">
                    <button class="action-btn-lg">
                        <div class="user-avatar" style="width: 32px; height: 32px; font-size: 12px;">
                            RE
                        </div>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <div class="dropdown-header">
                            <div class="dropdown-title">Paramètres du compte</div>
                        </div>
                        <div class="dropdown-item" onclick="showSection('profile')">
                            <i class='bx bx-user'></i>
                            <span>Mon profil</span>
                        </div>
                        <div class="dropdown-divider"></div>
                        <div class="dropdown-item" onclick="logout()">
                            <i class='bx bx-log-out'></i>
                            <span>Déconnexion</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-wrapper">
            <!-- Permis Émis Section -->
            <section id="permis-emis" class="form-section">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Permis émis</h1>
                        <p class="content-subtitle">Permis émis par le coordinateur en attente d'acceptation ou de rejet</p>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <input type="text" id="searchPermisEmisInput" placeholder="Rechercher un permis..." onkeyup="searchPermisEmisTable()">
                        <button onclick="searchPermisEmisTable()">Rechercher</button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="compact-table">
                        <thead>
                            <tr>
                                <th>ID permis</th>
                                <th>Type de travail</th>
                                <th>Zone</th>
                                <th>Date début</th>
                                <th>Date fin</th>
                                <th>Émis par</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="permisEmisTableBody">
                            <tr data-permit-id="PT-2024-008">
                                <td><span class="permit-number">PT-2024-008</span></td>
                                <td>Travaux de maintenance électrique</td>
                                <td>Zone A - Production</td>
                                <td>25/01/2024</td>
                                <td>27/01/2024</td>
                                <td>Coordinateur Martin</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-008')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-edit" onclick="acceptPermitEmis('PT-2024-008')">
                                            <i class='bx bx-check-circle'></i>
                                            Accepter
                                        </button>
                                        <button class="btn-table btn-delete" onclick="rejectPermitEmis('PT-2024-008')">
                                            <i class='bx bx-x'></i>
                                            Rejeter
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-009">
                                <td><span class="permit-number">PT-2024-009</span></td>
                                <td>Travaux de réparation urgente</td>
                                <td>Zone C - Laboratoire</td>
                                <td>26/01/2024</td>
                                <td>28/01/2024</td>
                                <td>Coordinateur Dubois</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-009')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-edit" onclick="acceptPermitEmis('PT-2024-009')">
                                            <i class='bx bx-check-circle'></i>
                                            Accepter
                                        </button>
                                        <button class="btn-table btn-delete" onclick="rejectPermitEmis('PT-2024-009')">
                                            <i class='bx bx-x'></i>
                                            Rejeter
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-010">
                                <td><span class="permit-number">PT-2024-010</span></td>
                                <td>Travaux de nettoyage</td>
                                <td>Zone B - Stockage</td>
                                <td>27/01/2024</td>
                                <td>28/01/2024</td>
                                <td>Coordinateur Leroy</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-010')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-edit" onclick="acceptPermitEmis('PT-2024-010')">
                                            <i class='bx bx-check-circle'></i>
                                            Accepter
                                        </button>
                                        <button class="btn-table btn-delete" onclick="rejectPermitEmis('PT-2024-010')">
                                            <i class='bx bx-x'></i>
                                            Rejeter
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Déclaration Fin de Travail Section -->
            <section id="declaration-fin" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Déclaration fin de travail</h1>
                        <p class="content-subtitle">Déclarez la fin des travaux pour les permis en cours</p>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <input type="text" id="searchDeclarationInput" placeholder="Rechercher un permis..." onkeyup="searchDeclarationTable()">
                        <button onclick="searchDeclarationTable()">Rechercher</button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="compact-table">
                        <thead>
                            <tr>
                                <th>ID permis</th>
                                <th>Type de travail</th>
                                <th>Zone</th>
                                <th>Date début</th>
                                <th>Date fin prévue</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="declarationTableBody">
                            <tr data-permit-id="PT-2024-001">
                                <td><span class="permit-number">PT-2024-001</span></td>
                                <td>Travaux de maintenance électrique</td>
                                <td>Zone A - Production</td>
                                <td>20/01/2024</td>
                                <td>22/01/2024</td>
                                <td><span class="priority-badge medium">En cours</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-001')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-edit" onclick="openDeclarationForm('PT-2024-001')">
                                            <i class='bx bx-check'></i>
                                            Déclarer la fin de travail
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-003">
                                <td><span class="permit-number">PT-2024-003</span></td>
                                <td>Travaux d'urgence - Réparation fuite</td>
                                <td>Zone C - Laboratoire</td>
                                <td>18/01/2024</td>
                                <td>21/01/2024</td>
                                <td><span class="priority-badge high">En cours</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-003')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-edit" onclick="openDeclarationForm('PT-2024-003')">
                                            <i class='bx bx-check'></i>
                                            Déclarer la fin de travail
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Demande Prolongation Section -->
            <section id="demande-prolongation" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Demande prolongation</h1>
                        <p class="content-subtitle">Demandez une prolongation pour les permis en cours</p>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <input type="text" id="searchProlongationInput" placeholder="Rechercher un permis..." onkeyup="searchProlongationTable()">
                        <button onclick="searchProlongationTable()">Rechercher</button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="compact-table">
                        <thead>
                            <tr>
                                <th>ID permis</th>
                                <th>Type de travail</th>
                                <th>Zone</th>
                                <th>Date début</th>
                                <th>Date fin prévue</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="prolongationTableBody">
                            <tr data-permit-id="PT-2024-003">
                                <td><span class="permit-number">PT-2024-003</span></td>
                                <td>Travaux d'urgence - Réparation fuite</td>
                                <td>Zone C - Laboratoire</td>
                                <td>18/01/2024</td>
                                <td>21/01/2024</td>
                                <td><span class="priority-badge high">En cours</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-003')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-warning" onclick="openProlongationForm('PT-2024-003')">
                                            <i class='bx bx-time'></i>
                                            Demande prolongation
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-001">
                                <td><span class="permit-number">PT-2024-001</span></td>
                                <td>Travaux de maintenance électrique</td>
                                <td>Zone A - Production</td>
                                <td>20/01/2024</td>
                                <td>22/01/2024</td>
                                <td><span class="priority-badge medium">En cours</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-view" onclick="viewPermitDetails('PT-2024-001')">
                                            <i class='bx bx-show'></i>
                                            Voir
                                        </button>
                                        <button class="btn-table btn-warning" onclick="openProlongationForm('PT-2024-001')">
                                            <i class='bx bx-time'></i>
                                            Demande prolongation
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Autorisation Continuation/Reprise Section -->
            <section id="autorisation-continuation" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Autorisation continuation/reprise travail</h1>
                        <p class="content-subtitle">Autorisations quotidiennes du Représentant de l'Autorité de Zone</p>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <input type="text" id="searchAutorisationInput" placeholder="Rechercher un permis..." onkeyup="searchAutorisationTable()">
                        <button onclick="searchAutorisationTable()">Rechercher</button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="compact-table">
                        <thead>
                            <tr>
                                <th>ID permis</th>
                                <th>Type de travail</th>
                                <th>Zone</th>
                                <th>Date autorisation</th>
                                <th>Statut autorisation</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="autorisationTableBody">
                            <tr data-permit-id="PT-2024-001">
                                <td><span class="permit-number">PT-2024-001</span></td>
                                <td>Travaux de maintenance électrique</td>
                                <td>Zone A - Production</td>
                                <td>25/01/2024</td>
                                <td><span class="priority-badge medium">Autorisé</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-edit" onclick="acceptAutorisation('PT-2024-001')">
                                            <i class='bx bx-check-circle'></i>
                                            Accepter
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr data-permit-id="PT-2024-003">
                                <td><span class="permit-number">PT-2024-003</span></td>
                                <td>Travaux d'urgence - Réparation fuite</td>
                                <td>Zone C - Laboratoire</td>
                                <td>25/01/2024</td>
                                <td><span class="priority-badge medium">Autorisé</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn-table btn-edit" onclick="acceptAutorisation('PT-2024-003')">
                                            <i class='bx bx-check-circle'></i>
                                            Accepter
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>



            <!-- Settings Section -->
            <section id="settings" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Paramètres de compte</h1>
                        <p class="content-subtitle">Gérez vos préférences de notification et les paramètres de compte</p>
                    </div>
                </div>

                <div class="profile-section">
                    <div class="profile-section-header">
                        <h3 class="profile-section-title">Préférences de notification</h3>
                    </div>
                    <div class="profile-section-body">
                        <form id="notificationForm">
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked id="emailNotif">
                                    <span>Notifications par email</span>
                                </label>
                                <p class="form-text">Recevez des emails pour les mises à jour importantes</p>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked id="permitNotif">
                                    <span>Notifications de permis</span>
                                </label>
                                <p class="form-text">Soyez notifié quand un nouveau permis vous est assigné pour exécution</p>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked id="statusNotif">
                                    <span>Mises à jour de statut</span>
                                </label>
                                <p class="form-text">Recevez des notifications quand le statut d'un permis change</p>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="reminderNotif">
                                    <span>Rappels</span>
                                </label>
                                <p class="form-text">Recevez des rappels pour les permis en attente d'action</p>
                            </div>

                            <div class="profile-actions">
                                <button type="submit" class="btn btn-primary" id="saveNotifBtn">
                                    <i class="bx bx-save btn-icon"></i>Enregistrer les préférences
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </section>

            <!-- Profile Section -->
            <section id="profile" class="hidden">
                <!-- Profile Header -->
                <div class="profile-header">
                    <div class="profile-info">
                        <div class="profile-avatar">RE</div>
                        <div class="profile-details">
                            <h1 class="profile-name">Responsable Exécution</h1>
                            <div class="profile-username">@respexecution</div>
                            <span class="profile-role">
                                <i class='bx bx-hard-hat'></i>
                                Responsable d'Exécution
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Profile Form -->
                <div class="profile-form">
                    <h2 class="form-section-title">
                        <i class='bx bx-user'></i>
                        Informations du Profil
                    </h2>

                    <form id="profileForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-user label-icon'></i>
                                    Nom complet
                                </label>
                                <input type="text" value="Ahmed Benali" readonly/>
                            </div>
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-user label-icon'></i>
                                    Nom d'utilisateur
                                </label>
                                <input type="text" value="respexecution" readonly/>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-briefcase label-icon'></i>
                                    Fonction
                                </label>
                                <input type="text" value="Responsable d'Exécution" readonly/>
                            </div>
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-envelope label-icon'></i>
                                    Email <span class="required">*</span>
                                </label>
                                <input type="email" id="userEmail" value="<EMAIL>" required placeholder="Entrez votre adresse email"/>
                            </div>
                        </div>

                        <!-- Success/Error Message -->
                        <div id="profileMessage" class="hidden"></div>

                        <!-- Submit Button -->
                        <div style="display: flex; justify-content: flex-end; margin-top: 24px;">
                            <button type="submit" class="btn btn-primary" id="profileSubmitBtn">
                                <i class='bx bx-save btn-icon'></i>
                                Mettre à jour l'email
                            </button>
                        </div>
                    </form>
                </div>
            </section>
        </div>
    </main>

    <!-- Modal for Permit Details -->
    <div class="modal-overlay" id="permitModal">
        <div class="permit-modal">
            <div class="modal-header">
                <h2 class="modal-title">
                    <i class='bx bx-file-blank'></i>
                    <span id="modalPermitTitle">Détails du Permis</span>
                </h2>
                <button class="modal-close" onclick="closePermitModal()">
                    <i class='bx bx-x'></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="permit-info-grid">
                    <div class="info-card">
                        <div class="info-card-header">
                            <div class="info-card-icon">
                                <i class='bx bx-info-circle'></i>
                            </div>
                            <h3 class="info-card-title">Informations Générales</h3>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Numéro de permis</span>
                            <span class="info-value" id="modalPermitNumber">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Description</span>
                            <span class="info-value" id="modalPermitDescription">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Zone de travail</span>
                            <span class="info-value" id="modalPermitZone">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Demandeur</span>
                            <span class="info-value" id="modalPermitRequester">-</span>
                        </div>
                    </div>

                    <div class="info-card">
                        <div class="info-card-header">
                            <div class="info-card-icon">
                                <i class='bx bx-calendar'></i>
                            </div>
                            <h3 class="info-card-title">Planification</h3>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Date de début</span>
                            <span class="info-value" id="modalStartDate">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Date de fin</span>
                            <span class="info-value" id="modalEndDate">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Statut actuel</span>
                            <span class="info-value">
                                <span class="status-display active" id="modalStatus">En cours</span>
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Responsable HSE</span>
                            <span class="info-value">Marie Sécurité</span>
                        </div>
                    </div>

                    <div class="info-card" id="archiveInfoCard" style="display: none;">
                        <div class="info-card-header">
                            <div class="info-card-icon">
                                <i class='bx bx-archive'></i>
                            </div>
                            <h3 class="info-card-title">Informations d'archivage</h3>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Archivé par</span>
                            <span class="info-value" id="modalArchivedBy">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Date d'archivage</span>
                            <span class="info-value" id="modalArchiveDate">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Motif d'archivage</span>
                            <span class="info-value" id="modalArchiveReason">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Durée totale</span>
                            <span class="info-value" id="modalDuration">-</span>
                        </div>
                    </div>

                    <div class="info-card" id="safetyInfoCard" style="display: none;">
                        <div class="info-card-header">
                            <div class="info-card-icon">
                                <i class='bx bx-shield-check'></i>
                            </div>
                            <h3 class="info-card-title">Sécurité et Conformité</h3>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Type de travaux</span>
                            <span class="info-value" id="modalWorkType">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Risques identifiés</span>
                            <span class="info-value" id="modalRisks">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Mesures de sécurité</span>
                            <span class="info-value" id="modalSafetyMeasures">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">EPI requis</span>
                            <span class="info-value" id="modalPPE">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sidebar toggle functionality
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            document.querySelector('.sidebar').classList.toggle('collapsed');
        });

        // Mobile menu toggle
        document.getElementById('menuToggle').addEventListener('click', function() {
            document.querySelector('.sidebar').classList.toggle('active');
        });

        // User dropdown toggle
        document.getElementById('userDropdownTrigger').addEventListener('click', function() {
            document.getElementById('userDropdown').classList.toggle('active');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('userDropdown');
            const trigger = document.getElementById('userDropdownTrigger');

            if (!trigger.contains(event.target)) {
                dropdown.classList.remove('active');
            }
        });

        // Section navigation
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll("section").forEach(sec => sec.classList.add('hidden'));
            // Show selected section
            document.getElementById(sectionId).classList.remove('hidden');

            // Update page title
            const titles = {
                'permis-emis': 'PERMIS ÉMIS',
                'declaration-fin': 'DÉCLARATION FIN DE TRAVAIL',
                'demande-prolongation': 'DEMANDE DE PROLONGATION',
                'autorisation-continuation': 'AUTORISATION CONTINUATION/REPRISE TRAVAIL',
                'profile': 'MON PROFIL',
                'settings': 'PARAMÈTRES DE COMPTE'
            };
            document.getElementById("page-title").textContent = titles[sectionId];

            // Update active link in sidebar
            document.querySelectorAll(".menu-link").forEach(link => {
                link.classList.remove("active");
            });
            event.currentTarget.classList.add("active");
        }

        // Search functionality for new sections
        function searchPermisEmisTable() {
            const searchTerm = document.getElementById('searchPermisEmisInput').value.toLowerCase();
            const rows = document.querySelectorAll('#permisEmisTableBody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function searchDeclarationTable() {
            const searchTerm = document.getElementById('searchDeclarationInput').value.toLowerCase();
            const rows = document.querySelectorAll('#declarationTableBody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function searchProlongationTable() {
            const searchTerm = document.getElementById('searchProlongationInput').value.toLowerCase();
            const rows = document.querySelectorAll('#prolongationTableBody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function searchAutorisationTable() {
            const searchTerm = document.getElementById('searchAutorisationInput').value.toLowerCase();
            const rows = document.querySelectorAll('#autorisationTableBody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function searchArchiveTable() {
            const input = document.getElementById('searchArchiveInput');
            const filter = input.value.toUpperCase();
            const table = document.querySelector('#archive .table-container table');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length; j++) {
                    const cell = cells[j];
                    if (cell) {
                        const textValue = cell.textContent || cell.innerText;
                        if (textValue.toUpperCase().indexOf(filter) > -1) {
                            found = true;
                            break;
                        }
                    }
                }
                row.style.display = found ? '' : 'none';
            }
        }

        function filterArchiveTable() {
            const typeFilter = document.getElementById('archiveTypeFilter').value.toLowerCase();
            const statusFilter = document.getElementById('archiveStatusFilter').value.toLowerCase();
            const zoneFilter = document.getElementById('archiveZoneFilter').value.toLowerCase();
            const dateStart = document.getElementById('archiveDateStart') ? document.getElementById('archiveDateStart').value : '';
            const dateEnd = document.getElementById('archiveDateEnd') ? document.getElementById('archiveDateEnd').value : '';
            const archivedByFilter = document.getElementById('archivedByFilter') ? document.getElementById('archivedByFilter').value.toLowerCase() : '';

            const table = document.querySelector('#archive .table-container table');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let showRow = true;

                // Type filter
                if (typeFilter && cells[1]) {
                    const typeText = cells[1].textContent.toLowerCase();
                    if (!typeText.includes(typeFilter)) {
                        showRow = false;
                    }
                }

                // Status filter
                if (statusFilter && cells[5]) {
                    const statusText = cells[5].textContent.toLowerCase();
                    if (!statusText.includes(statusFilter)) {
                        showRow = false;
                    }
                }

                // Zone filter
                if (zoneFilter && cells[2]) {
                    const zoneText = cells[2].textContent.toLowerCase();
                    if (!zoneText.includes(zoneFilter)) {
                        showRow = false;
                    }
                }

                row.style.display = showRow ? '' : 'none';
            }
        }

        function resetArchiveFilters() {
            document.getElementById('searchArchiveInput').value = '';
            document.getElementById('archiveTypeFilter').value = '';
            document.getElementById('archiveStatusFilter').value = '';
            document.getElementById('archiveZoneFilter').value = '';

            if (document.getElementById('archiveDateStart')) {
                document.getElementById('archiveDateStart').value = '';
            }
            if (document.getElementById('archiveDateEnd')) {
                document.getElementById('archiveDateEnd').value = '';
            }
            if (document.getElementById('archivedByFilter')) {
                document.getElementById('archivedByFilter').value = '';
            }

            // Show all rows
            const table = document.querySelector('#archive .table-container table');
            const rows = table.getElementsByTagName('tr');
            for (let i = 1; i < rows.length; i++) {
                rows[i].style.display = '';
            }
        }

        function exportArchiveToExcel() {
            // Get current filter values for export info
            const typeFilter = document.getElementById('archiveTypeFilter').value;
            const statusFilter = document.getElementById('archiveStatusFilter').value;
            const zoneFilter = document.getElementById('archiveZoneFilter').value;

            let filterInfo = '';
            if (typeFilter || statusFilter || zoneFilter) {
                filterInfo = '\n\n🔍 Filtres appliqués:';
                if (typeFilter) filterInfo += `\n• Type: ${typeFilter}`;
                if (statusFilter) filterInfo += `\n• Statut: ${statusFilter}`;
                if (zoneFilter) filterInfo += `\n• Zone: ${zoneFilter}`;
            }

            alert(`📊 Export Excel en cours...\n\n✅ Le fichier "Archive_Permis_${new Date().toISOString().split('T')[0]}.xlsx" sera téléchargé dans quelques secondes.\n\n📋 Contenu:\n• ${document.querySelectorAll('#archive tbody tr:not([style*="display: none"])').length} permis archivés\n• Informations complètes de sécurité\n• Statistiques d'archivage\n• Historique des commentaires${filterInfo}`);
        }

        function viewArchivedPermitDetails(permitId) {
            // Find the permit row in archive table
            const permitRow = document.querySelector(`#archive tr[data-permit-id="${permitId}"]`);

            if (permitRow) {
                const cells = permitRow.querySelectorAll('td');
                const permitData = {
                    number: cells[0].textContent.trim(),
                    description: cells[1].textContent.trim(),
                    zone: cells[2].textContent.trim(),
                    requester: 'Responsable Maintenance',
                    startDate: 'Voir historique',
                    endDate: cells[3].textContent.trim(),
                    status: cells[5].textContent.trim(),
                    archivedBy: 'Responsable Exécution',
                    archiveDate: new Date().toLocaleDateString('fr-FR')
                };

                // Enhanced data for archived permits
                const enhancedData = getEnhancedPermitData(permitId, permitData);

                // Fill modal with permit data
                document.getElementById('modalPermitTitle').textContent = `Détails du Permis Archivé ${permitData.number}`;
                document.getElementById('modalPermitNumber').textContent = permitData.number;
                document.getElementById('modalPermitDescription').textContent = permitData.description;
                document.getElementById('modalPermitZone').textContent = permitData.zone;
                document.getElementById('modalPermitRequester').textContent = permitData.requester;
                document.getElementById('modalStartDate').textContent = permitData.startDate;
                document.getElementById('modalEndDate').textContent = permitData.endDate;
                document.getElementById('modalStatus').textContent = permitData.status;

                // Show archive-specific information
                document.getElementById('archiveInfoCard').style.display = 'block';
                document.getElementById('modalArchivedBy').textContent = permitData.archivedBy;
                document.getElementById('modalArchiveDate').textContent = permitData.archiveDate;
                document.getElementById('modalArchiveReason').textContent = enhancedData.archiveReason;
                document.getElementById('modalDuration').textContent = enhancedData.duration;

                // Show safety information
                document.getElementById('safetyInfoCard').style.display = 'block';
                document.getElementById('modalWorkType').textContent = enhancedData.workType;
                document.getElementById('modalRisks').textContent = enhancedData.risks;
                document.getElementById('modalSafetyMeasures').textContent = enhancedData.safetyMeasures;
                document.getElementById('modalPPE').textContent = enhancedData.ppe;

                // Show modal
                document.getElementById('permitModal').classList.add('active');
            } else {
                alert(`❌ Erreur: Impossible de trouver les détails du permis archivé ${permitId}`);
            }
        }

        function getEnhancedPermitData(permitId, basicData) {
            // Enhanced data based on permit ID and type
            const enhancedDataMap = {
                'PT-2024-001': {
                    workType: 'Travaux de maintenance préventive',
                    risks: 'Risques mécaniques, électriques',
                    safetyMeasures: 'Consignation électrique, EPI complets, surveillance continue',
                    ppe: 'Casque, gants isolants, chaussures de sécurité, lunettes',
                    archiveReason: 'Travaux terminés avec succès',
                    duration: '4 jours'
                },
                'PT-2024-003': {
                    workType: 'Réparation d\'urgence',
                    risks: 'Pression résiduelle, produits chimiques',
                    safetyMeasures: 'Dépressurisation, détection gaz, ventilation forcée',
                    ppe: 'Combinaison anti-acide, ARI, détecteur portable',
                    archiveReason: 'Prolongation approuvée',
                    duration: '6 jours'
                },
                'PT-2024-006': {
                    workType: 'Travaux de réparation mécanique',
                    risks: 'Risques mécaniques, hauteur',
                    safetyMeasures: 'Harnais de sécurité, balisage zone, surveillance',
                    ppe: 'Harnais, casque, gants, chaussures antidérapantes',
                    archiveReason: 'Demande rejetée - conditions météo',
                    duration: '2 jours'
                }
            };

            return enhancedDataMap[permitId] || {
                workType: 'Travaux généraux',
                risks: 'Risques standards',
                safetyMeasures: 'Mesures de sécurité standard',
                ppe: 'EPI standard requis',
                archiveReason: 'Archivage standard',
                duration: 'Non spécifié'
            };
        }

        function addArchiveComment(permitId) {
            // Get permit info for context
            const permitRow = document.querySelector(`#archive tr[data-permit-id="${permitId}"]`);
            const permitDescription = permitRow ? permitRow.querySelectorAll('td')[1].textContent.trim() : 'Permis inconnu';

            const comment = prompt(`💬 Ajouter un commentaire post-archivage\n\n📋 Permis: ${permitId}\n🔧 Travaux: ${permitDescription}\n\n📝 Votre commentaire (max 500 caractères):`);

            if (comment && comment.trim()) {
                if (comment.trim().length > 500) {
                    alert('⚠️ Le commentaire ne peut pas dépasser 500 caractères.\n\n📏 Longueur actuelle: ' + comment.trim().length + ' caractères');
                    return;
                }

                alert(`✅ Commentaire ajouté avec succès!\n\n📋 Permis: ${permitId}\n📅 Date: ${new Date().toLocaleString('fr-FR')}\n👤 Auteur: Responsable Exécution\n💬 Commentaire: "${comment.trim()}"\n\n📧 Le commentaire a été enregistré dans l'historique du permis.`);
            } else if (comment !== null) {
                alert('⚠️ Le commentaire ne peut pas être vide.\n\n💡 Conseil: Ajoutez des informations utiles comme:\n• Observations post-travaux\n• Recommandations pour l\'avenir\n• Points d\'amélioration identifiés\n• Retour d\'expérience');
            }
        }

        // Action functions
        function viewPermitDetails(permitId) {
            // Find the permit row in the current active section
            let permitRow = null;
            const currentSection = document.querySelector('section:not(.hidden)');

            if (currentSection) {
                permitRow = currentSection.querySelector(`tr[data-permit-id="${permitId}"]`);
            }

            if (permitRow) {
                const cells = permitRow.querySelectorAll('td');
                let permitData = {};

                // Extract data based on current section
                if (currentSection.id === 'permits') {
                    permitData = {
                        number: permitId,
                        description: cells[1].textContent.trim(),
                        zone: cells[2].textContent.trim(),
                        requester: 'Responsable Maintenance',
                        startDate: cells[3].textContent.trim(),
                        endDate: cells[4].textContent.trim(),
                        status: cells[5].textContent.trim()
                    };
                } else if (currentSection.id === 'history') {
                    permitData = {
                        number: permitId,
                        description: cells[1].textContent.trim(),
                        zone: cells[2].textContent.trim(),
                        requester: 'Responsable Maintenance',
                        startDate: cells[3].textContent.trim(),
                        endDate: cells[4].textContent.trim(),
                        status: cells[5].textContent.trim()
                    };
                } else if (currentSection.id === 'archive') {
                    permitData = {
                        number: permitId,
                        description: cells[1].textContent.trim(),
                        zone: cells[2].textContent.trim(),
                        requester: 'Responsable Maintenance',
                        startDate: 'Voir historique',
                        endDate: 'En attente d\'approbation',
                        status: cells[5].textContent.trim()
                    };
                }

                // Fill modal with permit data
                document.getElementById('modalPermitTitle').textContent = `Détails du Permis ${permitData.number}`;
                document.getElementById('modalPermitNumber').textContent = permitData.number;
                document.getElementById('modalPermitDescription').textContent = permitData.description;
                document.getElementById('modalPermitZone').textContent = permitData.zone;
                document.getElementById('modalPermitRequester').textContent = permitData.requester;
                document.getElementById('modalStartDate').textContent = permitData.startDate;
                document.getElementById('modalEndDate').textContent = permitData.endDate;
                document.getElementById('modalStatus').textContent = permitData.status;

                // Show modal
                document.getElementById('permitModal').classList.add('active');
            } else {
                alert(`❌ Erreur: Impossible de trouver les détails du permis ${permitId}`);
            }
        }

        function declareCompletion(permitId) {
            const confirmation = confirm(`✅ Déclarer la fin des travaux pour le permis ${permitId}?\n\n📋 Cette action va:\n• Marquer les travaux comme terminés\n• Notifier le Représentant de l'Autorité de Zone\n• Déclencher la procédure de clôture\n\n⚠️ Cette action ne peut pas être annulée.`);

            if (confirmation) {
                alert(`✅ Déclaration de fin de travaux envoyée!\n\n📋 Permis: ${permitId}\n📅 Date: ${new Date().toLocaleString('fr-FR')}\n👤 Déclaré par: Responsable Exécution\n\n📧 Le Représentant de l'Autorité de Zone a été notifié et procédera à la validation finale.`);

                // Update the permit status in the table
                const permitRow = document.querySelector(`tr[data-permit-id="${permitId}"]`);
                if (permitRow) {
                    const statusCell = permitRow.querySelector('.priority-badge');
                    if (statusCell) {
                        statusCell.textContent = 'Déclaré terminé';
                        statusCell.className = 'priority-badge low';
                    }
                }
            }
        }

        function acceptPermit(permitId) {
            const confirmation = confirm(`✅ Accepter le permis de travail ${permitId}?\n\n📋 Cette action va:\n• Confirmer votre acceptation du permis\n• Autoriser le début des travaux\n• Notifier les parties concernées\n\n⚠️ Assurez-vous d'avoir vérifié toutes les conditions de sécurité.`);

            if (confirmation) {
                alert(`✅ Permis accepté avec succès!\n\n📋 Permis: ${permitId}\n📅 Date d'acceptation: ${new Date().toLocaleString('fr-FR')}\n👤 Accepté par: Responsable Exécution\n\n🚀 Les travaux peuvent maintenant commencer selon les conditions définies.`);

                // Update permit status in the table
                const row = document.querySelector(`tr[data-permit-id="${permitId}"]`);
                if (row) {
                    const statusCell = row.querySelector('.priority-badge');
                    if (statusCell) {
                        statusCell.textContent = 'Accepté';
                        statusCell.className = 'priority-badge medium';
                    }
                }
            }
        }

        function acceptDailyContinuation(permitId) {
            const confirmation = confirm(`🔄 Accepter la continuation/reprise quotidienne pour le permis ${permitId}?\n\n📋 Cette action va:\n• Autoriser la reprise des travaux pour aujourd'hui\n• Confirmer que les conditions de sécurité sont maintenues\n• Enregistrer l'heure de reprise\n\n⚠️ Vérifiez que toutes les mesures de sécurité sont en place.`);

            if (confirmation) {
                const currentTime = new Date().toLocaleString('fr-FR');
                alert(`✅ Continuation/reprise autorisée!\n\n📋 Permis: ${permitId}\n🕐 Heure de reprise: ${currentTime}\n👤 Autorisé par: Responsable Exécution\n\n🔄 Les travaux peuvent reprendre selon les conditions du permis.`);

                // Update permit status in the table
                const row = document.querySelector(`tr[data-permit-id="${permitId}"]`);
                if (row) {
                    const statusCell = row.querySelector('.priority-badge');
                    if (statusCell) {
                        statusCell.textContent = 'En cours';
                        statusCell.className = 'priority-badge medium';
                    }
                }
            }
        }

        function requestExtension(permitId) {
            const newDate = prompt(`⏰ Demande de prolongation pour le permis ${permitId}\n\n📅 Nouvelle date de fin souhaitée (format: JJ/MM/AAAA):`);

            if (newDate && newDate.trim()) {
                const reason = prompt(`📝 Justification de la prolongation:\n\n💡 Expliquez brièvement pourquoi une prolongation est nécessaire:`);

                if (reason && reason.trim()) {
                    alert(`📤 Demande de prolongation envoyée!\n\n📋 Permis: ${permitId}\n📅 Nouvelle date: ${newDate}\n📝 Justification: ${reason}\n👤 Demandeur: Responsable Exécution\n\n⏳ La demande sera examinée par le Représentant de l'Autorité de Zone.`);
                }
            }
        }

        // New functions for the updated interface
        function acceptPermitEmis(permitId) {
            const confirmation = confirm(`✅ Accepter le permis émis ${permitId}?\n\n📋 Cette action va:\n• Accepter le permis émis par le coordinateur\n• Ajouter le permis à vos travaux en cours\n• Notifier le coordinateur de votre acceptation\n\n⚠️ Cette action ne peut pas être annulée.`);

            if (confirmation) {
                alert(`✅ Permis accepté avec succès!\n\n📋 Permis: ${permitId}\n📅 Date d'acceptation: ${new Date().toLocaleString('fr-FR')}\n👤 Accepté par: Responsable Exécution\n\n🚀 Le permis a été ajouté à vos travaux en cours.`);

                // Remove the permit from the table
                const row = document.querySelector(`tr[data-permit-id="${permitId}"]`);
                if (row) {
                    row.remove();
                }
            }
        }

        function rejectPermitEmis(permitId) {
            const reason = prompt(`❌ Rejeter le permis émis ${permitId}\n\n📝 Veuillez indiquer la raison du rejet:`);

            if (reason && reason.trim()) {
                alert(`❌ Permis rejeté!\n\n📋 Permis: ${permitId}\n📝 Raison: ${reason}\n📅 Date de rejet: ${new Date().toLocaleString('fr-FR')}\n👤 Rejeté par: Responsable Exécution\n\n📧 Le coordinateur a été notifié du rejet.`);

                // Remove the permit from the table
                const row = document.querySelector(`tr[data-permit-id="${permitId}"]`);
                if (row) {
                    row.remove();
                }
            }
        }

        function openDeclarationForm(permitId) {
            const formHtml = `
                <div id="declarationModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; padding: 30px; border-radius: 12px; width: 90%; max-width: 600px; max-height: 90%; overflow-y: auto;">
                        <h2 style="margin-bottom: 20px; color: var(--primary);">📋 Déclaration fin de travail - ${permitId}</h2>

                        <form id="declarationForm">
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">Date et heure de fin des travaux *</label>
                                <input type="datetime-local" id="endDateTime" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">Travaux réalisés *</label>
                                <textarea id="workCompleted" required rows="4" placeholder="Décrivez les travaux réalisés..." style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;"></textarea>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">Observations et remarques</label>
                                <textarea id="observations" rows="3" placeholder="Observations particulières, incidents, etc..." style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;"></textarea>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">Zone remise en état de sécurité</label>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <input type="checkbox" id="safetyRestored" style="transform: scale(1.2);">
                                    <label for="safetyRestored">Je confirme que la zone a été remise en état de sécurité</label>
                                </div>
                            </div>

                            <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px;">
                                <button type="button" onclick="closeDeclarationModal()" style="padding: 12px 24px; border: 1px solid #ddd; background: white; border-radius: 6px; cursor: pointer;">Annuler</button>
                                <button type="submit" style="padding: 12px 24px; background: var(--success); color: white; border: none; border-radius: 6px; cursor: pointer;">Soumettre</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', formHtml);

            // Set current date/time
            const now = new Date();
            now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
            document.getElementById('endDateTime').value = now.toISOString().slice(0, 16);

            // Handle form submission
            document.getElementById('declarationForm').addEventListener('submit', function(e) {
                e.preventDefault();
                submitDeclaration(permitId);
            });
        }

        function closeDeclarationModal() {
            const modal = document.getElementById('declarationModal');
            if (modal) {
                modal.remove();
            }
        }

        function submitDeclaration(permitId) {
            const endDateTime = document.getElementById('endDateTime').value;
            const workCompleted = document.getElementById('workCompleted').value;
            const observations = document.getElementById('observations').value;
            const safetyRestored = document.getElementById('safetyRestored').checked;

            if (!endDateTime || !workCompleted.trim()) {
                alert('⚠️ Veuillez remplir tous les champs obligatoires.');
                return;
            }

            alert(`✅ Déclaration de fin de travail soumise!\n\n📋 Permis: ${permitId}\n🕐 Fin des travaux: ${new Date(endDateTime).toLocaleString('fr-FR')}\n📝 Travaux: ${workCompleted}\n👤 Déclaré par: Responsable Exécution\n\n📧 La déclaration a été envoyée au coordinateur pour validation.`);

            closeDeclarationModal();

            // Remove the permit from the table
            const row = document.querySelector(`tr[data-permit-id="${permitId}"]`);
            if (row) {
                row.remove();
            }
        }

        function openProlongationForm(permitId) {
            const formHtml = `
                <div id="prolongationModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; padding: 30px; border-radius: 12px; width: 90%; max-width: 600px; max-height: 90%; overflow-y: auto;">
                        <h2 style="margin-bottom: 20px; color: var(--primary);">⏰ Demande de prolongation - ${permitId}</h2>

                        <form id="prolongationForm">
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">Nouvelle date de fin souhaitée *</label>
                                <input type="date" id="newEndDate" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">Durée supplémentaire demandée *</label>
                                <select id="additionalDuration" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                                    <option value="">Sélectionner la durée</option>
                                    <option value="1-jour">1 jour</option>
                                    <option value="2-jours">2 jours</option>
                                    <option value="3-jours">3 jours</option>
                                    <option value="1-semaine">1 semaine</option>
                                    <option value="autre">Autre (préciser dans justification)</option>
                                </select>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">Justification de la prolongation *</label>
                                <textarea id="justification" required rows="4" placeholder="Expliquez pourquoi une prolongation est nécessaire..." style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;"></textarea>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">Travaux restants à effectuer *</label>
                                <textarea id="remainingWork" required rows="3" placeholder="Décrivez les travaux qui restent à effectuer..." style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;"></textarea>
                            </div>

                            <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px;">
                                <button type="button" onclick="closeProlongationModal()" style="padding: 12px 24px; border: 1px solid #ddd; background: white; border-radius: 6px; cursor: pointer;">Annuler</button>
                                <button type="submit" style="padding: 12px 24px; background: var(--warning); color: white; border: none; border-radius: 6px; cursor: pointer;">Soumettre</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', formHtml);

            // Handle form submission
            document.getElementById('prolongationForm').addEventListener('submit', function(e) {
                e.preventDefault();
                submitProlongation(permitId);
            });
        }

        function closeProlongationModal() {
            const modal = document.getElementById('prolongationModal');
            if (modal) {
                modal.remove();
            }
        }

        function submitProlongation(permitId) {
            const newEndDate = document.getElementById('newEndDate').value;
            const additionalDuration = document.getElementById('additionalDuration').value;
            const justification = document.getElementById('justification').value;
            const remainingWork = document.getElementById('remainingWork').value;

            if (!newEndDate || !additionalDuration || !justification.trim() || !remainingWork.trim()) {
                alert('⚠️ Veuillez remplir tous les champs obligatoires.');
                return;
            }

            alert(`📤 Demande de prolongation soumise!\n\n📋 Permis: ${permitId}\n📅 Nouvelle date: ${new Date(newEndDate).toLocaleDateString('fr-FR')}\n⏱️ Durée: ${additionalDuration}\n📝 Justification: ${justification}\n👤 Demandeur: Responsable Exécution\n\n⏳ La demande sera examinée par le coordinateur.`);

            closeProlongationModal();
        }

        function acceptAutorisation(permitId) {
            const confirmation = confirm(`✅ Accepter l'autorisation quotidienne pour le permis ${permitId}?\n\n📋 Cette action va:\n• Confirmer votre acceptation de l'autorisation\n• Permettre la continuation des travaux aujourd'hui\n• Enregistrer l'heure d'acceptation\n\n⚠️ Assurez-vous que toutes les conditions de sécurité sont respectées.`);

            if (confirmation) {
                alert(`✅ Autorisation acceptée!\n\n📋 Permis: ${permitId}\n🕐 Heure d'acceptation: ${new Date().toLocaleString('fr-FR')}\n👤 Accepté par: Responsable Exécution\n\n🔄 Les travaux peuvent continuer selon les conditions du permis.`);

                // Update the button to show "Accepté"
                const row = document.querySelector(`tr[data-permit-id="${permitId}"]`);
                if (row) {
                    const actionCell = row.querySelector('.table-actions');
                    if (actionCell) {
                        actionCell.innerHTML = '<span class="priority-badge low">Accepté</span>';
                    }
                }
            }
        }

        function logout() {
            if (confirm('🚪 Êtes-vous sûr de vouloir vous déconnecter?\n\n⚠️ Toutes les données non sauvegardées seront perdues.')) {
                alert('👋 Déconnexion en cours...\n\n✅ Session fermée avec succès.\n🔒 Redirection vers la page de connexion.');
                // In a real application, this would redirect to login
                // window.location.href = 'login.html';
            }
        }

        // Profile form submission
        const profileForm = document.getElementById('profileForm');
        const profileSubmitBtn = document.getElementById('profileSubmitBtn');
        const profileMessage = document.getElementById('profileMessage');
        const userEmail = document.getElementById('userEmail');
        const originalEmail = userEmail.value;

        profileForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            if (userEmail.value === originalEmail) {
                profileMessage.className = 'message message-error';
                profileMessage.innerHTML = '<i class="bx bx-info-circle"></i>Aucune modification détectée';
                profileMessage.classList.remove('hidden');
                return;
            }

            profileSubmitBtn.disabled = true;
            profileSubmitBtn.innerHTML = '<i class="bx bx-loader-alt bx-spin btn-icon"></i>Mise à jour...';

            try {
                await new Promise(resolve => setTimeout(resolve, 1000));

                profileMessage.className = 'message message-success';
                profileMessage.innerHTML = '<i class="bx bx-check-circle"></i>Email mis à jour avec succès !';
                profileMessage.classList.remove('hidden');

                setTimeout(() => {
                    profileMessage.classList.add('hidden');
                }, 3000);

            } catch (error) {
                profileMessage.className = 'message message-error';
                profileMessage.innerHTML = '<i class="bx bx-error-circle"></i>Erreur lors de la mise à jour de l\'email';
                profileMessage.classList.remove('hidden');
            } finally {
                profileSubmitBtn.disabled = false;
                profileSubmitBtn.innerHTML = '<i class="bx bx-save btn-icon"></i>Mettre à jour l\'email';
            }
        });

        // Notification settings form submission
        const notificationForm = document.getElementById('notificationForm');
        const saveNotifBtn = document.getElementById('saveNotifBtn');

        if (notificationForm) {
            notificationForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                saveNotifBtn.disabled = true;
                saveNotifBtn.innerHTML = '<i class="bx bx-loader-alt bx-spin btn-icon"></i>Enregistrement...';

                try {
                    // Simulate API call
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // Show success message
                    alert('✅ Préférences de notification sauvegardées!\n\n🔔 Vos paramètres ont été mis à jour avec succès.');

                } catch (error) {
                    alert('❌ Erreur lors de la sauvegarde des préférences.\n\n🔄 Veuillez réessayer.');
                } finally {
                    saveNotifBtn.disabled = false;
                    saveNotifBtn.innerHTML = '<i class="bx bx-save btn-icon"></i>Enregistrer les préférences';
                }
            });
        }

        function closePermitModal() {
            document.getElementById('permitModal').classList.remove('active');
            // Hide archive-specific cards when closing
            document.getElementById('archiveInfoCard').style.display = 'none';
            document.getElementById('safetyInfoCard').style.display = 'none';
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('permitModal');
            if (e.target === modal) {
                closePermitModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePermitModal();
            }
        });



        // Suspension form submission
        document.getElementById('suspensionForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const permitNumber = document.getElementById('permitNumber').value;
            const reason = document.getElementById('reason').value;
            const urgency = document.getElementById('urgency').value;
            const comments = document.getElementById('comments').value;

            if (!reason || !urgency || !comments.trim()) {
                alert('⚠️ Veuillez remplir tous les champs obligatoires.');
                return;
            }

            const confirmation = confirm(`🚫 Confirmer la suspension/annulation du permis ${permitNumber}?\n\n📋 Motif: ${reason}\n⚡ Urgence: ${urgency}\n💬 Commentaires: ${comments.substring(0, 50)}...\n\n⚠️ Cette action est irréversible et notifiera immédiatement toutes les parties concernées.`);

            if (confirmation) {
                alert(`✅ Suspension/Annulation confirmée!\n\n📋 Permis: ${permitNumber}\n📅 Date: ${new Date().toLocaleString('fr-FR')}\n👤 Déclaré par: Responsable Exécution\n\n📧 Toutes les parties concernées ont été notifiées.`);

                // Clear the form and return to history section
                document.getElementById('suspensionForm').reset();
                showSection('history');
            }
        });



        // Function to start suspension declaration
        function startSuspensionDeclaration(permitId) {
            // Find permit data from the table
            const permitRow = document.querySelector(`tr[data-permit-id="${permitId}"]`);
            if (permitRow) {
                const cells = permitRow.querySelectorAll('td');

                // Fill the suspension form with permit data
                document.getElementById('permitNumber').value = permitId;
                document.getElementById('permitDescription').value = cells[1].textContent.trim();
                document.getElementById('permitZone').value = cells[2].textContent.trim();
                document.getElementById('permitRequester').value = 'Responsable Maintenance';

                // Switch to suspension declaration section
                showSection('suspension-declaration');
            }
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Set default active section
            showSection('permits');
        });
    </script>
</body>
</html>