<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Système de Gestion des Permis de Travail - Administrateur</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"/>
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet"/>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #3b82f6;
            --primary-dark: #2563eb;
            --primary-light: #93c5fd;
            --secondary: #4f46e5;
            --accent: #3b82f6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --sidebar-width: 280px;
            --topbar-height: 70px;
            --sidebar-collapsed-width: 80px;
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;
            --transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--gray-50);
            color: var(--gray-900);
            display: flex;
            min-height: 100vh;
            line-height: 1.5;
            font-size: 15px;
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--gray-900);
            color: var(--gray-100);
            position: fixed;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
            transition: var(--transition-slow);
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            border-right: 1px solid rgba(255, 255, 255, 0.05);
            overflow: hidden;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar.collapsed .logo-text,
        .sidebar.collapsed .menu-title,
        .sidebar.collapsed .menu-text,
        .sidebar.collapsed .menu-badge,
        .sidebar.collapsed .user-info,
        .sidebar.collapsed .user-actions {
            opacity: 0;
            width: 0;
            height: 0;
            margin: 0;
            padding: 0;
            overflow: hidden;
            position: absolute;
        }

        .sidebar.collapsed .menu-link {
            justify-content: center;
            padding: 12px 0;
        }

        .sidebar.collapsed .menu-icon {
            margin-right: 0;
            font-size: 24px;
        }

        .sidebar-header {
            padding: 0 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: var(--topbar-height);
            min-height: var(--topbar-height);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 18px;
            font-weight: 700;
            color: white;
            white-space: nowrap;
        }

        .logo-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            flex-shrink: 0;
        }

        .logo-text {
            transition: var(--transition-slow);
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--gray-400);
            font-size: 20px;
            cursor: pointer;
            transition: var(--transition);
            padding: 8px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar-toggle:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-menu {
            flex: 1;
            padding: 16px 0;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--gray-700) var(--gray-800);
        }

        .sidebar-menu::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar-menu::-webkit-scrollbar-track {
            background: var(--gray-800);
        }

        .sidebar-menu::-webkit-scrollbar-thumb {
            background-color: var(--gray-700);
            border-radius: var(--radius-full);
        }

        .menu-group {
            margin-bottom: 24px;
        }

        .menu-title {
            padding: 0 24px 12px;
            font-size: 12px;
            font-weight: 600;
            color: var(--gray-400);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: var(--transition-slow);
        }

        .menu-items {
            list-style: none;
        }

        .menu-item {
            margin: 2px 0;
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: var(--gray-300);
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: var(--transition);
            font-size: 14px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .menu-link:hover {
            background-color: var(--gray-800);
            color: white;
        }

        .menu-link.active {
            background-color: rgba(37, 99, 235, 0.1);
            border-left-color: var(--primary);
            color: white;
        }

        .menu-link.active .menu-icon {
            color: var(--primary-light);
        }

        .menu-icon {
            margin-right: 12px;
            font-size: 20px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-400);
            transition: var(--transition);
            flex-shrink: 0;
        }

        .menu-text {
            transition: var(--transition-slow);
            white-space: nowrap;
        }

        .sidebar-footer {
            padding: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: auto;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px;
            border-radius: var(--radius-md);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .user-profile:hover {
            background-color: var(--gray-800);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            flex-shrink: 0;
            position: relative;
        }

        .user-info {
            flex: 1;
            min-width: 0;
            transition: var(--transition-slow);
        }

        .user-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-role {
            font-size: 12px;
            color: var(--gray-400);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-actions {
            display: flex;
            gap: 8px;
            transition: var(--transition-slow);
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--gray-400);
            cursor: pointer;
            transition: var(--transition);
            border: none;
            position: relative;
        }

        .action-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 18px;
            height: 18px;
            background-color: var(--danger);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 700;
            color: white;
            border: 2px solid var(--gray-900);
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: var(--transition-slow);
            display: flex;
            flex-direction: column;
        }

        .sidebar.collapsed ~ .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        /* Top Navigation */
        .topbar {
            height: var(--topbar-height);
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            box-shadow: var(--shadow-sm);
            border-bottom: 1px solid var(--gray-200);
            position: sticky;
            top: 0;
            z-index: 900;
        }

        .topbar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .menu-toggle {
            background: none;
            border: none;
            color: var(--gray-500);
            font-size: 24px;
            cursor: pointer;
            transition: var(--transition);
            display: none;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
        }

        .menu-toggle:hover {
            background-color: var(--gray-100);
            color: var(--gray-700);
        }

        .breadcrumbs {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .breadcrumb-item {
            color: var(--gray-500);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .breadcrumb-item:not(:last-child)::after {
            content: '/';
            color: var(--gray-300);
            margin-left: 8px;
        }

        .breadcrumb-item.active {
            color: var(--gray-700);
            font-weight: 500;
        }

        .topbar-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .topbar-action {
            position: relative;
            cursor: pointer;
        }

        .action-btn-lg {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            color: var(--gray-500);
            cursor: pointer;
            transition: var(--transition);
            border: none;
            position: relative;
        }

        .action-btn-lg:hover {
            background-color: var(--gray-100);
            color: var(--gray-700);
        }

        .action-btn-lg .notification-badge {
            border-color: white;
        }

        .user-dropdown {
            position: absolute;
            top: calc(100% + 10px);
            right: 0;
            width: 280px;
            background-color: white;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-xl);
            padding: 8px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: var(--transition);
        }

        .user-dropdown.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-header {
            padding: 12px 16px;
            border-bottom: 1px solid var(--gray-100);
            margin-bottom: 8px;
        }

        .dropdown-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--gray-900);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            border-radius: var(--radius-sm);
            color: var(--gray-700);
            text-decoration: none;
            font-size: 14px;
            transition: var(--transition);
            cursor: pointer;
        }

        .dropdown-item:hover {
            background-color: var(--gray-50);
            color: var(--primary);
        }

        .dropdown-item i {
            margin-right: 12px;
            font-size: 18px;
            width: 20px;
            color: var(--gray-500);
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--gray-100);
            margin: 8px 0;
        }
        
        /* Settings Styles */
        .checkbox-label {
            display: flex;
            align-items: center;
            font-weight: 600;
            color: var(--gray-800);
            cursor: pointer;
            margin-bottom: 4px;
        }
        
        .checkbox-label input[type="checkbox"] {
            margin-right: 10px;
            width: 18px;
            height: 18px;
            border: 2px solid var(--primary);
            border-radius: 4px;
            cursor: pointer;
        }
        
        .form-text {
            font-size: 13px;
            color: var(--gray-500);
            margin-top: 4px;
            margin-left: 28px;
            margin-bottom: 16px;
        }
        
        .profile-section {
            background: white;
            border-radius: var(--radius-lg);
            margin-bottom: 24px;
            border: 1px solid var(--gray-200);
            overflow: hidden;
        }
        
        .profile-section-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--gray-200);
            background-color: var(--gray-50);
        }
        
        .profile-section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
        }
        
        .profile-section-body {
            padding: 24px;
        }
        
        .profile-actions {
            display: flex;
            justify-content: flex-end;
            padding-top: 16px;
            margin-top: 24px;
            border-top: 1px solid var(--gray-200);
        }

        /* Content Area */
        .content-wrapper {
            flex: 1;
            padding: 24px;
            background-color: var(--gray-50);
        }

        .content-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
        }

        .content-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
        }

        .content-subtitle {
            font-size: 16px;
            color: var(--gray-600);
            margin-top: 8px;
        }

        .content-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 20px;
            border-radius: var(--radius-md);
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: var(--transition);
            border: 1px solid transparent;
            text-decoration: none;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .btn-outline {
            background-color: transparent;
            border-color: var(--gray-300);
            color: var(--gray-700);
        }

        .btn-outline:hover {
            background-color: var(--gray-50);
            border-color: var(--gray-400);
        }

        .btn-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        /* Form Styles */
        .form-section {
            background-color: white;
            padding: 32px;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            margin-bottom: 24px;
            border: 1px solid var(--gray-200);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-row-4 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--gray-700);
        }

        .label-icon {
            margin-right: 6px;
            font-size: 16px;
            color: var(--primary);
        }

        input[type="text"],
        input[type="email"],
        input[type="password"],
        input[type="date"],
        textarea,
        select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 14px;
            background-color: white;
            transition: var(--transition);
            font-family: inherit;
        }

        input:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 120px;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 12px;
            margin-top: 12px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            transition: var(--transition);
            cursor: pointer;
        }

        .checkbox-item:hover {
            background-color: var(--gray-50);
            border-color: var(--primary);
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary);
            margin: 0;
        }

        .checkbox-item label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            font-size: 14px;
        }

        table {
            width: 90%; /* Reduced table width */
            border-collapse: collapse;
            margin: 0 auto 20px; /* Center the table */
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        th,
        td {
            padding: 10px; /* Reduced padding */
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
            font-size: 13px; /* Reduced font size */
        }

        th {
            background-color: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
            font-size: 13px;
        }

        td input {
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-sm);
            padding: 8px 10px; /* Reduced padding */
            font-size: 14px;
            width: 100%;
        }

        /* Profile Styles */
        .profile-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            padding: 32px;
            border-radius: var(--radius-xl);
            margin-bottom: 32px;
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .profile-info {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: var(--radius-full);
            background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 28px;
            flex-shrink: 0;
            box-shadow: var(--shadow-md);
            border: 3px solid rgba(255,255,255,0.2);
        }

        .profile-details {
            flex: 1;
        }

        .profile-name {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .profile-username {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 12px;
        }

        .profile-role {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border-radius: var(--radius-full);
            font-size: 14px;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .profile-form {
            background-color: white;
            padding: 32px;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
        }

        .form-section-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
        }


        /* Readonly fields */
        input[readonly] {
            background-color: var(--gray-50);
            color: var(--gray-600);
            cursor: not-allowed;
        }

        /* Success/Error Messages */
        .message {
            padding: 16px 20px;
            border-radius: var(--radius-md);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
        }

        .message-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .message-error {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Section Headers */
        .section-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--gray-200);
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--gray-900);
        }

        .section-icon {
            font-size: 24px;
            color: var(--primary);
        }

        /* Required field indicator */
        .required {
            color: var(--danger);
            margin-left: 4px;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            :root {
                --sidebar-width: 240px;
            }
        }

        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 1100;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .menu-toggle {
                display: flex;
            }

            .sidebar-toggle {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .topbar {
                padding: 0 16px;
            }

            .content-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .content-actions {
                width: 100%;
                justify-content: flex-end;
            }

            .form-row,
            .form-row-3,
            .form-row-4 {
                grid-template-columns: 1fr;
            }
            
            /* Dashboard Responsive */
            .stats-container {
                grid-template-columns: 1fr 1fr;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 767px) {
            .stats-container {
                grid-template-columns: 1fr;
            }

            .profile-info {
                flex-direction: column;
                text-align: center;
            }

            .checkbox-group {
                grid-template-columns: 1fr;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 576px) {
            .topbar-actions {
                gap: 8px;
            }

            .action-btn-lg {
                width: 36px;
                height: 36px;
            }

            .content-wrapper {
                padding: 16px;
            }

            .form-section,
            .profile-form {
                padding: 20px;
            }

            .profile-header {
                padding: 24px;
            }
        }

        /* Animation */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-section,
        .profile-header,
        .profile-form {
            animation: fadeIn 0.5s ease-out;
        }

        /* Dashboard Styles */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .stats-card {
            background-color: white;
            border-radius: var(--radius-lg);
            padding: 24px;
            box-shadow: var(--shadow-sm);
            display: flex;
            align-items: center;
            gap: 16px;
            transition: var(--transition);
        }
        
        .stats-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }
        
        .stats-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .stats-content {
            flex: 1;
        }
        
        .stats-title {
            font-size: 14px;
            color: var(--gray-500);
            margin-bottom: 4px;
        }
        
        .stats-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 4px;
        }
        
        .stats-change {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .increase {
            color: var(--success);
        }
        
        .decrease {
            color: var(--danger);
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
        }
        
        .dashboard-card {
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }
        
        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-800);
        }
        
        .chart-container {
            padding: 16px;
            height: 250px;
            position: relative;
        }
        
        .activity-list {
            padding: 16px;
        }
        
        .activity-item {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            padding: 12px 0;
            border-bottom: 1px solid var(--gray-100);
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
        }
        
        .activity-title {
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .activity-desc {
            font-size: 14px;
            color: var(--gray-600);
            margin-bottom: 4px;
        }
        
        .activity-time {
            font-size: 12px;
            color: var(--gray-500);
        }

        /* Hidden sections */
        .hidden {
            display: none;
        }

        /* Loading states */
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn:disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        /* Enhanced Table Styles */
        table {
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            width: 90%; /* Further reduced table width */
            margin: 0 auto 20px; /* Center the table */
        }

        th, td {
            padding: 10px; /* Reduced padding */
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
            font-size: 13px; /* Reduced font size */
        }

        th {
            background-color: var(--gray-100);
            font-weight: 600;
            color: var(--gray-700);
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:last-child td {
            border-bottom: none;
        }

        /* Enhanced Button Styles */
        .btn-table {
            padding: 6px 10px; /* Reduced padding */
            border-radius: var(--radius-sm);
            font-size: 12px; /* Reduced font size */
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 4px; /* Reduced gap */
        }

        .btn-table.btn-view {
            background-color: var(--primary);
            color: white;
        }

        .btn-table.btn-view:hover {
            background-color: var(--primary-dark);
        }

        .btn-table.btn-edit {
            background-color: var(--warning);
            color: var(--gray-900);
        }

        .btn-table.btn-edit:hover {
            background-color: var(--warning);
            opacity: 0.8;
        }

        .btn-table.btn-delete {
            background-color: var(--danger);
            color: white;
        }

        .btn-table.btn-delete:hover {
            background-color: var(--danger);
            opacity: 0.8;
        }

        .table-actions {
            display: flex;
            gap: 6px; /* Reduced gap */
            justify-content: center; /* Center the buttons */
        }

        /* Search Bar Styles */
        .search-bar {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .search-bar input[type="text"] {
            padding: 10px 14px; /* Reduced padding */
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 13px; /* Reduced font size */
            width: 250px; /* Reduced width */
            transition: var(--transition);
        }

        .search-bar input[type="text"]:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-bar button {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 10px 18px; /* Reduced padding */
            border-radius: var(--radius-md);
            font-size: 13px; /* Reduced font size */
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
        }

        .search-bar button:hover {
            background-color: var(--primary-dark);
        }

        /* Action Bar Styles */
        .action-bar {
            margin-bottom: 20px;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .action-bar .btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            font-size: 14px;
            font-weight: 500;
            border-radius: var(--radius-md);
            transition: var(--transition);
            text-decoration: none;
            cursor: pointer;
        }

        .action-bar .btn-primary {
            background-color: var(--primary);
            color: white;
            border: 1px solid var(--primary);
        }

        .action-bar .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }

        /* Popup Styles */
        .popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }

        /* Position editUserPopup to the right */
        #editUserPopup.popup {
            justify-content: flex-end;
            padding-right: 20px;
        }

        .popup.active {
            opacity: 1;
            visibility: visible;
        }

        .popup-inner {
            background-color: white;
            padding: 24px; /* Reduced padding */
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            width: 400px; /* Reduced width */
            max-width: 90%;
            max-height: 80%; /* Added max height */
            overflow-y: auto; /* Added scroll for long content */
            position: relative;
        }

        .popup-close {
            position: absolute;
            top: 12px; /* Reduced top */
            right: 12px; /* Reduced right */
            background: none;
            border: none;
            font-size: 20px; /* Reduced font size */
            color: var(--gray-500);
            cursor: pointer;
            transition: var(--transition);
        }

        .popup-close:hover {
            color: var(--gray-700);
        }

        .popup-title {
            font-size: 20px; /* Reduced font size */
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 16px; /* Reduced margin */
        }

        .popup-content {
            margin-bottom: 16px; /* Reduced margin */
        }

        .popup-content p {
            margin-bottom: 6px; /* Reduced margin */
            font-size: 14px; /* Reduced font size */
        }

        .popup-form .form-group {
            margin-bottom: 16px; /* Reduced margin */
        }

        .popup-form label {
            display: block;
            font-size: 13px; /* Reduced font size */
            font-weight: 600;
            margin-bottom: 6px; /* Reduced margin */
            color: var(--gray-700);
        }

        .popup-form input[type="text"],
        .popup-form input[type="email"],
        .popup-form input[type="password"],
        .popup-form select {
            width: 100%;
            padding: 10px 14px; /* Reduced padding */
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 13px; /* Reduced font size */
            background-color: white;
            transition: var(--transition);
            font-family: inherit;
        }

        .popup-form input:focus,
        .popup-form select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .popup-form .btn-primary {
            margin-top: 16px; /* Reduced margin */
        }

        /* Enhanced Edit User Popup Styles */
        #editUserPopup .popup-inner {
            width: 700px; /* Increased width for more space */
            max-height: 80vh; /* Increased max height */
            overflow-y: auto;
            position: relative;
            padding: 32px; /* Increased padding */
            border-top-right-radius: var(--radius-xl);
            border-bottom-right-radius: var(--radius-xl);
            border-top-left-radius: var(--radius-sm);
            border-bottom-left-radius: var(--radius-sm);
            box-shadow: var(--shadow-xl);
        }

        #editUserPopup .popup-form {
            max-height: none; /* Remove max height */
            overflow-y: visible; /* Disable scrolling */
            padding: 24px; /* Increased padding */
        }

        #editUserPopup .popup-form .form-group {
            margin-bottom: 20px; /* Increased spacing */
        }

        #editUserPopup .popup-form label {
            font-size: 14px; /* Slightly larger label font */
            margin-bottom: 8px;
        }

        #editUserPopup .popup-form input[type="text"],
        #editUserPopup .popup-form input[type="email"],
        #editUserPopup .popup-form input[type="password"],
        #editUserPopup .popup-form select {
            padding: 12px 16px; /* Slightly larger input padding */
            font-size: 14px;
        }

        #editUserPopup .popup-form .btn-primary {
            margin-top: 32px; /* Increased button margin */
            padding: 12px 20px; /* Larger button padding */
            font-size: 14px;
        }

        /* Apply form-row to the edit form */
        #editUserPopup .popup-form .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        /* Responsive adjustments for Edit User Popup */
        @media (max-width: 768px) {
            #editUserPopup .popup-inner {
                width: 95%; /* Take up more width on smaller screens */
                padding: 24px;
            }
        }

        @media (max-width: 576px) {
            #editUserPopup .popup-inner {
                width: 95%; /* Take up more width on smaller screens */
                padding: 16px;
            }
        }

        /* Enhanced Edit User Popup Styles */
        #editUserPopup .popup-inner {
            width: 800px; /* Further increased width */
            max-height: 90vh; /* Further increased max height */
            padding: 40px; /* Even more padding */
        }

        #editUserPopup .popup-title {
            font-size: 24px; /* Larger title */
            margin-bottom: 24px;
            color: var(--secondary); /* A different color for emphasis */
            text-align: center; /* Center the title */
        }

        #editUserPopup .popup-form {
            padding: 32px; /* More padding inside the form */
            border: 1px solid var(--gray-200); /* Add a subtle border */
            border-radius: var(--radius-lg); /* Rounded corners for the form */
            box-shadow: var(--shadow-sm); /* Subtle shadow for the form */
        }

        #editUserPopup .popup-form .form-group {
            margin-bottom: 24px; /* Increased spacing between form groups */
        }

        #editUserPopup .popup-form label {
            font-size: 15px; /* Slightly larger label font */
            font-weight: 500; /* Slightly lighter font weight */
            color: var(--gray-700);
            margin-bottom: 10px; /* More space below the label */
            display: flex; /* Align label and icon */
            align-items: center;
            gap: 6px; /* Space between label and icon */
        }

        #editUserPopup .popup-form label i {
            color: var(--primary); /* Icon color */
            font-size: 18px; /* Larger icon size */
        }

        #editUserPopup .popup-form input[type="text"],
        #editUserPopup .popup-form input[type="email"],
        #editUserPopup .popup-form input[type="password"],
        #editUserPopup .popup-form select {
            padding: 14px 18px; /* Slightly larger input padding */
            font-size: 15px; /* Slightly larger font size */
            border-radius: var(--radius-md); /* Rounded corners for inputs */
            border: 1px solid var(--gray-300); /* Subtle border */
            transition: border-color 0.3s ease; /* Smooth border color transition */
        }

        #editUserPopup .popup-form input[type="text"]:focus,
        #editUserPopup .popup-form input[type="email"]:focus,
        #editUserPopup .popup-form input[type="password"]:focus,
        #editUserPopup .popup-form select:focus {
            border-color: var(--primary); /* Highlight border on focus */
            box-shadow: none; /* Remove default box-shadow */
        }

        #editUserPopup .popup-form select {
            appearance: none; /* Remove default arrow */
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236B7280'%3E%3Cpath d='M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z'/%3E%3C/svg%3E"); /* Custom arrow */
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
        }

        #editUserPopup .popup-form .btn-primary {
            margin-top: 40px; /* Even more button margin */
            padding: 14px 24px; /* Larger button padding */
            font-size: 16px; /* Larger button font */
            border-radius: var(--radius-lg); /* Rounded corners for the button */
            box-shadow: var(--shadow-sm); /* Subtle shadow for the button */
            transition: background-color 0.3s ease, transform 0.3s ease; /* Smooth transitions */
        }

        #editUserPopup .popup-form .btn-primary:hover {
            background-color: var(--primary-dark); /* Darker background on hover */
            transform: translateY(-2px); /* Slight lift on hover */
            box-shadow: var(--shadow-md); /* Slightly larger shadow on hover */
        }

        #editUserPopup .popup-form .form-row {
            gap: 24px; /* Increased gap between form elements */
        }

        /* Style the close button */
        #editUserPopup .popup-close {
            top: 16px; /* More space from the top */
            right: 16px; /* More space from the right */
            font-size: 24px; /* Larger close button */
            color: var(--gray-500); /* Gray color */
            transition: color 0.3s ease; /* Smooth color transition */
        }

        #editUserPopup .popup-close:hover {
            color: var(--danger); /* Red color on hover */
        }

        /* Add a subtle background to the popup overlay */
        .popup {
            background-color: rgba(0, 0, 0, 0.3); /* Slightly lighter background */
        }

        /* Responsive adjustments for Edit User Popup */
        @media (max-width: 768px) {
            #editUserPopup .popup-inner {
                width: 95%; /* Take up more width on smaller screens */
                padding: 32px;
            }

            #editUserPopup .popup-form .form-row {
                grid-template-columns: 1fr; /* Stack form elements on smaller screens */
            }
        }

        @media (max-width: 576px) {
            #editUserPopup .popup-inner {
                padding: 24px;
            }
        }

        /* Status Badge Styles */
        .status-badge {
            padding: 4px 12px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-inactive {
            background-color: rgba(107, 114, 128, 0.1);
            color: var(--gray-600);
            border: 1px solid rgba(107, 114, 128, 0.2);
        }

        .status-low {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .status-medium {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .status-high {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .status-critical {
            background-color: rgba(127, 29, 29, 0.1);
            color: #7f1d1d;
            border: 1px solid rgba(127, 29, 29, 0.2);
        }

        .status-zone0 {
            background-color: rgba(220, 38, 38, 0.1);
            color: #dc2626;
            border: 1px solid rgba(220, 38, 38, 0.2);
        }

        .status-zone1 {
            background-color: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .status-zone2 {
            background-color: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.2);
        }
    </style>
</head>
<body>
    <!-- Sidebar Navigation -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <div class="logo-icon">
                    <img src="sonatrach-logo.png" alt="Logo Sonatrach" style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;"/>
                </div>
                <span class="logo-text">PT Pro</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class='bx bx-chevron-left'></i>
            </button>
        </div>
        <nav class="sidebar-menu">
            <div class="menu-group">
                <h3 class="menu-title">Navigation</h3>
                <ul class="menu-items">
                    <li class="menu-item">
                        <div class="menu-link active" onclick="showSection('dashboard')">
                            <i class='bx bxs-dashboard menu-icon'></i>
                            <span class="menu-text">Tableau de bord</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('users')">
                            <i class='bx bxs-user-account menu-icon'></i>
                            <span class="menu-text">Gérer les utilisateurs</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('add-user')">
                            <i class='bx bx-user-plus menu-icon'></i>
                            <span class="menu-text">Ajouter un utilisateur</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('zones')">
                            <i class='bx bx-map menu-icon'></i>
                            <span class="menu-text">Gérer les zones</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('profile')">
                            <i class='bx bxs-user menu-icon'></i>
                            <span class="menu-text">Mon Profil</span>
                        </div>
                    </li>
                </ul>
            </div>
        </nav>
        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">
                    AD
                    <span class="notification-badge">3</span>
                </div>
                <div class="user-info">
                    <div class="user-name">Admin User</div>
                    <div class="user-role">Administrateur</div>
                </div>
                <div class="user-actions">
                    <button class="action-btn" onclick="logout()">
                        <i class='bx bx-log-out'></i>
                    </button>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content">
        <!-- Top Navigation Bar -->
        <div class="topbar">
            <div class="topbar-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class='bx bx-menu'></i>
                </button>
                <div class="breadcrumbs">
                    <span class="breadcrumb-item">ACCUEIL</span>
                    <span class="breadcrumb-item active" id="page-title">TABLEAU DE BORD</span>
                </div>
            </div>
            <div class="topbar-actions">
                <div class="topbar-action">
                    <button class="action-btn-lg">
                        <i class='bx bx-bell'></i>
                        <span class="notification-badge">1</span>
                    </button>
                </div>
                <div class="topbar-action" id="userDropdownTrigger">
                    <button class="action-btn-lg">
                        <div class="user-avatar" style="width: 32px; height: 32px; font-size: 12px;">
                            AD
                        </div>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <div class="dropdown-header">
                            <div class="dropdown-title">Paramètres du compte</div>
                        </div>
                        <div class="dropdown-item" onclick="showSection('profile')">
                            <i class='bx bx-user'></i>
                            <span>Mon profil</span>
                        </div>
                        <div class="dropdown-divider"></div>
                        <div class="dropdown-item" onclick="logout()">
                            <i class='bx bx-log-out'></i>
                            <span>Déconnexion</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-wrapper">
            <!-- Dashboard Section -->
            <section id="mespermis" class="form-section">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Mes Permis de Travail</h1>
                        <p class="content-subtitle">Consultez et gérez l'ensemble de vos demandes de permis de travail</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-outline" onclick="refreshPermits()" title="Actualiser la liste">
                            <i class='bx bx-refresh btn-icon'></i>
                            Actualiser
                        </button>
                        <button class="btn btn-outline" onclick="exportPermits()" title="Exporter en PDF">
                            <i class='bx bx-download btn-icon'></i>
                            Exporter
                        </button>
                        <button class="btn btn-primary" onclick="showSection('initiation')">
                            <i class='bx bx-plus btn-icon'></i>
                            Nouveau permis
                        </button>
                    </div>
                </div>
                
                <!-- Stats Cards -->
                <div class="stats-container">
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(37, 99, 235, 0.1); color: var(--primary);">
                            <i class='bx bx-file'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">Permis de travail</div>
                            <div class="stats-value">156</div>
                            <div class="stats-change increase">
                                <i class='bx bx-up-arrow-alt'></i>
                                <span>12% ce mois</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(16, 185, 129, 0.1); color: var(--success);">
                            <i class='bx bx-check-circle'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">Permis approuvés</div>
                            <div class="stats-value">124</div>
                            <div class="stats-change increase">
                                <i class='bx bx-up-arrow-alt'></i>
                                <span>8% ce mois</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(245, 158, 11, 0.1); color: var(--warning);">
                            <i class='bx bx-time'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">En attente</div>
                            <div class="stats-value">32</div>
                            <div class="stats-change decrease">
                                <i class='bx bx-down-arrow-alt'></i>
                                <span>5% ce mois</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(239, 68, 68, 0.1); color: var(--danger);">
                            <i class='bx bx-user'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">Utilisateurs actifs</div>
                            <div class="stats-value">78</div>
                            <div class="stats-change increase">
                                <i class='bx bx-up-arrow-alt'></i>
                                <span>15% ce mois</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts & Activity -->
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3 class="card-title">Permis par type</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="permitsChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3 class="card-title">Activité récente</h3>
                        </div>
                        <div class="activity-list">
                            <div class="activity-item">
                                <div class="activity-icon" style="background-color: rgba(37, 99, 235, 0.1); color: var(--primary);">
                                    <i class='bx bx-file-plus'></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Nouveau permis créé</div>
                                    <div class="activity-desc">Zone A - Installation électrique</div>
                                    <div class="activity-time">Il y a 25 minutes</div>
                                </div>
                            </div>
                            
                            <div class="activity-item">
                                <div class="activity-icon" style="background-color: rgba(16, 185, 129, 0.1); color: var(--success);">
                                    <i class='bx bx-check-double'></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Permis approuvé</div>
                                    <div class="activity-desc">Zone B - Maintenance préventive</div>
                                    <div class="activity-time">Il y a 1 heure</div>
                                </div>
                            </div>
                            
                            <div class="activity-item">
                                <div class="activity-icon" style="background-color: rgba(245, 158, 11, 0.1); color: var(--warning);">
                                    <i class='bx bx-revision'></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Permis en révision</div>
                                    <div class="activity-desc">Zone C - Inspection des conduites</div>
                                    <div class="activity-time">Il y a 3 heures</div>
                                </div>
                            </div>
                            
                            <div class="activity-item">
                                <div class="activity-icon" style="background-color: rgba(124, 58, 237, 0.1); color: var(--secondary);">
                                    <i class='bx bx-user-plus'></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Nouvel utilisateur</div>
                                    <div class="activity-desc">Sophie Leroux - Autorité de Zone</div>
                                    <div class="activity-time">Il y a 5 heures</div>
                                </div>
                            </div>
                            
                            <div class="activity-item">
                                <div class="activity-icon" style="background-color: rgba(6, 182, 212, 0.1); color: var(--info);">
                                    <i class='bx bx-calendar-edit'></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Permis modifié</div>
                                    <div class="activity-desc">Zone D - Extension de durée</div>
                                    <div class="activity-time">Il y a 8 heures</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3 class="card-title">Permis par zone</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="zonesChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3 class="card-title">Utilisateurs par rôle</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="usersChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Initiation Section -->
            <section id="initiation" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Demande du Permis de Travail</h1>
                        <p class="content-subtitle">Remplissez le formulaire pour initier une nouvelle demande de permis de travail</p>
                    </div>
                </div>

                <form id="initiationForm" class="permit-form">
                    <!-- Section 1: Basic Information -->
                    <div class="section-header">
                        <i class='bx bx-info-circle section-icon'></i>
                        <h2 class="section-title">Informations générales</h2>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <i class='bx bx-edit label-icon'></i>
                                Titre du travail <span class="required">*</span>
                            </label>
                            <input type="text" name="workTitle" required placeholder="Entrez le titre du travail"/>
                        </div>
                        <div class="form-group">
                            <label>
                                <i class='bx bx-map-pin label-icon'></i>
                                Lieu du travail <span class="required">*</span>
                            </label>
                            <input type="text" name="location" required placeholder="Entrez le lieu du travail"/>
                        </div>
                    </div>

                    <!-- Section 2: Permit Type -->
                    <div class="section-header">
                        <i class='bx bx-file-blank section-icon'></i>
                        <h2 class="section-title">Type de permis demandé</h2>
                    </div>

                    <div class="permit-types">
                        <label class="permit-type-card">
                            <input type="radio" name="permitType" value="Permis de travail général" required>
                            <div class="permit-type-content">
                                <i class='bx bx-wrench permit-type-icon'></i>
                                <h3>Permis de travail général</h3>
                                <p>Pour les travaux de maintenance standard</p>
                            </div>
                        </label>
                        
                        <label class="permit-type-card">
                            <input type="radio" name="permitType" value="Permis de travail à chaud" required>
                            <div class="permit-type-content">
                                <i class='bx bx-flame permit-type-icon'></i>
                                <h3>Permis de travail à chaud</h3>
                                <p>Soudage, découpage, meulage</p>
                            </div>
                        </label>
                        
                        <label class="permit-type-card">
                            <input type="radio" name="permitType" value="Permis électrique" required>
                            <div class="permit-type-content">
                                <i class='bx bx-bolt permit-type-icon'></i>
                                <h3>Permis électrique</h3>
                                <p>Travaux sur installations électriques</p>
                            </div>
                        </label>
                        
                        <label class="permit-type-card">
                            <input type="radio" name="permitType" value="Permis de travail en hauteur" required>
                            <div class="permit-type-content">
                                <i class='bx bx-up-arrow-alt permit-type-icon'></i>
                                <h3>Permis de travail en hauteur</h3>
                                <p>Travaux à plus de 2 mètres</p>
                            </div>
                        </label>
                        
                        <label class="permit-type-card">
                            <input type="radio" name="permitType" value="Permis d'espace confiné" required>
                            <div class="permit-type-content">
                                <i class='bx bx-door-open permit-type-icon'></i>
                                <h3>Permis d'espace confiné</h3>
                                <p>Travaux dans espaces restreints</p>
                            </div>
                        </label>
                        
                        <label class="permit-type-card">
                            <input type="radio" name="permitType" value="Permis de levage" required>
                            <div class="permit-type-content">
                                <i class='bx bx-up-arrow permit-type-icon'></i>
                                <h3>Permis de levage</h3>
                                <p>Utilisation d'équipements de levage</p>
                            </div>
                        </label>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <i class='bx bx-building label-icon'></i>
                                Unité/Zone <span class="required">*</span>
                            </label>
                            <input type="text" name="unitZone" required placeholder="Entrez l'unité ou zone"/>
                        </div>
                        <div class="form-group">
                            <label>
                                <i class='bx bx-cog label-icon'></i>
                                Installation/équipement <span class="required">*</span>
                            </label>
                            <input type="text" name="installationEquipment" required placeholder="Décrivez l'installation ou l'équipement"/>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>
                            <i class='bx bx-edit label-icon'></i>
                            Description du travail <span class="required">*</span>
                        </label>
                        <textarea name="workDescription" required placeholder="Décrivez en détail le travail à effectuer" rows="4"></textarea>
                    </div>

                    <div class="form-group">
                        <label>
                            <i class='bx bx-calendar label-icon'></i>
                            Période prévisionnelle demandée <span class="required">*</span>
                        </label>
                        <div class="form-row-4">
                            <div>
                                <input type="date" name="dateFrom" id="dateFrom" required min="" />
                                <small style="color: var(--gray-500); font-size: 12px;">Date de début (min. 24h à l'avance)</small>
                                <div class="validation-message" id="dateFromError"></div>
                            </div>
                            <div>
                                <input type="date" name="dateTo" id="dateTo" required/>
                                <small style="color: var(--gray-500); font-size: 12px;">Date de fin (max. 7 jours)</small>
                                <div class="validation-message" id="dateToError"></div>
                            </div>
                            <div>
                                <input type="time" name="timeFrom" required/>
                                <small style="color: var(--gray-500); font-size: 12px;">Heure de début</small>
                            </div>
                            <div>
                                <input type="time" name="timeTo" required/>
                                <small style="color: var(--gray-500); font-size: 12px;">Heure de fin</small>
                            </div>
                        </div>
                    </div>

                    <!-- Section 3: Tools and Equipment -->
                    <div class="section-header" style="margin-top: 20px;">
                        <i class='bx bx-wrench section-icon'></i>
                        <h2 class="section-title">Outilage/Équipement utilisé</h2>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>Outillage manuel</th>
                                <th>Machines outils</th>
                                <th>Véhicules/engins</th>
                                <th>Équipement de levage</th>
                                <th>Travaux en hauteur</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="text" name="toolsManual" placeholder="Outils manuels"/></td>
                                <td><input type="text" name="toolsMachines" placeholder="Machines"/></td>
                                <td><input type="text" name="toolsVehicles" placeholder="Véhicules"/></td>
                                <td><input type="text" name="toolsLifting" placeholder="Équipement levage"/></td>
                                <td><input type="text" name="toolsHeight" placeholder="Équipement hauteur"/></td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Section 4: Execution Details -->
                    <div class="section-header">
                        <i class='bx bx-group section-icon'></i>
                        <h2 class="section-title">Exécution</h2>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <i class='bx bx-user-check label-icon'></i>
                                Responsable d'exécution <span class="required">*</span>
                            </label>
                            <select name="executionResponsible" required>
                                <option value="">Sélectionner un responsable</option>
                                <option value="jean-dupont">Jean Dupont</option>
                                <option value="marie-martin">Marie Martin</option>
                                <option value="pierre-durand">Pierre Durand</option>
                                <option value="autre">Autre</option>
                            </select>
                        </div>
                    </div>

                    <!-- Section 5: Initiator Information -->
                    <div class="section-header">
                        <i class='bx bx-user section-icon'></i>
                        <h2 class="section-title">L'initiateur de permis</h2>
                    </div>

                    <div class="form-row-4">
                        <div class="form-group">
                            <label>
                                <i class='bx bx-user label-icon'></i>
                                Nom
                            </label>
                            <input type="text" value="John Doe" readonly/>
                        </div>
                        <div class="form-group">
                            <label>
                                <i class='bx bx-briefcase label-icon'></i>
                                Fonction
                            </label>
                            <input type="text" value="Initiateur" readonly/>
                        </div>
                        <div class="form-group">
                            <label>
                                <i class='bx bx-calendar label-icon'></i>
                                Date <span class="required">*</span>
                            </label>
                            <input type="date" name="initiationDate" required/>
                        </div>
                        <div class="form-group">
                            <label>
                                <i class='bx bx-time label-icon'></i>
                                Heure <span class="required">*</span>
                            </label>
                            <input type="time" name="initiationTime" required/>
                        </div>
                    </div>

                    <!-- Submit Message -->
                    <div id="submitMessage" class="hidden"></div>

                    <!-- Submit Buttons -->
                    <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 32px; padding-top: 24px; border-top: 1px solid var(--gray-200);">
                        <button type="button" class="btn btn-secondary" id="draftBtn">
                            <i class='bx bx-edit-alt btn-icon'></i>
                            Enregistrer comme brouillon
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class='bx bx-send btn-icon'></i>
                            Soumettre la demande
                        </button>
                    </div>
                </form>
            </section>
            
            <!-- Users Section -->
            <section id="users" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Gestion des Utilisateurs</h1>
                        <p class="content-subtitle">Gérez les utilisateurs du système</p>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <input type="text" id="searchInput" placeholder="Rechercher un utilisateur..." onkeyup="searchTable()">
                    <button onclick="searchTable()">Rechercher</button>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>Nom</th>
                            <th>Email</th>
                            <th>Fonction</th>
                            <th>Rôle</th>
                            <th>Date d'inscription</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody id="userTableBody">
                        <tr data-email="<EMAIL>" data-full-name="Jean Dupont" data-username="jean.dupont" data-function="Développeur" data-role="Administrateur">
                            <td>Jean Dupont</td>
                            <td><EMAIL></td>
                            <td>Développeur</td>
                            <td>Administrateur</td>
                            <td>2023-01-15</td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" data-email="<EMAIL>" onclick="viewUser(this)">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-edit" data-email="<EMAIL>" onclick="editUser(this)">
                                        <i class='bx bx-edit'></i>
                                        Modifier
                                    </button>
                                    <button class="btn-table btn-delete" data-email="<EMAIL>" onclick="deleteUser('<EMAIL>')">
                                        <i class='bx bx-trash'></i>
                                        Supprimer
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-email="<EMAIL>" data-full-name="Marie Martin" data-username="marie.martin" data-function="Designer" data-role="Validateur">
                            <td>Marie Martin</td>
                            <td><EMAIL></td>
                            <td>Designer</td>
                            <td>Validateur</td>
                            <td>2023-02-20</td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" data-email="<EMAIL>" onclick="viewUser(this)">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-edit" data-email="<EMAIL>" onclick="editUser(this)">
                                        <i class='bx bx-edit'></i>
                                        Modifier
                                    </button>
                                    <button class="btn-table btn-delete" data-email="<EMAIL>" onclick="deleteUser('<EMAIL>')">
                                        <i class='bx bx-trash'></i>
                                        Supprimer
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-email="<EMAIL>" data-full-name="Pierre Durand" data-username="pierre.durand" data-function="Manager" data-role="Administrateur">
                            <td>Pierre Durand</td>
                            <td><EMAIL></td>
                            <td>Manager</td>
                            <td>Administrateur</td>
                            <td>2022-11-05</td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" data-email="<EMAIL>" onclick="viewUser(this)">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-edit" data-email="<EMAIL>" onclick="editUser(this)">
                                        <i class='bx bx-edit'></i>
                                        Modifier
                                    </button>
                                    <button class="btn-table btn-delete" data-email="<EMAIL>" onclick="deleteUser('<EMAIL>')">
                                        <i class='bx bx-trash'></i>
                                        Supprimer
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- Add User Section -->
            <section id="add-user" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Ajouter un utilisateur</h1>
                        <p class="content-subtitle">Créez un nouveau compte utilisateur</p>
                    </div>
                </div>
                <form id="addUserForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <i class='bx bx-user label-icon'></i>
                                Nom Complet <span class="required">*</span>
                            </label>
                            <input type="text" name="fullName" required placeholder="Entrez le nom complet"/>
                        </div>
                        <div class="form-group">
                            <label>
                                <i class='bx bx-id-card label-icon'></i>
                                Nom d'utilisateur <span class="required">*</span>
                            </label>
                            <input type="text" name="username" required placeholder="Entrez le nom d'utilisateur"/>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <i class='bx bx-envelope label-icon'></i>
                                Email <span class="required">*</span>
                            </label>
                            <input type="email" name="email" required placeholder="Entrez l'adresse email"/>
                        </div>
                        <div class="form-group">
                            <label>
                                <i class='bx bx-briefcase label-icon'></i>
                                Fonction <span class="required">*</span>
                            </label>
                            <input type="text" name="function" required placeholder="Entrez la fonction"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <i class='bx bx-lock label-icon'></i>
                            Mot de passe <span class="required">*</span>
                        </label>
                        <input type="password" name="password" required placeholder="Entrez le mot de passe"/>
                    </div>
                    <div class="form-group">
                        <label>
                            <i class='bx bx-shield label-icon'></i>
                            Rôles <span class="required">*</span>
                        </label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="role-coordinateur" name="roles" value="coordinateur">
                                <label for="role-coordinateur">Coordinateur</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="role-hse" name="roles" value="responsable-hse">
                                <label for="role-hse">Responsable HSE</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="role-autorite" name="roles" value="autorite-zone">
                                <label for="role-autorite">Autorité de Zone</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="role-execution" name="roles" value="responsable-execution">
                                <label for="role-execution">Responsable d'Exécution</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="role-rep-az" name="roles" value="representant-az">
                                <label for="role-rep-az">Représentant AZ</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="role-rep-hse" name="roles" value="representant-hse">
                                <label for="role-rep-hse">Représentant HSE</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Zone Selection (appears only when Autorité de Zone is selected) -->
                    <div class="form-group" id="zone-selection" style="display: none;">
                        <label>
                            <i class='bx bx-map label-icon'></i>
                            Zone de responsabilité <span class="required">*</span>
                        </label>
                        <select id="user-zone" name="zone" required>
                            <option value="">Sélectionner une zone</option>
                            <!-- Options will be populated dynamically from zones management -->
                        </select>
                    </div>

                    <!-- Submit Message -->
                    <div id="submitMessage" class="hidden"></div>

                    <div style="display: flex; justify-content: flex-end; margin-top: 32px; padding-top: 24px; border-top: 1px solid var(--gray-200);">
                        <button type="button" class="btn btn-outline" onclick="showSection('users')">Annuler</button>
<button type="submit" class="btn btn-primary" id="addUserBtn" style="margin-left: 12px;">
    <i class='bx bx-user-plus btn-icon'></i>
    Créer l'utilisateur
                    </div>
                </form>
            </section>

            <!-- Profile Section -->
            <section id="profile" class="hidden">
                <!-- Profile Header -->
                <div class="profile-header">
                    <div class="profile-info">
                        <div class="profile-avatar">AD</div>
                        <div class="profile-details">
                            <h1 class="profile-name">Admin User</h1>
                            <div class="profile-username">@adminuser</div>
                            <span class="profile-role">
                                <i class='bx bx-shield'></i>
                                Administrateur
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Profile Form -->
                <div class="profile-form">
                    <h2 class="form-section-title">
                        <i class='bx bx-user'></i>
                        Informations du Profil
                    </h2>

                    <form id="profileForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-user label-icon'></i>
                                    Nom complet
                                </label>
                                <input type="text" value="Admin User" readonly/>
                            </div>
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-user label-icon'></i>
                                    Nom d'utilisateur
                                </label>
                                <input type="text" value="adminuser" readonly/>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-briefcase label-icon'></i>
                                    Fonction
                                </label>
                                <input type="text" value="Administrateur" readonly/>
                            </div>
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-envelope label-icon'></i>
                                    Email <span class="required">*</span>
                                </label>
                                <input type="email" id="userEmail" value="<EMAIL>" required placeholder="Entrez votre adresse email"/>
                            </div>
                        </div>

                        <!-- Success/Error Message -->
                        <div id="profileMessage" class="hidden"></div>

                        <!-- Submit Button -->
                        <div style="display: flex; justify-content: flex-end; margin-top: 24px;">
                            <button type="submit" class="btn btn-primary" id="profileSubmitBtn">
                                <i class='bx bx-save btn-icon'></i>
                                Mettre à jour l'email
                            </button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Zones Management Section -->
            <section id="zones" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Gestion des Zones de Travail</h1>
                        <p class="content-subtitle">Gérez les zones de travail et attribuez-les aux centres d'opération</p>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <input type="text" id="searchZones" placeholder="Rechercher une zone..." onkeyup="searchZones()">
                    <button onclick="searchZones()">Rechercher</button>
                </div>

                <!-- Action Buttons -->
                <div class="action-bar">
                    <button class="btn btn-primary" onclick="showSection('add-zone')">
                        <i class='bx bx-plus'></i>
                        Ajouter une zone
                    </button>
                </div>

                <table id="zonesTable">
                    <thead>
                        <tr>
                            <th>Nom de la zone</th>
                            <th>Classification de zone</th>
                            <th>Description</th>
                            <th>Date de création</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-zone-id="1" data-zone-name="Zone A - Production" data-classification="Zone 1">
                            <td>Zone A - Production</td>
                            <td><span class="status-badge status-zone1">Zone 1</span></td>
                            <td>Zone de production principale avec équipements industriels</td>
                            <td>2023-01-15</td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="viewZone(this)" data-zone-id="1">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-edit" onclick="editZone(this)" data-zone-id="1">
                                        <i class='bx bx-edit'></i>
                                        Modifier
                                    </button>
                                    <button class="btn-table btn-delete" onclick="deleteZone('1', 'Zone A - Production')">
                                        <i class='bx bx-trash'></i>
                                        Supprimer
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-zone-id="2" data-zone-name="Zone B - Stockage" data-classification="Zone 2">
                            <td>Zone B - Stockage</td>
                            <td><span class="status-badge status-zone2">Zone 2</span></td>
                            <td>Zone de stockage des matières premières et produits finis</td>
                            <td>2023-02-20</td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="viewZone(this)" data-zone-id="2">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-edit" onclick="editZone(this)" data-zone-id="2">
                                        <i class='bx bx-edit'></i>
                                        Modifier
                                    </button>
                                    <button class="btn-table btn-delete" onclick="deleteZone('2', 'Zone B - Stockage')">
                                        <i class='bx bx-trash'></i>
                                        Supprimer
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-zone-id="3" data-zone-name="Zone C - Laboratoire" data-classification="Zone 0">
                            <td>Zone C - Laboratoire</td>
                            <td><span class="status-badge status-zone0">Zone 0</span></td>
                            <td>Laboratoire d'analyse et de contrôle qualité</td>
                            <td>2022-11-05</td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="viewZone(this)" data-zone-id="3">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-edit" onclick="editZone(this)" data-zone-id="3">
                                        <i class='bx bx-edit'></i>
                                        Modifier
                                    </button>
                                    <button class="btn-table btn-delete" onclick="deleteZone('3', 'Zone C - Laboratoire')">
                                        <i class='bx bx-trash'></i>
                                        Supprimer
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-zone-id="4" data-zone-name="Zone D - Maintenance" data-classification="Zone 1">
                            <td>Zone D - Maintenance</td>
                            <td><span class="status-badge status-zone1">Zone 1</span></td>
                            <td>Atelier de maintenance et réparation des équipements</td>
                            <td>2023-03-10</td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="viewZone(this)" data-zone-id="4">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-edit" onclick="editZone(this)" data-zone-id="4">
                                        <i class='bx bx-edit'></i>
                                        Modifier
                                    </button>
                                    <button class="btn-table btn-delete" onclick="deleteZone('4', 'Zone D - Maintenance')">
                                        <i class='bx bx-trash'></i>
                                        Supprimer
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-zone-id="5" data-zone-name="Zone E - Bureaux" data-classification="Zone 2">
                            <td>Zone E - Bureaux</td>
                            <td><span class="status-badge status-zone2">Zone 2</span></td>
                            <td>Espaces administratifs et bureaux du personnel</td>
                            <td>2023-01-08</td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="viewZone(this)" data-zone-id="5">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-edit" onclick="editZone(this)" data-zone-id="5">
                                        <i class='bx bx-edit'></i>
                                        Modifier
                                    </button>
                                    <button class="btn-table btn-delete" onclick="deleteZone('5', 'Zone E - Bureaux')">
                                        <i class='bx bx-trash'></i>
                                        Supprimer
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- Add Zone Section -->
            <section id="add-zone" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Ajouter une Zone de Travail</h1>
                        <p class="content-subtitle">Créez une nouvelle zone de travail</p>
                    </div>
                </div>

                <form id="addZoneForm" onsubmit="addZone(event)">
                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <i class='bx bx-map label-icon'></i>
                                Nom de la zone <span class="required">*</span>
                            </label>
                            <input type="text" id="zoneName" required placeholder="Ex: Zone A - Production"/>
                        </div>
                        <div class="form-group">
                            <label>
                                <i class='bx bx-shield label-icon'></i>
                                Classification de zone <span class="required">*</span>
                            </label>
                            <select id="zoneClassification" required>
                                <option value="">Sélectionner la classification</option>
                                <option value="Zone 0">Zone 0 - Présence permanente d'atmosphère explosive</option>
                                <option value="Zone 1">Zone 1 - Présence occasionnelle d'atmosphère explosive</option>
                                <option value="Zone 2">Zone 2 - Présence rare d'atmosphère explosive</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>
                            <i class='bx bx-edit label-icon'></i>
                            Description de la zone <span class="required">*</span>
                        </label>
                        <textarea id="zoneDescription" required placeholder="Décrivez la zone, ses équipements, ses activités..." rows="4"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <i class='bx bx-user label-icon'></i>
                                Responsable de zone
                            </label>
                            <select id="zoneResponsible">
                                <option value="">Sélectionner un responsable</option>
                                <option value="ahmed.benali">Ahmed Benali</option>
                                <option value="fatima.khelil">Fatima Khelil</option>
                                <option value="mohamed.saidi">Mohamed Saidi</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>
                                <i class='bx bx-phone label-icon'></i>
                                Contact d'urgence
                            </label>
                            <input type="tel" id="emergencyContact" placeholder="Numéro de téléphone d'urgence"/>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="showSection('zones')">
                            <i class='bx bx-arrow-back'></i>
                            Retour à la liste
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class='bx bx-plus'></i>
                            Ajouter la zone
                        </button>
                    </div>
                </form>
            </section>

            <!-- View User Popup -->
            <div class="popup" id="viewUserPopup">
                <div class="popup-inner">
                    <button class="popup-close" onclick="closePopup('viewUserPopup')"><i class='bx bx-x'></i></button>
                    <h2 class="popup-title">Informations de l'utilisateur</h2>
                    <div class="popup-content">
                        <p><strong>Nom Complet:</strong> <span id="viewFullName"></span></p>
                        <p><strong>Nom d'utilisateur:</strong> <span id="viewUsername"></span></p>
                        <p><strong>Email:</strong> <span id="viewEmail"></span></p>
                        <p><strong>Fonction:</strong> <span id="viewFunction"></span></p>
                        <p><strong>Rôle:</strong> <span id="viewRole"></span></p>
                    </div>
                </div>
            </div>

            <!-- Edit User Popup -->
            <div class="popup" id="editUserPopup">
                <div class="popup-inner">
                    <button class="popup-close" onclick="closePopup('editUserPopup')"><i class='bx bx-x'></i></button>
                    <h2 class="popup-title">Modifier l'utilisateur</h2>
                    <form class="popup-form" id="editUserForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="editFullName"><i class='bx bx-user'></i>Nom Complet</label>
                                <input type="text" id="editFullName" name="fullName">
                            </div>
                            <div class="form-group">
                                <label for="editUsername"><i class='bx bx-id-card'></i>Nom d'utilisateur</label>
                                <input type="text" id="editUsername" name="username">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="editEmail"><i class='bx bx-envelope'></i>Email</label>
                                <input type="email" id="editEmail" name="email">
                            </div>
                            <div class="form-group">
                                <label for="editFunction"><i class='bx bx-briefcase'></i>Fonction</label>
                                <input type="text" id="editFunction" name="function">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="editRole"><i class='bx bx-shield'></i>Rôle</label>
                            <select id="editRole" name="role">
                                <option value="Initiateur">Initiateur</option>
                                <option value="coordinateur">Coordinateur</option>
                                <option value="responsable-hse">Responsable HSE</option>
                                <option value="autorite-zone">Autorité de Zone</option>
                                <option value="responsable-execution">Responsable d'Exécution</option>
                                <option value="representant-az">Représentant AZ</option>
                                <option value="representant-hse">Représentant HSE</option>
                            </select>
                        </div>
                        
                        <!-- Zone Selection for Edit User -->
                        <div class="form-group" id="edit-zone-selection" style="display: none;">
                            <label for="editZone"><i class='bx bx-map'></i>Zone de responsabilité</label>
                            <select id="editZone" name="zone">
                                <option value="">Sélectionner une zone</option>
                                <!-- Options will be populated dynamically from zones management -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="editPassword"><i class='bx bx-lock'></i>Mot de passe</label>
                            <input type="password" id="editPassword" name="password" placeholder="Laissez vide pour ne pas changer">
                        </div>
                        <button type="submit" class="btn btn-primary" onclick="submitEditForm(event)">Enregistrer</button>
                    </form>
                </div>
            </div>

            <!-- Edit Zone Popup -->
            <div class="popup" id="editZonePopup">
                <div class="popup-inner">
                    <button class="popup-close" onclick="closePopup('editZonePopup')"><i class='bx bx-x'></i></button>
                    <h2 class="popup-title">Modifier la zone</h2>
                    <form class="popup-form" id="editZoneForm" onsubmit="submitEditZone(event)">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="editZoneName"><i class='bx bx-map'></i>Nom de la zone</label>
                                <input type="text" id="editZoneName" name="zoneName" required>
                            </div>
                            <div class="form-group">
                                <label for="editZoneClassification"><i class='bx bx-shield'></i>Classification de zone</label>
                                <select id="editZoneClassification" name="classification" required>
                                    <option value="">Sélectionner la classification</option>
                                    <option value="Zone 0">Zone 0</option>
                                    <option value="Zone 1">Zone 1</option>
                                    <option value="Zone 2">Zone 2</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="editZoneStatus"><i class='bx bx-check-circle'></i>Statut</label>
                                <select id="editZoneStatus" name="status" required>
                                    <option value="Actif">Actif</option>
                                    <option value="Inactif">Inactif</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <!-- Empty div for layout balance -->
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class='bx bx-save'></i>
                            Enregistrer les modifications
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Toggle sidebar on mobile
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.querySelector('.sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');

            menuToggle.addEventListener('click', function () {
                sidebar.classList.toggle('active');
            });

            // Toggle sidebar collapse/expand
            sidebarToggle.addEventListener('click', function () {
                sidebar.classList.toggle('collapsed');
                const icon = sidebarToggle.querySelector('i');
                if (sidebar.classList.contains('collapsed')) {
                    icon.classList.remove('bx-chevron-left');
                    icon.classList.add('bx-chevron-right');
                } else {
                    icon.classList.remove('bx-chevron-right');
                    icon.classList.add('bx-chevron-left');
                }
            });

            // User dropdown toggle
            const userDropdownTrigger = document.getElementById('userDropdownTrigger');
            const userDropdown = document.getElementById('userDropdown');

            userDropdownTrigger.addEventListener('click', function (e) {
                e.stopPropagation();
                userDropdown.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function (e) {
                if (!userDropdown.contains(e.target) && e.target !== userDropdownTrigger && !userDropdownTrigger.contains(e.target)) {
                    userDropdown.classList.remove('active');
                }
            });

            // Notification button action
            const notificationBtns = document.querySelectorAll('.action-btn-lg .bx-bell');
            notificationBtns.forEach(btn => {
                btn.addEventListener('click', function () {
                    alert('Vous avez 1 nouvelle notification');
                });
            });

            // Simulate enterprise-level resize handling
            function handleResize() {
                if (window.innerWidth < 992) {
                    sidebar.classList.remove('collapsed');
                    sidebarToggle.style.display = 'none';
                    menuToggle.style.display = 'flex';
                } else {
                    menuToggle.style.display = 'none';
                    sidebarToggle.style.display = 'flex';
                }
            }

            window.addEventListener('resize', handleResize);
            handleResize();
        });

        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll("section").forEach(sec => sec.classList.add('hidden'));
            // Show selected section
            document.getElementById(sectionId).classList.remove('hidden');
            // Update page title
            const titles = {
                'dashboard': 'TABLEAU DE BORD',
                'users': 'GESTION DES UTILISATEURS',
                'add-user': 'AJOUTER UN UTILISATEUR',
                'zones': 'GESTION DES ZONES',
                'profile': 'MON PROFIL'
            };
            document.getElementById("page-title").textContent = titles[sectionId];

            // Update active link in sidebar
            document.querySelectorAll(".menu-link").forEach(link => {
                link.classList.remove("active");
            });

            // Find the clicked element or its parent with class 'menu-link'
            let target = event.target;
            while (target && !target.classList.contains('menu-link')) {
                target = target.parentNode;
            }

            if (target && target.classList.contains('menu-link')) {
                target.classList.add("active");
            }
        }

        function logout() {
            if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                alert('Déconnexion en cours...');
                // Add your logout logic here
                window.location.href = 'login.html'; // Redirect to login page
            }
        }

        function addUser() {
            showSection('add-user');
        }

        function viewUser(element) {
            const email = element.dataset.email;

            // Find the row with the matching email
            const row = document.querySelector(`tr[data-email="${email}"]`);

            if (row) {
                const fullName = row.dataset.fullName;
                const username = row.dataset.username;
                const fonction = row.dataset.function;
                const role = row.dataset.role;

                document.getElementById('viewFullName').textContent = fullName;
                document.getElementById('viewUsername').textContent = username;
                document.getElementById('viewEmail').textContent = email;
                document.getElementById('viewFunction').textContent = fonction;
                document.getElementById('viewRole').textContent = role;

                openPopup('viewUserPopup');
            } else {
                alert('Utilisateur non trouvé.');
            }
        }

        function editUser(element) {
            const email = element.dataset.email;

            // Find the row with the matching email
            const row = document.querySelector(`tr[data-email="${email}"]`);

            if (row) {
                const fullName = row.dataset.fullName;
                const username = row.dataset.username;
                const fonction = row.dataset.function;
                const role = row.dataset.role;

                document.getElementById('editFullName').value = fullName;
                document.getElementById('editUsername').value = username;
                document.getElementById('editEmail').value = email;
                document.getElementById('editFunction').value = fonction;
                document.getElementById('editRole').value = role;
                
                // Handle zone selection visibility for Autorité de Zone
                const editZoneSelection = document.getElementById('edit-zone-selection');
                const editZone = document.getElementById('editZone');
                
                if (role === 'autorite-zone') {
                    editZoneSelection.style.display = 'block';
                    // If the user has a zone defined in their data, set it
                    if (row.dataset.zone) {
                        editZone.value = row.dataset.zone;
                    }
                } else {
                    editZoneSelection.style.display = 'none';
                }

                // Store the email in a data attribute of the form for later use
                document.getElementById('editUserForm').dataset.email = email;

                openPopup('editUserPopup');
            } else {
                alert('Utilisateur non trouvé.');
            }
        }

        function deleteUser(email) {
            if (confirm('Êtes-vous sûr de vouloir supprimer l\'utilisateur avec l\'email : ' + email + ' ?')) {
                // Find the row with the matching email
                const row = document.querySelector(`tr[data-email="${email}"]`);

                if (row) {
                    row.remove(); // Remove the row from the table
                    alert('Utilisateur supprimé avec succès.');
                } else {
                    alert('Utilisateur non trouvé.');
                }
            }
        }

        function searchTable() {
            let input, filter, table, tr, td, i, txtValue;
            input = document.getElementById("searchInput");
            filter = input.value.toUpperCase();
            table = document.getElementById("users").querySelector("table");
            tr = table.getElementsByTagName("tr");

            for (i = 1; i < tr.length; i++) { // Start from 1 to skip the header row
                tr[i].style.display = "none"; // Hide the row initially
                td = tr[i].getElementsByTagName("td");
                for (let j = 0; j < td.length; j++) {
                    if (td[j]) {
                        txtValue = td[j].textContent || td[j].innerText;
                        if (txtValue.toUpperCase().indexOf(filter) > -1) {
                            tr[i].style.display = ""; // Display the row if a match is found
                            break; // No need to check other columns in the same row
                        }
                    }
                }
            }
        }

        function openPopup(popupId) {
            document.getElementById(popupId).classList.add('active');
        }

        function closePopup(popupId) {
            document.getElementById(popupId).classList.remove('active');
        }

        function submitEditForm(event) {
            event.preventDefault(); // Prevent the default form submission

            // Get the form values
            const fullName = document.getElementById('editFullName').value;
            const username = document.getElementById('editUsername').value;
            const email = document.getElementById('editEmail').value;
            const fonction = document.getElementById('editFunction').value;
            const role = document.getElementById('editRole').value;
            const password = document.getElementById('editPassword').value; // Password can be empty if not changed
            const zone = document.getElementById('editZone').value; // Zone will be used if role is Autorité de Zone

            // Get the email of the user being edited from the form's data attribute
            const userEmail = document.getElementById('editUserForm').dataset.email;

            // Find the row with the matching email
            const row = document.querySelector(`tr[data-email="${userEmail}"]`);

            if (row) {
                // Update the row's data attributes and content
                row.dataset.fullName = fullName;
                row.dataset.username = username;
                row.dataset.function = fonction;
                row.dataset.role = role;
                
                // Store zone information if the role is Autorité de Zone
                if (role === 'autorite-zone') {
                    row.dataset.zone = zone;
                } else {
                    delete row.dataset.zone;
                }

                // Update the table cells with the new values
                const cells = row.getElementsByTagName('td');
                cells[0].textContent = fullName;
                cells[1].textContent = email;
                cells[2].textContent = fonction;
                cells[3].textContent = role;

                // Close the popup after submission
                closePopup('editUserPopup');
                alert('Utilisateur mis à jour avec succès.');
            } else {
                alert('Utilisateur non trouvé.');
            }
        }

        // Handle zone selection visibility based on Autorité de Zone role
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize charts for dashboard
            initCharts();
            
            // For Add User form
            const autoriteCheckbox = document.getElementById('role-autorite');
            const zoneSelection = document.getElementById('zone-selection');
            const zoneSelect = document.getElementById('user-zone');
            
            // Show/hide zone selection based on checkbox state
            autoriteCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    zoneSelection.style.display = 'block';
                    zoneSelect.setAttribute('required', 'required');
                } else {
                    zoneSelection.style.display = 'none';
                    zoneSelect.removeAttribute('required');
                }
            });

            // Validate form submission
            const addUserForm = document.getElementById('addUserForm');
            addUserForm.addEventListener('submit', function(e) {
                if (autoriteCheckbox.checked && zoneSelect.value === '') {
                    e.preventDefault();
                    alert('Veuillez sélectionner une zone de responsabilité pour l\'Autorité de Zone.');
                }
            });
            
            // For Edit User form
            const editRole = document.getElementById('editRole');
            const editZoneSelection = document.getElementById('edit-zone-selection');
            const editZone = document.getElementById('editZone');
            
            // Show/hide zone selection based on role dropdown
            editRole.addEventListener('change', function() {
                if (this.value === 'autorite-zone') {
                    editZoneSelection.style.display = 'block';
                    editZone.setAttribute('required', 'required');
                } else {
                    editZoneSelection.style.display = 'none';
                    editZone.removeAttribute('required');
                }
            });
            
            // Initialize the edit form handling
            const editUserForm = document.getElementById('editUserForm');
            editUserForm.addEventListener('submit', function(e) {
                if (editRole.value === 'autorite-zone' && editZone.value === '') {
                    e.preventDefault();
                    alert('Veuillez sélectionner une zone de responsabilité pour l\'Autorité de Zone.');
                }
            });
        });
        
        // Dashboard Functions
        function initCharts() {
            // Permits by type chart
            const permitsCtx = document.getElementById('permitsChart').getContext('2d');
            const permitsChart = new Chart(permitsCtx, {
                type: 'pie',
                data: {
                    labels: ['Travaux à chaud', 'Travaux électriques', 'Travaux en hauteur', 'Espace confiné', 'Excavation'],
                    datasets: [{
                        data: [42, 35, 28, 20, 31],
                        backgroundColor: [
                            '#2563eb',
                            '#7c3aed',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            
            // Permits by zone chart
            const zonesCtx = document.getElementById('zonesChart').getContext('2d');
            const zonesChart = new Chart(zonesCtx, {
                type: 'bar',
                data: {
                    labels: ['Zone A', 'Zone B', 'Zone C', 'Zone D', 'Zone E'],
                    datasets: [{
                        label: 'Nombre de permis',
                        data: [48, 37, 26, 29, 16],
                        backgroundColor: '#2563eb',
                        borderRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // Users by role chart
            const usersCtx = document.getElementById('usersChart').getContext('2d');
            const usersChart = new Chart(usersCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Administrateur', 'Coordinateur', 'Responsable HSE', 'Autorité de Zone', 'Responsable d\'Exécution'],
                    datasets: [{
                        data: [5, 12, 18, 25, 18],
                        backgroundColor: [
                            '#2563eb',
                            '#7c3aed',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
        
        function refreshDashboard() {
            // In a real application, this would fetch fresh data from the backend
            // For now, we'll just show a success message
            alert('Tableau de bord mis à jour !');
            
            // Reinitialize charts with new data (in a real app)
            initCharts();
        }

        // Zones Management Functions
        function viewZone(element) {
            const zoneId = element.getAttribute('data-zone-id');
            const row = document.querySelector(`tr[data-zone-id="${zoneId}"]`);
            
            if (row) {
                const zoneName = row.cells[0].textContent;
                const classification = row.cells[1].textContent;
                const description = row.cells[2].textContent;
                const dateCreation = row.cells[3].textContent;
                
                alert(`Détails de la zone:\n\nNom: ${zoneName}\nClassification: ${classification}\nDescription: ${description}\nDate de création: ${dateCreation}`);
            }
        }
        function addZone(event) {
            event.preventDefault();
            
            const zoneName = document.getElementById('zoneName').value;
            const zoneClassification = document.getElementById('zoneClassification').value;
            const zoneStatus = document.getElementById('zoneStatus').value;
            
            // Generate a unique ID for the zone
            const zoneId = Date.now().toString();
            
            // Create new table row
            const tbody = document.querySelector('#zonesTable tbody');
            const newRow = document.createElement('tr');
            newRow.setAttribute('data-zone-id', zoneId);
            newRow.setAttribute('data-zone-name', zoneName);
            newRow.setAttribute('data-classification', zoneClassification);
            newRow.setAttribute('data-status', zoneStatus);
            
            const classificationClass = getClassificationClass(zoneClassification);
            const statusClass = zoneStatus === 'Actif' ? 'status-active' : 'status-inactive';
            
            newRow.innerHTML = `
                <td>${zoneName}</td>
                <td><span class="status-badge ${classificationClass}">${zoneClassification}</span></td>
                <td><span class="status-badge ${statusClass}">${zoneStatus}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon btn-edit" onclick="editZone(this)" data-zone-id="${zoneId}" title="Modifier">
                            <i class='bx bx-edit'></i>
                        </button>
                        <button class="btn-icon btn-delete" onclick="deleteZone('${zoneId}', '${zoneName}')" title="Supprimer">
                            <i class='bx bx-trash'></i>
                        </button>
                    </div>
                </td>
            `;
            
            tbody.appendChild(newRow);
            
            // Update zone options in user forms
            updateZoneOptions();
            
            // Hide form and show list
            hideAddZoneForm();
            
            alert('Zone ajoutée avec succès !');
        }

        function editZone(element) {
            const zoneId = element.getAttribute('data-zone-id');
            const row = document.querySelector(`tr[data-zone-id="${zoneId}"]`);
            
            if (row) {
                const zoneName = row.getAttribute('data-zone-name');
                const classification = row.getAttribute('data-classification');
                const status = row.getAttribute('data-status');
                
                // Populate edit form
                document.getElementById('editZoneName').value = zoneName;
                document.getElementById('editZoneClassification').value = classification;
                document.getElementById('editZoneStatus').value = status;
                
                // Store zone ID for later use
                document.getElementById('editZoneForm').setAttribute('data-zone-id', zoneId);
                
                openPopup('editZonePopup');
            }
        }

        function submitEditZone(event) {
            event.preventDefault();
            
            const zoneId = document.getElementById('editZoneForm').getAttribute('data-zone-id');
            const row = document.querySelector(`tr[data-zone-id="${zoneId}"]`);
            
            if (row) {
                const zoneName = document.getElementById('editZoneName').value;
                const classification = document.getElementById('editZoneClassification').value;
                const status = document.getElementById('editZoneStatus').value;
                
                // Update row attributes
                row.setAttribute('data-zone-name', zoneName);
                row.setAttribute('data-classification', classification);
                row.setAttribute('data-status', status);
                
                // Update row content
                const cells = row.getElementsByTagName('td');
                const classificationClass = getClassificationClass(classification);
                const statusClass = status === 'Actif' ? 'status-active' : 'status-inactive';
                
                cells[0].textContent = zoneName;
                cells[1].innerHTML = `<span class="status-badge ${classificationClass}">${classification}</span>`;
                cells[2].innerHTML = `<span class="status-badge ${statusClass}">${status}</span>`;
                
                // Update zone options in user forms
                updateZoneOptions();
                
                closePopup('editZonePopup');
                alert('Zone modifiée avec succès !');
            }
        }

        function deleteZone(zoneId, zoneName) {
            if (confirm(`Êtes-vous sûr de vouloir supprimer la zone "${zoneName}" ?`)) {
                const row = document.querySelector(`tr[data-zone-id="${zoneId}"]`);
                if (row) {
                    row.remove();
                    updateZoneOptions();
                    alert('Zone supprimée avec succès !');
                }
            }
        }

        function searchZones() {
            const input = document.getElementById('searchZones');
            const filter = input.value.toUpperCase();
            const table = document.getElementById('zonesTable');
            const tr = table.getElementsByTagName('tr');
            
            for (let i = 1; i < tr.length; i++) {
                tr[i].style.display = 'none';
                const td = tr[i].getElementsByTagName('td');
                for (let j = 0; j < td.length; j++) {
                    if (td[j]) {
                        const txtValue = td[j].textContent || td[j].innerText;
                        if (txtValue.toUpperCase().indexOf(filter) > -1) {
                            tr[i].style.display = '';
                            break;
                        }
                    }
                }
            }
        }

        function updateZoneOptions() {
            // Get all active zones
            const zones = [];
            const rows = document.querySelectorAll('#zonesTable tbody tr');
            
            rows.forEach(row => {
                const status = row.getAttribute('data-status');
                if (status === 'Actif') {
                    const zoneName = row.getAttribute('data-zone-name');
                    zones.push(zoneName);
                }
            });
            
            // Update Add User form zone options
            const addUserZoneSelect = document.getElementById('user-zone');
            if (addUserZoneSelect) {
                addUserZoneSelect.innerHTML = '<option value="">Sélectionner une zone</option>';
                zones.forEach(zone => {
                    const option = document.createElement('option');
                    option.value = zone;
                    option.textContent = zone;
                    addUserZoneSelect.appendChild(option);
                });
            }
            
            // Update Edit User form zone options
            const editUserZoneSelect = document.getElementById('editZone');
            if (editUserZoneSelect) {
                const currentValue = editUserZoneSelect.value;
                editUserZoneSelect.innerHTML = '<option value="">Sélectionner une zone</option>';
                zones.forEach(zone => {
                    const option = document.createElement('option');
                    option.value = zone;
                    option.textContent = zone;
                    if (zone === currentValue) {
                        option.selected = true;
                    }
                    editUserZoneSelect.appendChild(option);
                });
            }
        }

        function getClassificationClass(classification) {
            const classes = {
                'Zone 0': 'status-zone0',
                'Zone 1': 'status-zone1',
                'Zone 2': 'status-zone2'
            };
            return classes[classification] || 'status-zone2';
        }

        // Initialize zone options when page loads
        document.addEventListener('DOMContentLoaded', function() {
            updateZoneOptions();
        });
    </script>
</body>
</html>